{"version": 3, "sources": ["../../scheduler/cjs/scheduler.development.js", "../../scheduler/index.js"], "sourcesContent": ["/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function performWorkUntilDeadline() {\n      needsPaint = !1;\n      if (isMessageLoopRunning) {\n        var currentTime = exports.unstable_now();\n        startTime = currentTime;\n        var hasMoreWork = !0;\n        try {\n          a: {\n            isHostCallbackScheduled = !1;\n            isHostTimeoutScheduled &&\n              ((isHostTimeoutScheduled = !1),\n              localClearTimeout(taskTimeoutID),\n              (taskTimeoutID = -1));\n            isPerformingWork = !0;\n            var previousPriorityLevel = currentPriorityLevel;\n            try {\n              b: {\n                advanceTimers(currentTime);\n                for (\n                  currentTask = peek(taskQueue);\n                  null !== currentTask &&\n                  !(\n                    currentTask.expirationTime > currentTime &&\n                    shouldYieldToHost()\n                  );\n\n                ) {\n                  var callback = currentTask.callback;\n                  if (\"function\" === typeof callback) {\n                    currentTask.callback = null;\n                    currentPriorityLevel = currentTask.priorityLevel;\n                    var continuationCallback = callback(\n                      currentTask.expirationTime <= currentTime\n                    );\n                    currentTime = exports.unstable_now();\n                    if (\"function\" === typeof continuationCallback) {\n                      currentTask.callback = continuationCallback;\n                      advanceTimers(currentTime);\n                      hasMoreWork = !0;\n                      break b;\n                    }\n                    currentTask === peek(taskQueue) && pop(taskQueue);\n                    advanceTimers(currentTime);\n                  } else pop(taskQueue);\n                  currentTask = peek(taskQueue);\n                }\n                if (null !== currentTask) hasMoreWork = !0;\n                else {\n                  var firstTimer = peek(timerQueue);\n                  null !== firstTimer &&\n                    requestHostTimeout(\n                      handleTimeout,\n                      firstTimer.startTime - currentTime\n                    );\n                  hasMoreWork = !1;\n                }\n              }\n              break a;\n            } finally {\n              (currentTask = null),\n                (currentPriorityLevel = previousPriorityLevel),\n                (isPerformingWork = !1);\n            }\n            hasMoreWork = void 0;\n          }\n        } finally {\n          hasMoreWork\n            ? schedulePerformWorkUntilDeadline()\n            : (isMessageLoopRunning = !1);\n        }\n      }\n    }\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      a: for (; 0 < index; ) {\n        var parentIndex = (index - 1) >>> 1,\n          parent = heap[parentIndex];\n        if (0 < compare(parent, node))\n          (heap[parentIndex] = node),\n            (heap[index] = parent),\n            (index = parentIndex);\n        else break a;\n      }\n    }\n    function peek(heap) {\n      return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n      if (0 === heap.length) return null;\n      var first = heap[0],\n        last = heap.pop();\n      if (last !== first) {\n        heap[0] = last;\n        a: for (\n          var index = 0, length = heap.length, halfLength = length >>> 1;\n          index < halfLength;\n\n        ) {\n          var leftIndex = 2 * (index + 1) - 1,\n            left = heap[leftIndex],\n            rightIndex = leftIndex + 1,\n            right = heap[rightIndex];\n          if (0 > compare(left, last))\n            rightIndex < length && 0 > compare(right, left)\n              ? ((heap[index] = right),\n                (heap[rightIndex] = last),\n                (index = rightIndex))\n              : ((heap[index] = left),\n                (heap[leftIndex] = last),\n                (index = leftIndex));\n          else if (rightIndex < length && 0 > compare(right, last))\n            (heap[index] = right),\n              (heap[rightIndex] = last),\n              (index = rightIndex);\n          else break a;\n        }\n      }\n      return first;\n    }\n    function compare(a, b) {\n      var diff = a.sortIndex - b.sortIndex;\n      return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n      for (var timer = peek(timerQueue); null !== timer; ) {\n        if (null === timer.callback) pop(timerQueue);\n        else if (timer.startTime <= currentTime)\n          pop(timerQueue),\n            (timer.sortIndex = timer.expirationTime),\n            push(taskQueue, timer);\n        else break;\n        timer = peek(timerQueue);\n      }\n    }\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = !1;\n      advanceTimers(currentTime);\n      if (!isHostCallbackScheduled)\n        if (null !== peek(taskQueue))\n          (isHostCallbackScheduled = !0),\n            isMessageLoopRunning ||\n              ((isMessageLoopRunning = !0), schedulePerformWorkUntilDeadline());\n        else {\n          var firstTimer = peek(timerQueue);\n          null !== firstTimer &&\n            requestHostTimeout(\n              handleTimeout,\n              firstTimer.startTime - currentTime\n            );\n        }\n    }\n    function shouldYieldToHost() {\n      return needsPaint\n        ? !0\n        : exports.unstable_now() - startTime < frameInterval\n          ? !1\n          : !0;\n    }\n    function requestHostTimeout(callback, ms) {\n      taskTimeoutID = localSetTimeout(function () {\n        callback(exports.unstable_now());\n      }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\n      \"object\" === typeof performance &&\n      \"function\" === typeof performance.now\n    ) {\n      var localPerformance = performance;\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date,\n        initialTime = localDate.now();\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    }\n    var taskQueue = [],\n      timerQueue = [],\n      taskIdCounter = 1,\n      currentTask = null,\n      currentPriorityLevel = 3,\n      isPerformingWork = !1,\n      isHostCallbackScheduled = !1,\n      isHostTimeoutScheduled = !1,\n      needsPaint = !1,\n      localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null,\n      localClearTimeout =\n        \"function\" === typeof clearTimeout ? clearTimeout : null,\n      localSetImmediate =\n        \"undefined\" !== typeof setImmediate ? setImmediate : null,\n      isMessageLoopRunning = !1,\n      taskTimeoutID = -1,\n      frameInterval = 5,\n      startTime = -1;\n    if (\"function\" === typeof localSetImmediate)\n      var schedulePerformWorkUntilDeadline = function () {\n        localSetImmediate(performWorkUntilDeadline);\n      };\n    else if (\"undefined\" !== typeof MessageChannel) {\n      var channel = new MessageChannel(),\n        port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n      schedulePerformWorkUntilDeadline = function () {\n        port.postMessage(null);\n      };\n    } else\n      schedulePerformWorkUntilDeadline = function () {\n        localSetTimeout(performWorkUntilDeadline, 0);\n      };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function (task) {\n      task.callback = null;\n    };\n    exports.unstable_forceFrameRate = function (fps) {\n      0 > fps || 125 < fps\n        ? console.error(\n            \"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\"\n          )\n        : (frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5);\n    };\n    exports.unstable_getCurrentPriorityLevel = function () {\n      return currentPriorityLevel;\n    };\n    exports.unstable_next = function (eventHandler) {\n      switch (currentPriorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n          var priorityLevel = 3;\n          break;\n        default:\n          priorityLevel = currentPriorityLevel;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_requestPaint = function () {\n      needsPaint = !0;\n    };\n    exports.unstable_runWithPriority = function (priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n        case 4:\n        case 5:\n          break;\n        default:\n          priorityLevel = 3;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_scheduleCallback = function (\n      priorityLevel,\n      callback,\n      options\n    ) {\n      var currentTime = exports.unstable_now();\n      \"object\" === typeof options && null !== options\n        ? ((options = options.delay),\n          (options =\n            \"number\" === typeof options && 0 < options\n              ? currentTime + options\n              : currentTime))\n        : (options = currentTime);\n      switch (priorityLevel) {\n        case 1:\n          var timeout = -1;\n          break;\n        case 2:\n          timeout = 250;\n          break;\n        case 5:\n          timeout = 1073741823;\n          break;\n        case 4:\n          timeout = 1e4;\n          break;\n        default:\n          timeout = 5e3;\n      }\n      timeout = options + timeout;\n      priorityLevel = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: options,\n        expirationTime: timeout,\n        sortIndex: -1\n      };\n      options > currentTime\n        ? ((priorityLevel.sortIndex = options),\n          push(timerQueue, priorityLevel),\n          null === peek(taskQueue) &&\n            priorityLevel === peek(timerQueue) &&\n            (isHostTimeoutScheduled\n              ? (localClearTimeout(taskTimeoutID), (taskTimeoutID = -1))\n              : (isHostTimeoutScheduled = !0),\n            requestHostTimeout(handleTimeout, options - currentTime)))\n        : ((priorityLevel.sortIndex = timeout),\n          push(taskQueue, priorityLevel),\n          isHostCallbackScheduled ||\n            isPerformingWork ||\n            ((isHostCallbackScheduled = !0),\n            isMessageLoopRunning ||\n              ((isMessageLoopRunning = !0),\n              schedulePerformWorkUntilDeadline())));\n      return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function (callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/scheduler.production.js');\n} else {\n  module.exports = require('./cjs/scheduler.development.js');\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,2BAA2B;AAClC,qBAAa;AACb,YAAI,sBAAsB;AACxB,cAAI,cAAc,QAAQ,aAAa;AACvC,sBAAY;AACZ,cAAI,cAAc;AAClB,cAAI;AACF,eAAG;AACD,wCAA0B;AAC1B,yCACI,yBAAyB,OAC3B,kBAAkB,aAAa,GAC9B,gBAAgB;AACnB,iCAAmB;AACnB,kBAAI,wBAAwB;AAC5B,kBAAI;AACF,mBAAG;AACD,gCAAc,WAAW;AACzB,uBACE,cAAc,KAAK,SAAS,GAC5B,SAAS,eACT,EACE,YAAY,iBAAiB,eAC7B,kBAAkB,MAGpB;AACA,wBAAI,WAAW,YAAY;AAC3B,wBAAI,eAAe,OAAO,UAAU;AAClC,kCAAY,WAAW;AACvB,6CAAuB,YAAY;AACnC,0BAAI,uBAAuB;AAAA,wBACzB,YAAY,kBAAkB;AAAA,sBAChC;AACA,oCAAc,QAAQ,aAAa;AACnC,0BAAI,eAAe,OAAO,sBAAsB;AAC9C,oCAAY,WAAW;AACvB,sCAAc,WAAW;AACzB,sCAAc;AACd,8BAAM;AAAA,sBACR;AACA,sCAAgB,KAAK,SAAS,KAAK,IAAI,SAAS;AAChD,oCAAc,WAAW;AAAA,oBAC3B;AAAO,0BAAI,SAAS;AACpB,kCAAc,KAAK,SAAS;AAAA,kBAC9B;AACA,sBAAI,SAAS;AAAa,kCAAc;AAAA,uBACnC;AACH,wBAAI,aAAa,KAAK,UAAU;AAChC,6BAAS,cACP;AAAA,sBACE;AAAA,sBACA,WAAW,YAAY;AAAA,oBACzB;AACF,kCAAc;AAAA,kBAChB;AAAA,gBACF;AACA,sBAAM;AAAA,cACR,UAAE;AACA,gBAAC,cAAc,MACZ,uBAAuB,uBACvB,mBAAmB;AAAA,cACxB;AACA,4BAAc;AAAA,YAChB;AAAA,UACF,UAAE;AACA,0BACI,iCAAiC,IAChC,uBAAuB;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AACA,eAAS,KAAK,MAAM,MAAM;AACxB,YAAI,QAAQ,KAAK;AACjB,aAAK,KAAK,IAAI;AACd;AAAG,iBAAO,IAAI,SAAS;AACrB,gBAAI,cAAe,QAAQ,MAAO,GAChC,SAAS,KAAK,WAAW;AAC3B,gBAAI,IAAI,QAAQ,QAAQ,IAAI;AAC1B,cAAC,KAAK,WAAW,IAAI,MAClB,KAAK,KAAK,IAAI,QACd,QAAQ;AAAA;AACR,oBAAM;AAAA,UACb;AAAA,MACF;AACA,eAAS,KAAK,MAAM;AAClB,eAAO,MAAM,KAAK,SAAS,OAAO,KAAK,CAAC;AAAA,MAC1C;AACA,eAAS,IAAI,MAAM;AACjB,YAAI,MAAM,KAAK;AAAQ,iBAAO;AAC9B,YAAI,QAAQ,KAAK,CAAC,GAChB,OAAO,KAAK,IAAI;AAClB,YAAI,SAAS,OAAO;AAClB,eAAK,CAAC,IAAI;AACV;AAAG,qBACG,QAAQ,GAAG,SAAS,KAAK,QAAQ,aAAa,WAAW,GAC7D,QAAQ,cAER;AACA,kBAAI,YAAY,KAAK,QAAQ,KAAK,GAChC,OAAO,KAAK,SAAS,GACrB,aAAa,YAAY,GACzB,QAAQ,KAAK,UAAU;AACzB,kBAAI,IAAI,QAAQ,MAAM,IAAI;AACxB,6BAAa,UAAU,IAAI,QAAQ,OAAO,IAAI,KACxC,KAAK,KAAK,IAAI,OACf,KAAK,UAAU,IAAI,MACnB,QAAQ,eACP,KAAK,KAAK,IAAI,MACf,KAAK,SAAS,IAAI,MAClB,QAAQ;AAAA,uBACN,aAAa,UAAU,IAAI,QAAQ,OAAO,IAAI;AACrD,gBAAC,KAAK,KAAK,IAAI,OACZ,KAAK,UAAU,IAAI,MACnB,QAAQ;AAAA;AACR,sBAAM;AAAA,YACb;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,eAAS,QAAQ,GAAG,GAAG;AACrB,YAAI,OAAO,EAAE,YAAY,EAAE;AAC3B,eAAO,MAAM,OAAO,OAAO,EAAE,KAAK,EAAE;AAAA,MACtC;AACA,eAAS,cAAc,aAAa;AAClC,iBAAS,QAAQ,KAAK,UAAU,GAAG,SAAS,SAAS;AACnD,cAAI,SAAS,MAAM;AAAU,gBAAI,UAAU;AAAA,mBAClC,MAAM,aAAa;AAC1B,gBAAI,UAAU,GACX,MAAM,YAAY,MAAM,gBACzB,KAAK,WAAW,KAAK;AAAA;AACpB;AACL,kBAAQ,KAAK,UAAU;AAAA,QACzB;AAAA,MACF;AACA,eAAS,cAAc,aAAa;AAClC,iCAAyB;AACzB,sBAAc,WAAW;AACzB,YAAI,CAAC;AACH,cAAI,SAAS,KAAK,SAAS;AACzB,YAAC,0BAA0B,MACzB,yBACI,uBAAuB,MAAK,iCAAiC;AAAA,eAChE;AACH,gBAAI,aAAa,KAAK,UAAU;AAChC,qBAAS,cACP;AAAA,cACE;AAAA,cACA,WAAW,YAAY;AAAA,YACzB;AAAA,UACJ;AAAA,MACJ;AACA,eAAS,oBAAoB;AAC3B,eAAO,aACH,OACA,QAAQ,aAAa,IAAI,YAAY,gBACnC,QACA;AAAA,MACR;AACA,eAAS,mBAAmB,UAAU,IAAI;AACxC,wBAAgB,gBAAgB,WAAY;AAC1C,mBAAS,QAAQ,aAAa,CAAC;AAAA,QACjC,GAAG,EAAE;AAAA,MACP;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,cAAQ,eAAe;AACvB,UACE,aAAa,OAAO,eACpB,eAAe,OAAO,YAAY,KAClC;AACA,YAAI,mBAAmB;AACvB,gBAAQ,eAAe,WAAY;AACjC,iBAAO,iBAAiB,IAAI;AAAA,QAC9B;AAAA,MACF,OAAO;AACL,YAAI,YAAY,MACd,cAAc,UAAU,IAAI;AAC9B,gBAAQ,eAAe,WAAY;AACjC,iBAAO,UAAU,IAAI,IAAI;AAAA,QAC3B;AAAA,MACF;AACA,UAAI,YAAY,CAAC,GACf,aAAa,CAAC,GACd,gBAAgB,GAChB,cAAc,MACd,uBAAuB,GACvB,mBAAmB,OACnB,0BAA0B,OAC1B,yBAAyB,OACzB,aAAa,OACb,kBAAkB,eAAe,OAAO,aAAa,aAAa,MAClE,oBACE,eAAe,OAAO,eAAe,eAAe,MACtD,oBACE,gBAAgB,OAAO,eAAe,eAAe,MACvD,uBAAuB,OACvB,gBAAgB,IAChB,gBAAgB,GAChB,YAAY;AACd,UAAI,eAAe,OAAO;AACxB,YAAI,mCAAmC,WAAY;AACjD,4BAAkB,wBAAwB;AAAA,QAC5C;AAAA,eACO,gBAAgB,OAAO,gBAAgB;AAC9C,YAAI,UAAU,IAAI,eAAe,GAC/B,OAAO,QAAQ;AACjB,gBAAQ,MAAM,YAAY;AAC1B,2CAAmC,WAAY;AAC7C,eAAK,YAAY,IAAI;AAAA,QACvB;AAAA,MACF;AACE,2CAAmC,WAAY;AAC7C,0BAAgB,0BAA0B,CAAC;AAAA,QAC7C;AACF,cAAQ,wBAAwB;AAChC,cAAQ,6BAA6B;AACrC,cAAQ,uBAAuB;AAC/B,cAAQ,0BAA0B;AAClC,cAAQ,qBAAqB;AAC7B,cAAQ,gCAAgC;AACxC,cAAQ,0BAA0B,SAAU,MAAM;AAChD,aAAK,WAAW;AAAA,MAClB;AACA,cAAQ,0BAA0B,SAAU,KAAK;AAC/C,YAAI,OAAO,MAAM,MACb,QAAQ;AAAA,UACN;AAAA,QACF,IACC,gBAAgB,IAAI,MAAM,KAAK,MAAM,MAAM,GAAG,IAAI;AAAA,MACzD;AACA,cAAQ,mCAAmC,WAAY;AACrD,eAAO;AAAA,MACT;AACA,cAAQ,gBAAgB,SAAU,cAAc;AAC9C,gBAAQ,sBAAsB;AAAA,UAC5B,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,gBAAI,gBAAgB;AACpB;AAAA,UACF;AACE,4BAAgB;AAAA,QACpB;AACA,YAAI,wBAAwB;AAC5B,+BAAuB;AACvB,YAAI;AACF,iBAAO,aAAa;AAAA,QACtB,UAAE;AACA,iCAAuB;AAAA,QACzB;AAAA,MACF;AACA,cAAQ,wBAAwB,WAAY;AAC1C,qBAAa;AAAA,MACf;AACA,cAAQ,2BAA2B,SAAU,eAAe,cAAc;AACxE,gBAAQ,eAAe;AAAA,UACrB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH;AAAA,UACF;AACE,4BAAgB;AAAA,QACpB;AACA,YAAI,wBAAwB;AAC5B,+BAAuB;AACvB,YAAI;AACF,iBAAO,aAAa;AAAA,QACtB,UAAE;AACA,iCAAuB;AAAA,QACzB;AAAA,MACF;AACA,cAAQ,4BAA4B,SAClC,eACA,UACA,SACA;AACA,YAAI,cAAc,QAAQ,aAAa;AACvC,qBAAa,OAAO,WAAW,SAAS,WAClC,UAAU,QAAQ,OACnB,UACC,aAAa,OAAO,WAAW,IAAI,UAC/B,cAAc,UACd,eACL,UAAU;AACf,gBAAQ,eAAe;AAAA,UACrB,KAAK;AACH,gBAAI,UAAU;AACd;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF;AACE,sBAAU;AAAA,QACd;AACA,kBAAU,UAAU;AACpB,wBAAgB;AAAA,UACd,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,UACA,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,WAAW;AAAA,QACb;AACA,kBAAU,eACJ,cAAc,YAAY,SAC5B,KAAK,YAAY,aAAa,GAC9B,SAAS,KAAK,SAAS,KACrB,kBAAkB,KAAK,UAAU,MAChC,0BACI,kBAAkB,aAAa,GAAI,gBAAgB,MACnD,yBAAyB,MAC9B,mBAAmB,eAAe,UAAU,WAAW,OACvD,cAAc,YAAY,SAC5B,KAAK,WAAW,aAAa,GAC7B,2BACE,qBACE,0BAA0B,MAC5B,yBACI,uBAAuB,MACzB,iCAAiC;AACzC,eAAO;AAAA,MACT;AACA,cAAQ,uBAAuB;AAC/B,cAAQ,wBAAwB,SAAU,UAAU;AAClD,YAAI,sBAAsB;AAC1B,eAAO,WAAY;AACjB,cAAI,wBAAwB;AAC5B,iCAAuB;AACvB,cAAI;AACF,mBAAO,SAAS,MAAM,MAAM,SAAS;AAAA,UACvC,UAAE;AACA,mCAAuB;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AC3WL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": []}