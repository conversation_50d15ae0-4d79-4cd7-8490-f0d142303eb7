{"version": 3, "sources": ["../../jstat/dist/jstat.js", "../../bessel/bessel.js"], "sourcesContent": ["(function (window, factory) {\n    if (typeof exports === 'object') {\n        module.exports = factory();\n    } else if (typeof define === 'function' && define.amd) {\n        define(factory);\n    } else {\n        window.jStat = factory();\n    }\n})(this, function () {\nvar jStat = (function(Math, undefined) {\n\n// For quick reference.\nvar concat = Array.prototype.concat;\nvar slice = Array.prototype.slice;\nvar toString = Object.prototype.toString;\n\n// Calculate correction for IEEE error\n// TODO: This calculation can be improved.\nfunction calcRdx(n, m) {\n  var val = n > m ? n : m;\n  return Math.pow(10,\n                  17 - ~~(Math.log(((val > 0) ? val : -val)) * Math.LOG10E));\n}\n\n\nvar isArray = Array.isArray || function isArray(arg) {\n  return toString.call(arg) === '[object Array]';\n};\n\n\nfunction isFunction(arg) {\n  return toString.call(arg) === '[object Function]';\n}\n\n\nfunction isNumber(num) {\n  return (typeof num === 'number') ? num - num === 0 : false;\n}\n\n\n// Converts the jStat matrix to vector.\nfunction toVector(arr) {\n  return concat.apply([], arr);\n}\n\n\n// The one and only jStat constructor.\nfunction jStat() {\n  return new jStat._init(arguments);\n}\n\n\n// TODO: Remove after all references in src files have been removed.\njStat.fn = jStat.prototype;\n\n\n// By separating the initializer from the constructor it's easier to handle\n// always returning a new instance whether \"new\" was used or not.\njStat._init = function _init(args) {\n  // If first argument is an array, must be vector or matrix.\n  if (isArray(args[0])) {\n    // Check if matrix.\n    if (isArray(args[0][0])) {\n      // See if a mapping function was also passed.\n      if (isFunction(args[1]))\n        args[0] = jStat.map(args[0], args[1]);\n      // Iterate over each is faster than this.push.apply(this, args[0].\n      for (var i = 0; i < args[0].length; i++)\n        this[i] = args[0][i];\n      this.length = args[0].length;\n\n    // Otherwise must be a vector.\n    } else {\n      this[0] = isFunction(args[1]) ? jStat.map(args[0], args[1]) : args[0];\n      this.length = 1;\n    }\n\n  // If first argument is number, assume creation of sequence.\n  } else if (isNumber(args[0])) {\n    this[0] = jStat.seq.apply(null, args);\n    this.length = 1;\n\n  // Handle case when jStat object is passed to jStat.\n  } else if (args[0] instanceof jStat) {\n    // Duplicate the object and pass it back.\n    return jStat(args[0].toArray());\n\n  // Unexpected argument value, return empty jStat object.\n  // TODO: This is strange behavior. Shouldn't this throw or some such to let\n  // the user know they had bad arguments?\n  } else {\n    this[0] = [];\n    this.length = 1;\n  }\n\n  return this;\n};\njStat._init.prototype = jStat.prototype;\njStat._init.constructor = jStat;\n\n\n// Utility functions.\n// TODO: for internal use only?\njStat.utils = {\n  calcRdx: calcRdx,\n  isArray: isArray,\n  isFunction: isFunction,\n  isNumber: isNumber,\n  toVector: toVector\n};\n\n\njStat._random_fn = Math.random;\njStat.setRandom = function setRandom(fn) {\n  if (typeof fn !== 'function')\n    throw new TypeError('fn is not a function');\n  jStat._random_fn = fn;\n};\n\n\n// Easily extend the jStat object.\n// TODO: is this seriously necessary?\njStat.extend = function extend(obj) {\n  var i, j;\n\n  if (arguments.length === 1) {\n    for (j in obj)\n      jStat[j] = obj[j];\n    return this;\n  }\n\n  for (i = 1; i < arguments.length; i++) {\n    for (j in arguments[i])\n      obj[j] = arguments[i][j];\n  }\n\n  return obj;\n};\n\n\n// Returns the number of rows in the matrix.\njStat.rows = function rows(arr) {\n  return arr.length || 1;\n};\n\n\n// Returns the number of columns in the matrix.\njStat.cols = function cols(arr) {\n  return arr[0].length || 1;\n};\n\n\n// Returns the dimensions of the object { rows: i, cols: j }\njStat.dimensions = function dimensions(arr) {\n  return {\n    rows: jStat.rows(arr),\n    cols: jStat.cols(arr)\n  };\n};\n\n\n// Returns a specified row as a vector or return a sub matrix by pick some rows\njStat.row = function row(arr, index) {\n  if (isArray(index)) {\n    return index.map(function(i) {\n      return jStat.row(arr, i);\n    })\n  }\n  return arr[index];\n};\n\n\n// return row as array\n// rowa([[1,2],[3,4]],0) -> [1,2]\njStat.rowa = function rowa(arr, i) {\n  return jStat.row(arr, i);\n};\n\n\n// Returns the specified column as a vector or return a sub matrix by pick some\n// columns\njStat.col = function col(arr, index) {\n  if (isArray(index)) {\n    var submat = jStat.arange(arr.length).map(function() {\n      return new Array(index.length);\n    });\n    index.forEach(function(ind, i){\n      jStat.arange(arr.length).forEach(function(j) {\n        submat[j][i] = arr[j][ind];\n      });\n    });\n    return submat;\n  }\n  var column = new Array(arr.length);\n  for (var i = 0; i < arr.length; i++)\n    column[i] = [arr[i][index]];\n  return column;\n};\n\n\n// return column as array\n// cola([[1,2],[3,4]],0) -> [1,3]\njStat.cola = function cola(arr, i) {\n  return jStat.col(arr, i).map(function(a){ return a[0] });\n};\n\n\n// Returns the diagonal of the matrix\njStat.diag = function diag(arr) {\n  var nrow = jStat.rows(arr);\n  var res = new Array(nrow);\n  for (var row = 0; row < nrow; row++)\n    res[row] = [arr[row][row]];\n  return res;\n};\n\n\n// Returns the anti-diagonal of the matrix\njStat.antidiag = function antidiag(arr) {\n  var nrow = jStat.rows(arr) - 1;\n  var res = new Array(nrow);\n  for (var i = 0; nrow >= 0; nrow--, i++)\n    res[i] = [arr[i][nrow]];\n  return res;\n};\n\n// Transpose a matrix or array.\njStat.transpose = function transpose(arr) {\n  var obj = [];\n  var objArr, rows, cols, j, i;\n\n  // Make sure arr is in matrix format.\n  if (!isArray(arr[0]))\n    arr = [arr];\n\n  rows = arr.length;\n  cols = arr[0].length;\n\n  for (i = 0; i < cols; i++) {\n    objArr = new Array(rows);\n    for (j = 0; j < rows; j++)\n      objArr[j] = arr[j][i];\n    obj.push(objArr);\n  }\n\n  // If obj is vector, return only single array.\n  return obj.length === 1 ? obj[0] : obj;\n};\n\n\n// Map a function to an array or array of arrays.\n// \"toAlter\" is an internal variable.\njStat.map = function map(arr, func, toAlter) {\n  var row, nrow, ncol, res, col;\n\n  if (!isArray(arr[0]))\n    arr = [arr];\n\n  nrow = arr.length;\n  ncol = arr[0].length;\n  res = toAlter ? arr : new Array(nrow);\n\n  for (row = 0; row < nrow; row++) {\n    // if the row doesn't exist, create it\n    if (!res[row])\n      res[row] = new Array(ncol);\n    for (col = 0; col < ncol; col++)\n      res[row][col] = func(arr[row][col], row, col);\n  }\n\n  return res.length === 1 ? res[0] : res;\n};\n\n\n// Cumulatively combine the elements of an array or array of arrays using a function.\njStat.cumreduce = function cumreduce(arr, func, toAlter) {\n  var row, nrow, ncol, res, col;\n\n  if (!isArray(arr[0]))\n    arr = [arr];\n\n  nrow = arr.length;\n  ncol = arr[0].length;\n  res = toAlter ? arr : new Array(nrow);\n\n  for (row = 0; row < nrow; row++) {\n    // if the row doesn't exist, create it\n    if (!res[row])\n      res[row] = new Array(ncol);\n    if (ncol > 0)\n      res[row][0] = arr[row][0];\n    for (col = 1; col < ncol; col++)\n      res[row][col] = func(res[row][col-1], arr[row][col]);\n  }\n  return res.length === 1 ? res[0] : res;\n};\n\n\n// Destructively alter an array.\njStat.alter = function alter(arr, func) {\n  return jStat.map(arr, func, true);\n};\n\n\n// Generate a rows x cols matrix according to the supplied function.\njStat.create = function  create(rows, cols, func) {\n  var res = new Array(rows);\n  var i, j;\n\n  if (isFunction(cols)) {\n    func = cols;\n    cols = rows;\n  }\n\n  for (i = 0; i < rows; i++) {\n    res[i] = new Array(cols);\n    for (j = 0; j < cols; j++)\n      res[i][j] = func(i, j);\n  }\n\n  return res;\n};\n\n\nfunction retZero() { return 0; }\n\n\n// Generate a rows x cols matrix of zeros.\njStat.zeros = function zeros(rows, cols) {\n  if (!isNumber(cols))\n    cols = rows;\n  return jStat.create(rows, cols, retZero);\n};\n\n\nfunction retOne() { return 1; }\n\n\n// Generate a rows x cols matrix of ones.\njStat.ones = function ones(rows, cols) {\n  if (!isNumber(cols))\n    cols = rows;\n  return jStat.create(rows, cols, retOne);\n};\n\n\n// Generate a rows x cols matrix of uniformly random numbers.\njStat.rand = function rand(rows, cols) {\n  if (!isNumber(cols))\n    cols = rows;\n  return jStat.create(rows, cols, jStat._random_fn);\n};\n\n\nfunction retIdent(i, j) { return i === j ? 1 : 0; }\n\n\n// Generate an identity matrix of size row x cols.\njStat.identity = function identity(rows, cols) {\n  if (!isNumber(cols))\n    cols = rows;\n  return jStat.create(rows, cols, retIdent);\n};\n\n\n// Tests whether a matrix is symmetric\njStat.symmetric = function symmetric(arr) {\n  var size = arr.length;\n  var row, col;\n\n  if (arr.length !== arr[0].length)\n    return false;\n\n  for (row = 0; row < size; row++) {\n    for (col = 0; col < size; col++)\n      if (arr[col][row] !== arr[row][col])\n        return false;\n  }\n\n  return true;\n};\n\n\n// Set all values to zero.\njStat.clear = function clear(arr) {\n  return jStat.alter(arr, retZero);\n};\n\n\n// Generate sequence.\njStat.seq = function seq(min, max, length, func) {\n  if (!isFunction(func))\n    func = false;\n\n  var arr = [];\n  var hival = calcRdx(min, max);\n  var step = (max * hival - min * hival) / ((length - 1) * hival);\n  var current = min;\n  var cnt;\n\n  // Current is assigned using a technique to compensate for IEEE error.\n  // TODO: Needs better implementation.\n  for (cnt = 0;\n       current <= max && cnt < length;\n       cnt++, current = (min * hival + step * hival * cnt) / hival) {\n    arr.push((func ? func(current, cnt) : current));\n  }\n\n  return arr;\n};\n\n\n// arange(5) -> [0,1,2,3,4]\n// arange(1,5) -> [1,2,3,4]\n// arange(5,1,-1) -> [5,4,3,2]\njStat.arange = function arange(start, end, step) {\n  var rl = [];\n  var i;\n  step = step || 1;\n  if (end === undefined) {\n    end = start;\n    start = 0;\n  }\n  if (start === end || step === 0) {\n    return [];\n  }\n  if (start < end && step < 0) {\n    return [];\n  }\n  if (start > end && step > 0) {\n    return [];\n  }\n  if (step > 0) {\n    for (i = start; i < end; i += step) {\n      rl.push(i);\n    }\n  } else {\n    for (i = start; i > end; i += step) {\n      rl.push(i);\n    }\n  }\n  return rl;\n};\n\n\n// A=[[1,2,3],[4,5,6],[7,8,9]]\n// slice(A,{row:{end:2},col:{start:1}}) -> [[2,3],[5,6]]\n// slice(A,1,{start:1}) -> [5,6]\n// as numpy code A[:2,1:]\njStat.slice = (function(){\n  function _slice(list, start, end, step) {\n    // note it's not equal to range.map mode it's a bug\n    var i;\n    var rl = [];\n    var length = list.length;\n    if (start === undefined && end === undefined && step === undefined) {\n      return jStat.copy(list);\n    }\n\n    start = start || 0;\n    end = end || list.length;\n    start = start >= 0 ? start : length + start;\n    end = end >= 0 ? end : length + end;\n    step = step || 1;\n    if (start === end || step === 0) {\n      return [];\n    }\n    if (start < end && step < 0) {\n      return [];\n    }\n    if (start > end && step > 0) {\n      return [];\n    }\n    if (step > 0) {\n      for (i = start; i < end; i += step) {\n        rl.push(list[i]);\n      }\n    } else {\n      for (i = start; i > end;i += step) {\n        rl.push(list[i]);\n      }\n    }\n    return rl;\n  }\n\n  function slice(list, rcSlice) {\n    var colSlice, rowSlice;\n    rcSlice = rcSlice || {};\n    if (isNumber(rcSlice.row)) {\n      if (isNumber(rcSlice.col))\n        return list[rcSlice.row][rcSlice.col];\n      var row = jStat.rowa(list, rcSlice.row);\n      colSlice = rcSlice.col || {};\n      return _slice(row, colSlice.start, colSlice.end, colSlice.step);\n    }\n\n    if (isNumber(rcSlice.col)) {\n      var col = jStat.cola(list, rcSlice.col);\n      rowSlice = rcSlice.row || {};\n      return _slice(col, rowSlice.start, rowSlice.end, rowSlice.step);\n    }\n\n    rowSlice = rcSlice.row || {};\n    colSlice = rcSlice.col || {};\n    var rows = _slice(list, rowSlice.start, rowSlice.end, rowSlice.step);\n    return rows.map(function(row) {\n      return _slice(row, colSlice.start, colSlice.end, colSlice.step);\n    });\n  }\n\n  return slice;\n}());\n\n\n// A=[[1,2,3],[4,5,6],[7,8,9]]\n// sliceAssign(A,{row:{start:1},col:{start:1}},[[0,0],[0,0]])\n// A=[[1,2,3],[4,0,0],[7,0,0]]\njStat.sliceAssign = function sliceAssign(A, rcSlice, B) {\n  var nl, ml;\n  if (isNumber(rcSlice.row)) {\n    if (isNumber(rcSlice.col))\n      return A[rcSlice.row][rcSlice.col] = B;\n    rcSlice.col = rcSlice.col || {};\n    rcSlice.col.start = rcSlice.col.start || 0;\n    rcSlice.col.end = rcSlice.col.end || A[0].length;\n    rcSlice.col.step = rcSlice.col.step || 1;\n    nl = jStat.arange(rcSlice.col.start,\n                          Math.min(A.length, rcSlice.col.end),\n                          rcSlice.col.step);\n    var m = rcSlice.row;\n    nl.forEach(function(n, i) {\n      A[m][n] = B[i];\n    });\n    return A;\n  }\n\n  if (isNumber(rcSlice.col)) {\n    rcSlice.row = rcSlice.row || {};\n    rcSlice.row.start = rcSlice.row.start || 0;\n    rcSlice.row.end = rcSlice.row.end || A.length;\n    rcSlice.row.step = rcSlice.row.step || 1;\n    ml = jStat.arange(rcSlice.row.start,\n                          Math.min(A[0].length, rcSlice.row.end),\n                          rcSlice.row.step);\n    var n = rcSlice.col;\n    ml.forEach(function(m, j) {\n      A[m][n] = B[j];\n    });\n    return A;\n  }\n\n  if (B[0].length === undefined) {\n    B = [B];\n  }\n  rcSlice.row.start = rcSlice.row.start || 0;\n  rcSlice.row.end = rcSlice.row.end || A.length;\n  rcSlice.row.step = rcSlice.row.step || 1;\n  rcSlice.col.start = rcSlice.col.start || 0;\n  rcSlice.col.end = rcSlice.col.end || A[0].length;\n  rcSlice.col.step = rcSlice.col.step || 1;\n  ml = jStat.arange(rcSlice.row.start,\n                        Math.min(A.length, rcSlice.row.end),\n                        rcSlice.row.step);\n  nl = jStat.arange(rcSlice.col.start,\n                        Math.min(A[0].length, rcSlice.col.end),\n                        rcSlice.col.step);\n  ml.forEach(function(m, i) {\n    nl.forEach(function(n, j) {\n      A[m][n] = B[i][j];\n    });\n  });\n  return A;\n};\n\n\n// [1,2,3] ->\n// [[1,0,0],[0,2,0],[0,0,3]]\njStat.diagonal = function diagonal(diagArray) {\n  var mat = jStat.zeros(diagArray.length, diagArray.length);\n  diagArray.forEach(function(t, i) {\n    mat[i][i] = t;\n  });\n  return mat;\n};\n\n\n// return copy of A\njStat.copy = function copy(A) {\n  return A.map(function(row) {\n    if (isNumber(row))\n      return row;\n    return row.map(function(t) {\n      return t;\n    });\n  });\n};\n\n\n// TODO: Go over this entire implementation. Seems a tragic waste of resources\n// doing all this work. Instead, and while ugly, use new Function() to generate\n// a custom function for each static method.\n\n// Quick reference.\nvar jProto = jStat.prototype;\n\n// Default length.\njProto.length = 0;\n\n// For internal use only.\n// TODO: Check if they're actually used, and if they are then rename them\n// to _*\njProto.push = Array.prototype.push;\njProto.sort = Array.prototype.sort;\njProto.splice = Array.prototype.splice;\njProto.slice = Array.prototype.slice;\n\n\n// Return a clean array.\njProto.toArray = function toArray() {\n  return this.length > 1 ? slice.call(this) : slice.call(this)[0];\n};\n\n\n// Map a function to a matrix or vector.\njProto.map = function map(func, toAlter) {\n  return jStat(jStat.map(this, func, toAlter));\n};\n\n\n// Cumulatively combine the elements of a matrix or vector using a function.\njProto.cumreduce = function cumreduce(func, toAlter) {\n  return jStat(jStat.cumreduce(this, func, toAlter));\n};\n\n\n// Destructively alter an array.\njProto.alter = function alter(func) {\n  jStat.alter(this, func);\n  return this;\n};\n\n\n// Extend prototype with methods that have no argument.\n(function(funcs) {\n  for (var i = 0; i < funcs.length; i++) (function(passfunc) {\n    jProto[passfunc] = function(func) {\n      var self = this,\n      results;\n      // Check for callback.\n      if (func) {\n        setTimeout(function() {\n          func.call(self, jProto[passfunc].call(self));\n        });\n        return this;\n      }\n      results = jStat[passfunc](this);\n      return isArray(results) ? jStat(results) : results;\n    };\n  })(funcs[i]);\n})('transpose clear symmetric rows cols dimensions diag antidiag'.split(' '));\n\n\n// Extend prototype with methods that have one argument.\n(function(funcs) {\n  for (var i = 0; i < funcs.length; i++) (function(passfunc) {\n    jProto[passfunc] = function(index, func) {\n      var self = this;\n      // check for callback\n      if (func) {\n        setTimeout(function() {\n          func.call(self, jProto[passfunc].call(self, index));\n        });\n        return this;\n      }\n      return jStat(jStat[passfunc](this, index));\n    };\n  })(funcs[i]);\n})('row col'.split(' '));\n\n\n// Extend prototype with simple shortcut methods.\n(function(funcs) {\n  for (var i = 0; i < funcs.length; i++) (function(passfunc) {\n    jProto[passfunc] = function() {\n      return jStat(jStat[passfunc].apply(null, arguments));\n    };\n  })(funcs[i]);\n})('create zeros ones rand identity'.split(' '));\n\n\n// Exposing jStat.\nreturn jStat;\n\n}(Math));\n(function(jStat, Math) {\n\nvar isFunction = jStat.utils.isFunction;\n\n// Ascending functions for sort\nfunction ascNum(a, b) { return a - b; }\n\nfunction clip(arg, min, max) {\n  return Math.max(min, Math.min(arg, max));\n}\n\n\n// sum of an array\njStat.sum = function sum(arr) {\n  var sum = 0;\n  var i = arr.length;\n  while (--i >= 0)\n    sum += arr[i];\n  return sum;\n};\n\n\n// sum squared\njStat.sumsqrd = function sumsqrd(arr) {\n  var sum = 0;\n  var i = arr.length;\n  while (--i >= 0)\n    sum += arr[i] * arr[i];\n  return sum;\n};\n\n\n// sum of squared errors of prediction (SSE)\njStat.sumsqerr = function sumsqerr(arr) {\n  var mean = jStat.mean(arr);\n  var sum = 0;\n  var i = arr.length;\n  var tmp;\n  while (--i >= 0) {\n    tmp = arr[i] - mean;\n    sum += tmp * tmp;\n  }\n  return sum;\n};\n\n// sum of an array in each row\njStat.sumrow = function sumrow(arr) {\n  var sum = 0;\n  var i = arr.length;\n  while (--i >= 0)\n    sum += arr[i];\n  return sum;\n};\n\n// product of an array\njStat.product = function product(arr) {\n  var prod = 1;\n  var i = arr.length;\n  while (--i >= 0)\n    prod *= arr[i];\n  return prod;\n};\n\n\n// minimum value of an array\njStat.min = function min(arr) {\n  var low = arr[0];\n  var i = 0;\n  while (++i < arr.length)\n    if (arr[i] < low)\n      low = arr[i];\n  return low;\n};\n\n\n// maximum value of an array\njStat.max = function max(arr) {\n  var high = arr[0];\n  var i = 0;\n  while (++i < arr.length)\n    if (arr[i] > high)\n      high = arr[i];\n  return high;\n};\n\n\n// unique values of an array\njStat.unique = function unique(arr) {\n  var hash = {}, _arr = [];\n  for(var i = 0; i < arr.length; i++) {\n    if (!hash[arr[i]]) {\n      hash[arr[i]] = true;\n      _arr.push(arr[i]);\n    }\n  }\n  return _arr;\n};\n\n\n// mean value of an array\njStat.mean = function mean(arr) {\n  return jStat.sum(arr) / arr.length;\n};\n\n\n// mean squared error (MSE)\njStat.meansqerr = function meansqerr(arr) {\n  return jStat.sumsqerr(arr) / arr.length;\n};\n\n\n// geometric mean of an array\njStat.geomean = function geomean(arr) {\n  var logs = arr.map(Math.log)\n  var meanOfLogs = jStat.mean(logs)\n  return Math.exp(meanOfLogs)\n};\n\n\n// median of an array\njStat.median = function median(arr) {\n  var arrlen = arr.length;\n  var _arr = arr.slice().sort(ascNum);\n  // check if array is even or odd, then return the appropriate\n  return !(arrlen & 1)\n    ? (_arr[(arrlen / 2) - 1 ] + _arr[(arrlen / 2)]) / 2\n    : _arr[(arrlen / 2) | 0 ];\n};\n\n\n// cumulative sum of an array\njStat.cumsum = function cumsum(arr) {\n  return jStat.cumreduce(arr, function (a, b) { return a + b; });\n};\n\n\n// cumulative product of an array\njStat.cumprod = function cumprod(arr) {\n  return jStat.cumreduce(arr, function (a, b) { return a * b; });\n};\n\n\n// successive differences of a sequence\njStat.diff = function diff(arr) {\n  var diffs = [];\n  var arrLen = arr.length;\n  var i;\n  for (i = 1; i < arrLen; i++)\n    diffs.push(arr[i] - arr[i - 1]);\n  return diffs;\n};\n\n\n// ranks of an array\njStat.rank = function (arr) {\n  var i;\n  var distinctNumbers = [];\n  var numberCounts = {};\n  for (i = 0; i < arr.length; i++) {\n    var number = arr[i];\n    if (numberCounts[number]) {\n      numberCounts[number]++;\n    } else {\n      numberCounts[number] = 1;\n      distinctNumbers.push(number);\n    }\n  }\n\n  var sortedDistinctNumbers = distinctNumbers.sort(ascNum);\n  var numberRanks = {};\n  var currentRank = 1;\n  for (i = 0; i < sortedDistinctNumbers.length; i++) {\n    var number = sortedDistinctNumbers[i];\n    var count = numberCounts[number];\n    var first = currentRank;\n    var last = currentRank + count - 1;\n    var rank = (first + last) / 2;\n    numberRanks[number] = rank;\n    currentRank += count;\n  }\n\n  return arr.map(function (number) {\n    return numberRanks[number];\n  });\n};\n\n\n// mode of an array\n// if there are multiple modes of an array, return all of them\n// is this the appropriate way of handling it?\njStat.mode = function mode(arr) {\n  var arrLen = arr.length;\n  var _arr = arr.slice().sort(ascNum);\n  var count = 1;\n  var maxCount = 0;\n  var numMaxCount = 0;\n  var mode_arr = [];\n  var i;\n\n  for (i = 0; i < arrLen; i++) {\n    if (_arr[i] === _arr[i + 1]) {\n      count++;\n    } else {\n      if (count > maxCount) {\n        mode_arr = [_arr[i]];\n        maxCount = count;\n        numMaxCount = 0;\n      }\n      // are there multiple max counts\n      else if (count === maxCount) {\n        mode_arr.push(_arr[i]);\n        numMaxCount++;\n      }\n      // resetting count for new value in array\n      count = 1;\n    }\n  }\n\n  return numMaxCount === 0 ? mode_arr[0] : mode_arr;\n};\n\n\n// range of an array\njStat.range = function range(arr) {\n  return jStat.max(arr) - jStat.min(arr);\n};\n\n// variance of an array\n// flag = true indicates sample instead of population\njStat.variance = function variance(arr, flag) {\n  return jStat.sumsqerr(arr) / (arr.length - (flag ? 1 : 0));\n};\n\n// pooled variance of an array of arrays\njStat.pooledvariance = function pooledvariance(arr) {\n  var sumsqerr = arr.reduce(function (a, samples) {return a + jStat.sumsqerr(samples);}, 0);\n  var count = arr.reduce(function (a, samples) {return a + samples.length;}, 0);\n  return sumsqerr / (count - arr.length);\n};\n\n// deviation of an array\njStat.deviation = function (arr) {\n  var mean = jStat.mean(arr);\n  var arrlen = arr.length;\n  var dev = new Array(arrlen);\n  for (var i = 0; i < arrlen; i++) {\n    dev[i] = arr[i] - mean;\n  }\n  return dev;\n};\n\n// standard deviation of an array\n// flag = true indicates sample instead of population\njStat.stdev = function stdev(arr, flag) {\n  return Math.sqrt(jStat.variance(arr, flag));\n};\n\n// pooled standard deviation of an array of arrays\njStat.pooledstdev = function pooledstdev(arr) {\n  return Math.sqrt(jStat.pooledvariance(arr));\n};\n\n// mean deviation (mean absolute deviation) of an array\njStat.meandev = function meandev(arr) {\n  var mean = jStat.mean(arr);\n  var a = [];\n  for (var i = arr.length - 1; i >= 0; i--) {\n    a.push(Math.abs(arr[i] - mean));\n  }\n  return jStat.mean(a);\n};\n\n\n// median deviation (median absolute deviation) of an array\njStat.meddev = function meddev(arr) {\n  var median = jStat.median(arr);\n  var a = [];\n  for (var i = arr.length - 1; i >= 0; i--) {\n    a.push(Math.abs(arr[i] - median));\n  }\n  return jStat.median(a);\n};\n\n\n// coefficient of variation\njStat.coeffvar = function coeffvar(arr) {\n  return jStat.stdev(arr) / jStat.mean(arr);\n};\n\n\n// quartiles of an array\njStat.quartiles = function quartiles(arr) {\n  var arrlen = arr.length;\n  var _arr = arr.slice().sort(ascNum);\n  return [\n    _arr[ Math.round((arrlen) / 4) - 1 ],\n    _arr[ Math.round((arrlen) / 2) - 1 ],\n    _arr[ Math.round((arrlen) * 3 / 4) - 1 ]\n  ];\n};\n\n\n// Arbitary quantiles of an array. Direct port of the scipy.stats\n// implementation by Pierre GF Gerard-Marchant.\njStat.quantiles = function quantiles(arr, quantilesArray, alphap, betap) {\n  var sortedArray = arr.slice().sort(ascNum);\n  var quantileVals = [quantilesArray.length];\n  var n = arr.length;\n  var i, p, m, aleph, k, gamma;\n\n  if (typeof alphap === 'undefined')\n    alphap = 3 / 8;\n  if (typeof betap === 'undefined')\n    betap = 3 / 8;\n\n  for (i = 0; i < quantilesArray.length; i++) {\n    p = quantilesArray[i];\n    m = alphap + p * (1 - alphap - betap);\n    aleph = n * p + m;\n    k = Math.floor(clip(aleph, 1, n - 1));\n    gamma = clip(aleph - k, 0, 1);\n    quantileVals[i] = (1 - gamma) * sortedArray[k - 1] + gamma * sortedArray[k];\n  }\n\n  return quantileVals;\n};\n\n// Return the k-th percentile of values in a range, where k is in the range 0..1, inclusive.\n// Passing true for the exclusive parameter excludes both endpoints of the range.\njStat.percentile = function percentile(arr, k, exclusive) {\n  var _arr = arr.slice().sort(ascNum);\n  var realIndex = k * (_arr.length + (exclusive ? 1 : -1)) + (exclusive ? 0 : 1);\n  var index = parseInt(realIndex);\n  var frac = realIndex - index;\n  if (index + 1 < _arr.length) {\n    return _arr[index - 1] + frac * (_arr[index] - _arr[index - 1]);\n  } else {\n    return _arr[index - 1];\n  }\n}\n\n// The percentile rank of score in a given array. Returns the percentage\n// of all values in the input array that are less than (kind='strict') or\n// less or equal than (kind='weak') score. Default is weak.\njStat.percentileOfScore = function percentileOfScore(arr, score, kind) {\n  var counter = 0;\n  var len = arr.length;\n  var strict = false;\n  var value, i;\n\n  if (kind === 'strict')\n    strict = true;\n\n  for (i = 0; i < len; i++) {\n    value = arr[i];\n    if ((strict && value < score) ||\n        (!strict && value <= score)) {\n      counter++;\n    }\n  }\n\n  return counter / len;\n};\n\n\n// Histogram (bin count) data\njStat.histogram = function histogram(arr, binCnt) {\n  binCnt = binCnt || 4;\n  var first = jStat.min(arr);\n  var binWidth = (jStat.max(arr) - first) / binCnt;\n  var len = arr.length;\n  var bins = [];\n  var i;\n\n  for (i = 0; i < binCnt; i++)\n    bins[i] = 0;\n  for (i = 0; i < len; i++)\n    bins[Math.min(Math.floor(((arr[i] - first) / binWidth)), binCnt - 1)] += 1;\n\n  return bins;\n};\n\n\n// covariance of two arrays\njStat.covariance = function covariance(arr1, arr2) {\n  var u = jStat.mean(arr1);\n  var v = jStat.mean(arr2);\n  var arr1Len = arr1.length;\n  var sq_dev = new Array(arr1Len);\n  var i;\n\n  for (i = 0; i < arr1Len; i++)\n    sq_dev[i] = (arr1[i] - u) * (arr2[i] - v);\n\n  return jStat.sum(sq_dev) / (arr1Len - 1);\n};\n\n\n// (pearson's) population correlation coefficient, rho\njStat.corrcoeff = function corrcoeff(arr1, arr2) {\n  return jStat.covariance(arr1, arr2) /\n      jStat.stdev(arr1, 1) /\n      jStat.stdev(arr2, 1);\n};\n\n  // (spearman's) rank correlation coefficient, sp\njStat.spearmancoeff =  function (arr1, arr2) {\n  arr1 = jStat.rank(arr1);\n  arr2 = jStat.rank(arr2);\n  //return pearson's correlation of the ranks:\n  return jStat.corrcoeff(arr1, arr2);\n}\n\n\n// statistical standardized moments (general form of skew/kurt)\njStat.stanMoment = function stanMoment(arr, n) {\n  var mu = jStat.mean(arr);\n  var sigma = jStat.stdev(arr);\n  var len = arr.length;\n  var skewSum = 0;\n\n  for (var i = 0; i < len; i++)\n    skewSum += Math.pow((arr[i] - mu) / sigma, n);\n\n  return skewSum / arr.length;\n};\n\n// (pearson's) moment coefficient of skewness\njStat.skewness = function skewness(arr) {\n  return jStat.stanMoment(arr, 3);\n};\n\n// (pearson's) (excess) kurtosis\njStat.kurtosis = function kurtosis(arr) {\n  return jStat.stanMoment(arr, 4) - 3;\n};\n\n\nvar jProto = jStat.prototype;\n\n\n// Extend jProto with method for calculating cumulative sums and products.\n// This differs from the similar extension below as cumsum and cumprod should\n// not be run again in the case fullbool === true.\n// If a matrix is passed, automatically assume operation should be done on the\n// columns.\n(function(funcs) {\n  for (var i = 0; i < funcs.length; i++) (function(passfunc) {\n    // If a matrix is passed, automatically assume operation should be done on\n    // the columns.\n    jProto[passfunc] = function(fullbool, func) {\n      var arr = [];\n      var i = 0;\n      var tmpthis = this;\n      // Assignment reassignation depending on how parameters were passed in.\n      if (isFunction(fullbool)) {\n        func = fullbool;\n        fullbool = false;\n      }\n      // Check if a callback was passed with the function.\n      if (func) {\n        setTimeout(function() {\n          func.call(tmpthis, jProto[passfunc].call(tmpthis, fullbool));\n        });\n        return this;\n      }\n      // Check if matrix and run calculations.\n      if (this.length > 1) {\n        tmpthis = fullbool === true ? this : this.transpose();\n        for (; i < tmpthis.length; i++)\n          arr[i] = jStat[passfunc](tmpthis[i]);\n        return arr;\n      }\n      // Pass fullbool if only vector, not a matrix. for variance and stdev.\n      return jStat[passfunc](this[0], fullbool);\n    };\n  })(funcs[i]);\n})(('cumsum cumprod').split(' '));\n\n\n// Extend jProto with methods which don't require arguments and work on columns.\n(function(funcs) {\n  for (var i = 0; i < funcs.length; i++) (function(passfunc) {\n    // If a matrix is passed, automatically assume operation should be done on\n    // the columns.\n    jProto[passfunc] = function(fullbool, func) {\n      var arr = [];\n      var i = 0;\n      var tmpthis = this;\n      // Assignment reassignation depending on how parameters were passed in.\n      if (isFunction(fullbool)) {\n        func = fullbool;\n        fullbool = false;\n      }\n      // Check if a callback was passed with the function.\n      if (func) {\n        setTimeout(function() {\n          func.call(tmpthis, jProto[passfunc].call(tmpthis, fullbool));\n        });\n        return this;\n      }\n      // Check if matrix and run calculations.\n      if (this.length > 1) {\n        if (passfunc !== 'sumrow')\n          tmpthis = fullbool === true ? this : this.transpose();\n        for (; i < tmpthis.length; i++)\n          arr[i] = jStat[passfunc](tmpthis[i]);\n        return fullbool === true\n            ? jStat[passfunc](jStat.utils.toVector(arr))\n            : arr;\n      }\n      // Pass fullbool if only vector, not a matrix. for variance and stdev.\n      return jStat[passfunc](this[0], fullbool);\n    };\n  })(funcs[i]);\n})(('sum sumsqrd sumsqerr sumrow product min max unique mean meansqerr ' +\n    'geomean median diff rank mode range variance deviation stdev meandev ' +\n    'meddev coeffvar quartiles histogram skewness kurtosis').split(' '));\n\n\n// Extend jProto with functions that take arguments. Operations on matrices are\n// done on columns.\n(function(funcs) {\n  for (var i = 0; i < funcs.length; i++) (function(passfunc) {\n    jProto[passfunc] = function() {\n      var arr = [];\n      var i = 0;\n      var tmpthis = this;\n      var args = Array.prototype.slice.call(arguments);\n      var callbackFunction;\n\n      // If the last argument is a function, we assume it's a callback; we\n      // strip the callback out and call the function again.\n      if (isFunction(args[args.length - 1])) {\n        callbackFunction = args[args.length - 1];\n        var argsToPass = args.slice(0, args.length - 1);\n\n        setTimeout(function() {\n          callbackFunction.call(tmpthis,\n                                jProto[passfunc].apply(tmpthis, argsToPass));\n        });\n        return this;\n\n      // Otherwise we curry the function args and call normally.\n      } else {\n        callbackFunction = undefined;\n        var curriedFunction = function curriedFunction(vector) {\n          return jStat[passfunc].apply(tmpthis, [vector].concat(args));\n        }\n      }\n\n      // If this is a matrix, run column-by-column.\n      if (this.length > 1) {\n        tmpthis = tmpthis.transpose();\n        for (; i < tmpthis.length; i++)\n          arr[i] = curriedFunction(tmpthis[i]);\n        return arr;\n      }\n\n      // Otherwise run on the vector.\n      return curriedFunction(this[0]);\n    };\n  })(funcs[i]);\n})('quantiles percentileOfScore'.split(' '));\n\n}(jStat, Math));\n// Special functions //\n(function(jStat, Math) {\n\n// Log-gamma function\njStat.gammaln = function gammaln(x) {\n  var j = 0;\n  var cof = [\n    76.18009172947146, -86.50532032941677, 24.01409824083091,\n    -1.231739572450155, 0.1208650973866179e-2, -0.5395239384953e-5\n  ];\n  var ser = 1.000000000190015;\n  var xx, y, tmp;\n  tmp = (y = xx = x) + 5.5;\n  tmp -= (xx + 0.5) * Math.log(tmp);\n  for (; j < 6; j++)\n    ser += cof[j] / ++y;\n  return Math.log(2.5066282746310005 * ser / xx) - tmp;\n};\n\n/*\n * log-gamma function to support poisson distribution sampling. The\n * algorithm comes from SPECFUN by Shanjie Zhang and Jianming Jin and their\n * book \"Computation of Special Functions\", 1996, John Wiley & Sons, Inc.\n */\njStat.loggam = function loggam(x) {\n  var x0, x2, xp, gl, gl0;\n  var k, n;\n\n  var a = [8.333333333333333e-02, -2.777777777777778e-03,\n          7.936507936507937e-04, -5.952380952380952e-04,\n          8.417508417508418e-04, -1.917526917526918e-03,\n          6.410256410256410e-03, -2.955065359477124e-02,\n          1.796443723688307e-01, -1.39243221690590e+00];\n  x0 = x;\n  n = 0;\n  if ((x == 1.0) || (x == 2.0)) {\n      return 0.0;\n  }\n  if (x <= 7.0) {\n      n = Math.floor(7 - x);\n      x0 = x + n;\n  }\n  x2 = 1.0 / (x0 * x0);\n  xp = 2 * Math.PI;\n  gl0 = a[9];\n  for (k = 8; k >= 0; k--) {\n      gl0 *= x2;\n      gl0 += a[k];\n  }\n  gl = gl0 / x0 + 0.5 * Math.log(xp) + (x0 - 0.5) * Math.log(x0) - x0;\n  if (x <= 7.0) {\n      for (k = 1; k <= n; k++) {\n          gl -= Math.log(x0 - 1.0);\n          x0 -= 1.0;\n      }\n  }\n  return gl;\n}\n\n// gamma of x\njStat.gammafn = function gammafn(x) {\n  var p = [-1.716185138865495, 24.76565080557592, -379.80425647094563,\n           629.3311553128184, 866.9662027904133, -31451.272968848367,\n           -36144.413418691176, 66456.14382024054\n  ];\n  var q = [-30.8402300119739, 315.35062697960416, -1015.1563674902192,\n           -3107.771671572311, 22538.118420980151, 4755.8462775278811,\n           -134659.9598649693, -115132.2596755535];\n  var fact = false;\n  var n = 0;\n  var xden = 0;\n  var xnum = 0;\n  var y = x;\n  var i, z, yi, res;\n  if (x > 171.6243769536076) {\n    return Infinity;\n  }\n  if (y <= 0) {\n    res = y % 1 + 3.6e-16;\n    if (res) {\n      fact = (!(y & 1) ? 1 : -1) * Math.PI / Math.sin(Math.PI * res);\n      y = 1 - y;\n    } else {\n      return Infinity;\n    }\n  }\n  yi = y;\n  if (y < 1) {\n    z = y++;\n  } else {\n    z = (y -= n = (y | 0) - 1) - 1;\n  }\n  for (i = 0; i < 8; ++i) {\n    xnum = (xnum + p[i]) * z;\n    xden = xden * z + q[i];\n  }\n  res = xnum / xden + 1;\n  if (yi < y) {\n    res /= yi;\n  } else if (yi > y) {\n    for (i = 0; i < n; ++i) {\n      res *= y;\n      y++;\n    }\n  }\n  if (fact) {\n    res = fact / res;\n  }\n  return res;\n};\n\n\n// lower incomplete gamma function, which is usually typeset with a\n// lower-case greek gamma as the function symbol\njStat.gammap = function gammap(a, x) {\n  return jStat.lowRegGamma(a, x) * jStat.gammafn(a);\n};\n\n\n// The lower regularized incomplete gamma function, usually written P(a,x)\njStat.lowRegGamma = function lowRegGamma(a, x) {\n  var aln = jStat.gammaln(a);\n  var ap = a;\n  var sum = 1 / a;\n  var del = sum;\n  var b = x + 1 - a;\n  var c = 1 / 1.0e-30;\n  var d = 1 / b;\n  var h = d;\n  var i = 1;\n  // calculate maximum number of itterations required for a\n  var ITMAX = -~(Math.log((a >= 1) ? a : 1 / a) * 8.5 + a * 0.4 + 17);\n  var an;\n\n  if (x < 0 || a <= 0) {\n    return NaN;\n  } else if (x < a + 1) {\n    for (; i <= ITMAX; i++) {\n      sum += del *= x / ++ap;\n    }\n    return (sum * Math.exp(-x + a * Math.log(x) - (aln)));\n  }\n\n  for (; i <= ITMAX; i++) {\n    an = -i * (i - a);\n    b += 2;\n    d = an * d + b;\n    c = b + an / c;\n    d = 1 / d;\n    h *= d * c;\n  }\n\n  return (1 - h * Math.exp(-x + a * Math.log(x) - (aln)));\n};\n\n// natural log factorial of n\njStat.factorialln = function factorialln(n) {\n  return n < 0 ? NaN : jStat.gammaln(n + 1);\n};\n\n// factorial of n\njStat.factorial = function factorial(n) {\n  return n < 0 ? NaN : jStat.gammafn(n + 1);\n};\n\n// combinations of n, m\njStat.combination = function combination(n, m) {\n  // make sure n or m don't exceed the upper limit of usable values\n  return (n > 170 || m > 170)\n      ? Math.exp(jStat.combinationln(n, m))\n      : (jStat.factorial(n) / jStat.factorial(m)) / jStat.factorial(n - m);\n};\n\n\njStat.combinationln = function combinationln(n, m){\n  return jStat.factorialln(n) - jStat.factorialln(m) - jStat.factorialln(n - m);\n};\n\n\n// permutations of n, m\njStat.permutation = function permutation(n, m) {\n  return jStat.factorial(n) / jStat.factorial(n - m);\n};\n\n\n// beta function\njStat.betafn = function betafn(x, y) {\n  // ensure arguments are positive\n  if (x <= 0 || y <= 0)\n    return undefined;\n  // make sure x + y doesn't exceed the upper limit of usable values\n  return (x + y > 170)\n      ? Math.exp(jStat.betaln(x, y))\n      : jStat.gammafn(x) * jStat.gammafn(y) / jStat.gammafn(x + y);\n};\n\n\n// natural logarithm of beta function\njStat.betaln = function betaln(x, y) {\n  return jStat.gammaln(x) + jStat.gammaln(y) - jStat.gammaln(x + y);\n};\n\n\n// Evaluates the continued fraction for incomplete beta function by modified\n// Lentz's method.\njStat.betacf = function betacf(x, a, b) {\n  var fpmin = 1e-30;\n  var m = 1;\n  var qab = a + b;\n  var qap = a + 1;\n  var qam = a - 1;\n  var c = 1;\n  var d = 1 - qab * x / qap;\n  var m2, aa, del, h;\n\n  // These q's will be used in factors that occur in the coefficients\n  if (Math.abs(d) < fpmin)\n    d = fpmin;\n  d = 1 / d;\n  h = d;\n\n  for (; m <= 100; m++) {\n    m2 = 2 * m;\n    aa = m * (b - m) * x / ((qam + m2) * (a + m2));\n    // One step (the even one) of the recurrence\n    d = 1 + aa * d;\n    if (Math.abs(d) < fpmin)\n      d = fpmin;\n    c = 1 + aa / c;\n    if (Math.abs(c) < fpmin)\n      c = fpmin;\n    d = 1 / d;\n    h *= d * c;\n    aa = -(a + m) * (qab + m) * x / ((a + m2) * (qap + m2));\n    // Next step of the recurrence (the odd one)\n    d = 1 + aa * d;\n    if (Math.abs(d) < fpmin)\n      d = fpmin;\n    c = 1 + aa / c;\n    if (Math.abs(c) < fpmin)\n      c = fpmin;\n    d = 1 / d;\n    del = d * c;\n    h *= del;\n    if (Math.abs(del - 1.0) < 3e-7)\n      break;\n  }\n\n  return h;\n};\n\n\n// Returns the inverse of the lower regularized inomplete gamma function\njStat.gammapinv = function gammapinv(p, a) {\n  var j = 0;\n  var a1 = a - 1;\n  var EPS = 1e-8;\n  var gln = jStat.gammaln(a);\n  var x, err, t, u, pp, lna1, afac;\n\n  if (p >= 1)\n    return Math.max(100, a + 100 * Math.sqrt(a));\n  if (p <= 0)\n    return 0;\n  if (a > 1) {\n    lna1 = Math.log(a1);\n    afac = Math.exp(a1 * (lna1 - 1) - gln);\n    pp = (p < 0.5) ? p : 1 - p;\n    t = Math.sqrt(-2 * Math.log(pp));\n    x = (2.30753 + t * 0.27061) / (1 + t * (0.99229 + t * 0.04481)) - t;\n    if (p < 0.5)\n      x = -x;\n    x = Math.max(1e-3,\n                 a * Math.pow(1 - 1 / (9 * a) - x / (3 * Math.sqrt(a)), 3));\n  } else {\n    t = 1 - a * (0.253 + a * 0.12);\n    if (p < t)\n      x = Math.pow(p / t, 1 / a);\n    else\n      x = 1 - Math.log(1 - (p - t) / (1 - t));\n  }\n\n  for(; j < 12; j++) {\n    if (x <= 0)\n      return 0;\n    err = jStat.lowRegGamma(a, x) - p;\n    if (a > 1)\n      t = afac * Math.exp(-(x - a1) + a1 * (Math.log(x) - lna1));\n    else\n      t = Math.exp(-x + a1 * Math.log(x) - gln);\n    u = err / t;\n    x -= (t = u / (1 - 0.5 * Math.min(1, u * ((a - 1) / x - 1))));\n    if (x <= 0)\n      x = 0.5 * (x + t);\n    if (Math.abs(t) < EPS * x)\n      break;\n  }\n\n  return x;\n};\n\n\n// Returns the error function erf(x)\njStat.erf = function erf(x) {\n  var cof = [-1.3026537197817094, 6.4196979235649026e-1, 1.9476473204185836e-2,\n             -9.561514786808631e-3, -9.46595344482036e-4, 3.66839497852761e-4,\n             4.2523324806907e-5, -2.0278578112534e-5, -1.624290004647e-6,\n             1.303655835580e-6, 1.5626441722e-8, -8.5238095915e-8,\n             6.529054439e-9, 5.059343495e-9, -9.91364156e-10,\n             -2.27365122e-10, 9.6467911e-11, 2.394038e-12,\n             -6.886027e-12, 8.94487e-13, 3.13092e-13,\n             -1.12708e-13, 3.81e-16, 7.106e-15,\n             -1.523e-15, -9.4e-17, 1.21e-16,\n             -2.8e-17];\n  var j = cof.length - 1;\n  var isneg = false;\n  var d = 0;\n  var dd = 0;\n  var t, ty, tmp, res;\n\n  if (x < 0) {\n    x = -x;\n    isneg = true;\n  }\n\n  t = 2 / (2 + x);\n  ty = 4 * t - 2;\n\n  for(; j > 0; j--) {\n    tmp = d;\n    d = ty * d - dd + cof[j];\n    dd = tmp;\n  }\n\n  res = t * Math.exp(-x * x + 0.5 * (cof[0] + ty * d) - dd);\n  return isneg ? res - 1 : 1 - res;\n};\n\n\n// Returns the complmentary error function erfc(x)\njStat.erfc = function erfc(x) {\n  return 1 - jStat.erf(x);\n};\n\n\n// Returns the inverse of the complementary error function\njStat.erfcinv = function erfcinv(p) {\n  var j = 0;\n  var x, err, t, pp;\n  if (p >= 2)\n    return -100;\n  if (p <= 0)\n    return 100;\n  pp = (p < 1) ? p : 2 - p;\n  t = Math.sqrt(-2 * Math.log(pp / 2));\n  x = -0.70711 * ((2.30753 + t * 0.27061) /\n                  (1 + t * (0.99229 + t * 0.04481)) - t);\n  for (; j < 2; j++) {\n    err = jStat.erfc(x) - pp;\n    x += err / (1.12837916709551257 * Math.exp(-x * x) - x * err);\n  }\n  return (p < 1) ? x : -x;\n};\n\n\n// Returns the inverse of the incomplete beta function\njStat.ibetainv = function ibetainv(p, a, b) {\n  var EPS = 1e-8;\n  var a1 = a - 1;\n  var b1 = b - 1;\n  var j = 0;\n  var lna, lnb, pp, t, u, err, x, al, h, w, afac;\n  if (p <= 0)\n    return 0;\n  if (p >= 1)\n    return 1;\n  if (a >= 1 && b >= 1) {\n    pp = (p < 0.5) ? p : 1 - p;\n    t = Math.sqrt(-2 * Math.log(pp));\n    x = (2.30753 + t * 0.27061) / (1 + t* (0.99229 + t * 0.04481)) - t;\n    if (p < 0.5)\n      x = -x;\n    al = (x * x - 3) / 6;\n    h = 2 / (1 / (2 * a - 1)  + 1 / (2 * b - 1));\n    w = (x * Math.sqrt(al + h) / h) - (1 / (2 * b - 1) - 1 / (2 * a - 1)) *\n        (al + 5 / 6 - 2 / (3 * h));\n    x = a / (a + b * Math.exp(2 * w));\n  } else {\n    lna = Math.log(a / (a + b));\n    lnb = Math.log(b / (a + b));\n    t = Math.exp(a * lna) / a;\n    u = Math.exp(b * lnb) / b;\n    w = t + u;\n    if (p < t / w)\n      x = Math.pow(a * w * p, 1 / a);\n    else\n      x = 1 - Math.pow(b * w * (1 - p), 1 / b);\n  }\n  afac = -jStat.gammaln(a) - jStat.gammaln(b) + jStat.gammaln(a + b);\n  for(; j < 10; j++) {\n    if (x === 0 || x === 1)\n      return x;\n    err = jStat.ibeta(x, a, b) - p;\n    t = Math.exp(a1 * Math.log(x) + b1 * Math.log(1 - x) + afac);\n    u = err / t;\n    x -= (t = u / (1 - 0.5 * Math.min(1, u * (a1 / x - b1 / (1 - x)))));\n    if (x <= 0)\n      x = 0.5 * (x + t);\n    if (x >= 1)\n      x = 0.5 * (x + t + 1);\n    if (Math.abs(t) < EPS * x && j > 0)\n      break;\n  }\n  return x;\n};\n\n\n// Returns the incomplete beta function I_x(a,b)\njStat.ibeta = function ibeta(x, a, b) {\n  // Factors in front of the continued fraction.\n  var bt = (x === 0 || x === 1) ?  0 :\n    Math.exp(jStat.gammaln(a + b) - jStat.gammaln(a) -\n             jStat.gammaln(b) + a * Math.log(x) + b *\n             Math.log(1 - x));\n  if (x < 0 || x > 1)\n    return false;\n  if (x < (a + 1) / (a + b + 2))\n    // Use continued fraction directly.\n    return bt * jStat.betacf(x, a, b) / a;\n  // else use continued fraction after making the symmetry transformation.\n  return 1 - bt * jStat.betacf(1 - x, b, a) / b;\n};\n\n\n// Returns a normal deviate (mu=0, sigma=1).\n// If n and m are specified it returns a object of normal deviates.\njStat.randn = function randn(n, m) {\n  var u, v, x, y, q;\n  if (!m)\n    m = n;\n  if (n)\n    return jStat.create(n, m, function() { return jStat.randn(); });\n  do {\n    u = jStat._random_fn();\n    v = 1.7156 * (jStat._random_fn() - 0.5);\n    x = u - 0.449871;\n    y = Math.abs(v) + 0.386595;\n    q = x * x + y * (0.19600 * y - 0.25472 * x);\n  } while (q > 0.27597 && (q > 0.27846 || v * v > -4 * Math.log(u) * u * u));\n  return v / u;\n};\n\n\n// Returns a gamma deviate by the method of Marsaglia and Tsang.\njStat.randg = function randg(shape, n, m) {\n  var oalph = shape;\n  var a1, a2, u, v, x, mat;\n  if (!m)\n    m = n;\n  if (!shape)\n    shape = 1;\n  if (n) {\n    mat = jStat.zeros(n,m);\n    mat.alter(function() { return jStat.randg(shape); });\n    return mat;\n  }\n  if (shape < 1)\n    shape += 1;\n  a1 = shape - 1 / 3;\n  a2 = 1 / Math.sqrt(9 * a1);\n  do {\n    do {\n      x = jStat.randn();\n      v = 1 + a2 * x;\n    } while(v <= 0);\n    v = v * v * v;\n    u = jStat._random_fn();\n  } while(u > 1 - 0.331 * Math.pow(x, 4) &&\n          Math.log(u) > 0.5 * x*x + a1 * (1 - v + Math.log(v)));\n  // alpha > 1\n  if (shape == oalph)\n    return a1 * v;\n  // alpha < 1\n  do {\n    u = jStat._random_fn();\n  } while(u === 0);\n  return Math.pow(u, 1 / oalph) * a1 * v;\n};\n\n\n// making use of static methods on the instance\n(function(funcs) {\n  for (var i = 0; i < funcs.length; i++) (function(passfunc) {\n    jStat.fn[passfunc] = function() {\n      return jStat(\n          jStat.map(this, function(value) { return jStat[passfunc](value); }));\n    }\n  })(funcs[i]);\n})('gammaln gammafn factorial factorialln'.split(' '));\n\n\n(function(funcs) {\n  for (var i = 0; i < funcs.length; i++) (function(passfunc) {\n    jStat.fn[passfunc] = function() {\n      return jStat(jStat[passfunc].apply(null, arguments));\n    };\n  })(funcs[i]);\n})('randn'.split(' '));\n\n}(jStat, Math));\n(function(jStat, Math) {\n\n// generate all distribution instance methods\n(function(list) {\n  for (var i = 0; i < list.length; i++) (function(func) {\n    // distribution instance method\n    jStat[func] = function f(a, b, c) {\n      if (!(this instanceof f))\n        return new f(a, b, c);\n      this._a = a;\n      this._b = b;\n      this._c = c;\n      return this;\n    };\n    // distribution method to be used on a jStat instance\n    jStat.fn[func] = function(a, b, c) {\n      var newthis = jStat[func](a, b, c);\n      newthis.data = this;\n      return newthis;\n    };\n    // sample instance method\n    jStat[func].prototype.sample = function(arr) {\n      var a = this._a;\n      var b = this._b;\n      var c = this._c;\n      if (arr)\n        return jStat.alter(arr, function() {\n          return jStat[func].sample(a, b, c);\n        });\n      else\n        return jStat[func].sample(a, b, c);\n    };\n    // generate the pdf, cdf and inv instance methods\n    (function(vals) {\n      for (var i = 0; i < vals.length; i++) (function(fnfunc) {\n        jStat[func].prototype[fnfunc] = function(x) {\n          var a = this._a;\n          var b = this._b;\n          var c = this._c;\n          if (!x && x !== 0)\n            x = this.data;\n          if (typeof x !== 'number') {\n            return jStat.fn.map.call(x, function(x) {\n              return jStat[func][fnfunc](x, a, b, c);\n            });\n          }\n          return jStat[func][fnfunc](x, a, b, c);\n        };\n      })(vals[i]);\n    })('pdf cdf inv'.split(' '));\n    // generate the mean, median, mode and variance instance methods\n    (function(vals) {\n      for (var i = 0; i < vals.length; i++) (function(fnfunc) {\n        jStat[func].prototype[fnfunc] = function() {\n          return jStat[func][fnfunc](this._a, this._b, this._c);\n        };\n      })(vals[i]);\n    })('mean median mode variance'.split(' '));\n  })(list[i]);\n})((\n  'beta centralF cauchy chisquare exponential gamma invgamma kumaraswamy ' +\n  'laplace lognormal noncentralt normal pareto studentt weibull uniform ' +\n  'binomial negbin hypgeom poisson triangular tukey arcsine'\n).split(' '));\n\n\n\n// extend beta function with static methods\njStat.extend(jStat.beta, {\n  pdf: function pdf(x, alpha, beta) {\n    // PDF is zero outside the support\n    if (x > 1 || x < 0)\n      return 0;\n    // PDF is one for the uniform case\n    if (alpha == 1 && beta == 1)\n      return 1;\n\n    if (alpha < 512 && beta < 512) {\n      return (Math.pow(x, alpha - 1) * Math.pow(1 - x, beta - 1)) /\n          jStat.betafn(alpha, beta);\n    } else {\n      return Math.exp((alpha - 1) * Math.log(x) +\n                      (beta - 1) * Math.log(1 - x) -\n                      jStat.betaln(alpha, beta));\n    }\n  },\n\n  cdf: function cdf(x, alpha, beta) {\n    return (x > 1 || x < 0) ? (x > 1) * 1 : jStat.ibeta(x, alpha, beta);\n  },\n\n  inv: function inv(x, alpha, beta) {\n    return jStat.ibetainv(x, alpha, beta);\n  },\n\n  mean: function mean(alpha, beta) {\n    return alpha / (alpha + beta);\n  },\n\n  median: function median(alpha, beta) {\n    return jStat.ibetainv(0.5, alpha, beta);\n  },\n\n  mode: function mode(alpha, beta) {\n    return (alpha - 1 ) / ( alpha + beta - 2);\n  },\n\n  // return a random sample\n  sample: function sample(alpha, beta) {\n    var u = jStat.randg(alpha);\n    return u / (u + jStat.randg(beta));\n  },\n\n  variance: function variance(alpha, beta) {\n    return (alpha * beta) / (Math.pow(alpha + beta, 2) * (alpha + beta + 1));\n  }\n});\n\n// extend F function with static methods\njStat.extend(jStat.centralF, {\n  // This implementation of the pdf function avoids float overflow\n  // See the way that R calculates this value:\n  // https://svn.r-project.org/R/trunk/src/nmath/df.c\n  pdf: function pdf(x, df1, df2) {\n    var p, q, f;\n\n    if (x < 0)\n      return 0;\n\n    if (df1 <= 2) {\n      if (x === 0 && df1 < 2) {\n        return Infinity;\n      }\n      if (x === 0 && df1 === 2) {\n        return 1;\n      }\n      return (1 / jStat.betafn(df1 / 2, df2 / 2)) *\n              Math.pow(df1 / df2, df1 / 2) *\n              Math.pow(x, (df1/2) - 1) *\n              Math.pow((1 + (df1 / df2) * x), -(df1 + df2) / 2);\n    }\n\n    p = (df1 * x) / (df2 + x * df1);\n    q = df2 / (df2 + x * df1);\n    f = df1 * q / 2.0;\n    return f * jStat.binomial.pdf((df1 - 2) / 2, (df1 + df2 - 2) / 2, p);\n  },\n\n  cdf: function cdf(x, df1, df2) {\n    if (x < 0)\n      return 0;\n    return jStat.ibeta((df1 * x) / (df1 * x + df2), df1 / 2, df2 / 2);\n  },\n\n  inv: function inv(x, df1, df2) {\n    return df2 / (df1 * (1 / jStat.ibetainv(x, df1 / 2, df2 / 2) - 1));\n  },\n\n  mean: function mean(df1, df2) {\n    return (df2 > 2) ? df2 / (df2 - 2) : undefined;\n  },\n\n  mode: function mode(df1, df2) {\n    return (df1 > 2) ? (df2 * (df1 - 2)) / (df1 * (df2 + 2)) : undefined;\n  },\n\n  // return a random sample\n  sample: function sample(df1, df2) {\n    var x1 = jStat.randg(df1 / 2) * 2;\n    var x2 = jStat.randg(df2 / 2) * 2;\n    return (x1 / df1) / (x2 / df2);\n  },\n\n  variance: function variance(df1, df2) {\n    if (df2 <= 4)\n      return undefined;\n    return 2 * df2 * df2 * (df1 + df2 - 2) /\n        (df1 * (df2 - 2) * (df2 - 2) * (df2 - 4));\n  }\n});\n\n\n// extend cauchy function with static methods\njStat.extend(jStat.cauchy, {\n  pdf: function pdf(x, local, scale) {\n    if (scale < 0) { return 0; }\n\n    return (scale / (Math.pow(x - local, 2) + Math.pow(scale, 2))) / Math.PI;\n  },\n\n  cdf: function cdf(x, local, scale) {\n    return Math.atan((x - local) / scale) / Math.PI + 0.5;\n  },\n\n  inv: function(p, local, scale) {\n    return local + scale * Math.tan(Math.PI * (p - 0.5));\n  },\n\n  median: function median(local/*, scale*/) {\n    return local;\n  },\n\n  mode: function mode(local/*, scale*/) {\n    return local;\n  },\n\n  sample: function sample(local, scale) {\n    return jStat.randn() *\n        Math.sqrt(1 / (2 * jStat.randg(0.5))) * scale + local;\n  }\n});\n\n\n\n// extend chisquare function with static methods\njStat.extend(jStat.chisquare, {\n  pdf: function pdf(x, dof) {\n    if (x < 0)\n      return 0;\n    return (x === 0 && dof === 2) ? 0.5 :\n        Math.exp((dof / 2 - 1) * Math.log(x) - x / 2 - (dof / 2) *\n                 Math.log(2) - jStat.gammaln(dof / 2));\n  },\n\n  cdf: function cdf(x, dof) {\n    if (x < 0)\n      return 0;\n    return jStat.lowRegGamma(dof / 2, x / 2);\n  },\n\n  inv: function(p, dof) {\n    return 2 * jStat.gammapinv(p, 0.5 * dof);\n  },\n\n  mean : function(dof) {\n    return dof;\n  },\n\n  // TODO: this is an approximation (is there a better way?)\n  median: function median(dof) {\n    return dof * Math.pow(1 - (2 / (9 * dof)), 3);\n  },\n\n  mode: function mode(dof) {\n    return (dof - 2 > 0) ? dof - 2 : 0;\n  },\n\n  sample: function sample(dof) {\n    return jStat.randg(dof / 2) * 2;\n  },\n\n  variance: function variance(dof) {\n    return 2 * dof;\n  }\n});\n\n\n\n// extend exponential function with static methods\njStat.extend(jStat.exponential, {\n  pdf: function pdf(x, rate) {\n    return x < 0 ? 0 : rate * Math.exp(-rate * x);\n  },\n\n  cdf: function cdf(x, rate) {\n    return x < 0 ? 0 : 1 - Math.exp(-rate * x);\n  },\n\n  inv: function(p, rate) {\n    return -Math.log(1 - p) / rate;\n  },\n\n  mean : function(rate) {\n    return 1 / rate;\n  },\n\n  median: function (rate) {\n    return (1 / rate) * Math.log(2);\n  },\n\n  mode: function mode(/*rate*/) {\n    return 0;\n  },\n\n  sample: function sample(rate) {\n    return -1 / rate * Math.log(jStat._random_fn());\n  },\n\n  variance : function(rate) {\n    return Math.pow(rate, -2);\n  }\n});\n\n\n\n// extend gamma function with static methods\njStat.extend(jStat.gamma, {\n  pdf: function pdf(x, shape, scale) {\n    if (x < 0)\n      return 0;\n    return (x === 0 && shape === 1) ? 1 / scale :\n            Math.exp((shape - 1) * Math.log(x) - x / scale -\n                    jStat.gammaln(shape) - shape * Math.log(scale));\n  },\n\n  cdf: function cdf(x, shape, scale) {\n    if (x < 0)\n      return 0;\n    return jStat.lowRegGamma(shape, x / scale);\n  },\n\n  inv: function(p, shape, scale) {\n    return jStat.gammapinv(p, shape) * scale;\n  },\n\n  mean : function(shape, scale) {\n    return shape * scale;\n  },\n\n  mode: function mode(shape, scale) {\n    if(shape > 1) return (shape - 1) * scale;\n    return undefined;\n  },\n\n  sample: function sample(shape, scale) {\n    return jStat.randg(shape) * scale;\n  },\n\n  variance: function variance(shape, scale) {\n    return shape * scale * scale;\n  }\n});\n\n// extend inverse gamma function with static methods\njStat.extend(jStat.invgamma, {\n  pdf: function pdf(x, shape, scale) {\n    if (x <= 0)\n      return 0;\n    return Math.exp(-(shape + 1) * Math.log(x) - scale / x -\n                    jStat.gammaln(shape) + shape * Math.log(scale));\n  },\n\n  cdf: function cdf(x, shape, scale) {\n    if (x <= 0)\n      return 0;\n    return 1 - jStat.lowRegGamma(shape, scale / x);\n  },\n\n  inv: function(p, shape, scale) {\n    return scale / jStat.gammapinv(1 - p, shape);\n  },\n\n  mean : function(shape, scale) {\n    return (shape > 1) ? scale / (shape - 1) : undefined;\n  },\n\n  mode: function mode(shape, scale) {\n    return scale / (shape + 1);\n  },\n\n  sample: function sample(shape, scale) {\n    return scale / jStat.randg(shape);\n  },\n\n  variance: function variance(shape, scale) {\n    if (shape <= 2)\n      return undefined;\n    return scale * scale / ((shape - 1) * (shape - 1) * (shape - 2));\n  }\n});\n\n\n// extend kumaraswamy function with static methods\njStat.extend(jStat.kumaraswamy, {\n  pdf: function pdf(x, alpha, beta) {\n    if (x === 0 && alpha === 1)\n      return beta;\n    else if (x === 1 && beta === 1)\n      return alpha;\n    return Math.exp(Math.log(alpha) + Math.log(beta) + (alpha - 1) *\n                    Math.log(x) + (beta - 1) *\n                    Math.log(1 - Math.pow(x, alpha)));\n  },\n\n  cdf: function cdf(x, alpha, beta) {\n    if (x < 0)\n      return 0;\n    else if (x > 1)\n      return 1;\n    return (1 - Math.pow(1 - Math.pow(x, alpha), beta));\n  },\n\n  inv: function inv(p, alpha, beta) {\n    return Math.pow(1 - Math.pow(1 - p, 1 / beta), 1 / alpha);\n  },\n\n  mean : function(alpha, beta) {\n    return (beta * jStat.gammafn(1 + 1 / alpha) *\n            jStat.gammafn(beta)) / (jStat.gammafn(1 + 1 / alpha + beta));\n  },\n\n  median: function median(alpha, beta) {\n    return Math.pow(1 - Math.pow(2, -1 / beta), 1 / alpha);\n  },\n\n  mode: function mode(alpha, beta) {\n    if (!(alpha >= 1 && beta >= 1 && (alpha !== 1 && beta !== 1)))\n      return undefined;\n    return Math.pow((alpha - 1) / (alpha * beta - 1), 1 / alpha);\n  },\n\n  variance: function variance(/*alpha, beta*/) {\n    throw new Error('variance not yet implemented');\n    // TODO: complete this\n  }\n});\n\n\n\n// extend lognormal function with static methods\njStat.extend(jStat.lognormal, {\n  pdf: function pdf(x, mu, sigma) {\n    if (x <= 0)\n      return 0;\n    return Math.exp(-Math.log(x) - 0.5 * Math.log(2 * Math.PI) -\n                    Math.log(sigma) - Math.pow(Math.log(x) - mu, 2) /\n                    (2 * sigma * sigma));\n  },\n\n  cdf: function cdf(x, mu, sigma) {\n    if (x < 0)\n      return 0;\n    return 0.5 +\n        (0.5 * jStat.erf((Math.log(x) - mu) / Math.sqrt(2 * sigma * sigma)));\n  },\n\n  inv: function(p, mu, sigma) {\n    return Math.exp(-1.41421356237309505 * sigma * jStat.erfcinv(2 * p) + mu);\n  },\n\n  mean: function mean(mu, sigma) {\n    return Math.exp(mu + sigma * sigma / 2);\n  },\n\n  median: function median(mu/*, sigma*/) {\n    return Math.exp(mu);\n  },\n\n  mode: function mode(mu, sigma) {\n    return Math.exp(mu - sigma * sigma);\n  },\n\n  sample: function sample(mu, sigma) {\n    return Math.exp(jStat.randn() * sigma + mu);\n  },\n\n  variance: function variance(mu, sigma) {\n    return (Math.exp(sigma * sigma) - 1) * Math.exp(2 * mu + sigma * sigma);\n  }\n});\n\n\n\n// extend noncentralt function with static methods\njStat.extend(jStat.noncentralt, {\n  pdf: function pdf(x, dof, ncp) {\n    var tol = 1e-14;\n    if (Math.abs(ncp) < tol)  // ncp approx 0; use student-t\n      return jStat.studentt.pdf(x, dof)\n\n    if (Math.abs(x) < tol) {  // different formula for x == 0\n      return Math.exp(jStat.gammaln((dof + 1) / 2) - ncp * ncp / 2 -\n                      0.5 * Math.log(Math.PI * dof) - jStat.gammaln(dof / 2));\n    }\n\n    // formula for x != 0\n    return dof / x *\n        (jStat.noncentralt.cdf(x * Math.sqrt(1 + 2 / dof), dof+2, ncp) -\n         jStat.noncentralt.cdf(x, dof, ncp));\n  },\n\n  cdf: function cdf(x, dof, ncp) {\n    var tol = 1e-14;\n    var min_iterations = 200;\n\n    if (Math.abs(ncp) < tol)  // ncp approx 0; use student-t\n      return jStat.studentt.cdf(x, dof);\n\n    // turn negative x into positive and flip result afterwards\n    var flip = false;\n    if (x < 0) {\n      flip = true;\n      ncp = -ncp;\n    }\n\n    var prob = jStat.normal.cdf(-ncp, 0, 1);\n    var value = tol + 1;\n    // use value at last two steps to determine convergence\n    var lastvalue = value;\n    var y = x * x / (x * x + dof);\n    var j = 0;\n    var p = Math.exp(-ncp * ncp / 2);\n    var q = Math.exp(-ncp * ncp / 2 - 0.5 * Math.log(2) -\n                     jStat.gammaln(3 / 2)) * ncp;\n    while (j < min_iterations || lastvalue > tol || value > tol) {\n      lastvalue = value;\n      if (j > 0) {\n        p *= (ncp * ncp) / (2 * j);\n        q *= (ncp * ncp) / (2 * (j + 1 / 2));\n      }\n      value = p * jStat.beta.cdf(y, j + 0.5, dof / 2) +\n          q * jStat.beta.cdf(y, j+1, dof/2);\n      prob += 0.5 * value;\n      j++;\n    }\n\n    return flip ? (1 - prob) : prob;\n  }\n});\n\n\n// extend normal function with static methods\njStat.extend(jStat.normal, {\n  pdf: function pdf(x, mean, std) {\n    return Math.exp(-0.5 * Math.log(2 * Math.PI) -\n                    Math.log(std) - Math.pow(x - mean, 2) / (2 * std * std));\n  },\n\n  cdf: function cdf(x, mean, std) {\n    return 0.5 * (1 + jStat.erf((x - mean) / Math.sqrt(2 * std * std)));\n  },\n\n  inv: function(p, mean, std) {\n    return -1.41421356237309505 * std * jStat.erfcinv(2 * p) + mean;\n  },\n\n  mean : function(mean/*, std*/) {\n    return mean;\n  },\n\n  median: function median(mean/*, std*/) {\n    return mean;\n  },\n\n  mode: function (mean/*, std*/) {\n    return mean;\n  },\n\n  sample: function sample(mean, std) {\n    return jStat.randn() * std + mean;\n  },\n\n  variance : function(mean, std) {\n    return std * std;\n  }\n});\n\n\n\n// extend pareto function with static methods\njStat.extend(jStat.pareto, {\n  pdf: function pdf(x, scale, shape) {\n    if (x < scale)\n      return 0;\n    return (shape * Math.pow(scale, shape)) / Math.pow(x, shape + 1);\n  },\n\n  cdf: function cdf(x, scale, shape) {\n    if (x < scale)\n      return 0;\n    return 1 - Math.pow(scale / x, shape);\n  },\n\n  inv: function inv(p, scale, shape) {\n    return scale / Math.pow(1 - p, 1 / shape);\n  },\n\n  mean: function mean(scale, shape) {\n    if (shape <= 1)\n      return undefined;\n    return (shape * Math.pow(scale, shape)) / (shape - 1);\n  },\n\n  median: function median(scale, shape) {\n    return scale * (shape * Math.SQRT2);\n  },\n\n  mode: function mode(scale/*, shape*/) {\n    return scale;\n  },\n\n  variance : function(scale, shape) {\n    if (shape <= 2)\n      return undefined;\n    return (scale*scale * shape) / (Math.pow(shape - 1, 2) * (shape - 2));\n  }\n});\n\n\n\n// extend studentt function with static methods\njStat.extend(jStat.studentt, {\n  pdf: function pdf(x, dof) {\n    dof = dof > 1e100 ? 1e100 : dof;\n    return (1/(Math.sqrt(dof) * jStat.betafn(0.5, dof/2))) *\n        Math.pow(1 + ((x * x) / dof), -((dof + 1) / 2));\n  },\n\n  cdf: function cdf(x, dof) {\n    var dof2 = dof / 2;\n    return jStat.ibeta((x + Math.sqrt(x * x + dof)) /\n                       (2 * Math.sqrt(x * x + dof)), dof2, dof2);\n  },\n\n  inv: function(p, dof) {\n    var x = jStat.ibetainv(2 * Math.min(p, 1 - p), 0.5 * dof, 0.5);\n    x = Math.sqrt(dof * (1 - x) / x);\n    return (p > 0.5) ? x : -x;\n  },\n\n  mean: function mean(dof) {\n    return (dof > 1) ? 0 : undefined;\n  },\n\n  median: function median(/*dof*/) {\n    return 0;\n  },\n\n  mode: function mode(/*dof*/) {\n    return 0;\n  },\n\n  sample: function sample(dof) {\n    return jStat.randn() * Math.sqrt(dof / (2 * jStat.randg(dof / 2)));\n  },\n\n  variance: function variance(dof) {\n    return (dof  > 2) ? dof / (dof - 2) : (dof > 1) ? Infinity : undefined;\n  }\n});\n\n\n\n// extend weibull function with static methods\njStat.extend(jStat.weibull, {\n  pdf: function pdf(x, scale, shape) {\n    if (x < 0 || scale < 0 || shape < 0)\n      return 0;\n    return (shape / scale) * Math.pow((x / scale), (shape - 1)) *\n        Math.exp(-(Math.pow((x / scale), shape)));\n  },\n\n  cdf: function cdf(x, scale, shape) {\n    return x < 0 ? 0 : 1 - Math.exp(-Math.pow((x / scale), shape));\n  },\n\n  inv: function(p, scale, shape) {\n    return scale * Math.pow(-Math.log(1 - p), 1 / shape);\n  },\n\n  mean : function(scale, shape) {\n    return scale * jStat.gammafn(1 + 1 / shape);\n  },\n\n  median: function median(scale, shape) {\n    return scale * Math.pow(Math.log(2), 1 / shape);\n  },\n\n  mode: function mode(scale, shape) {\n    if (shape <= 1)\n      return 0;\n    return scale * Math.pow((shape - 1) / shape, 1 / shape);\n  },\n\n  sample: function sample(scale, shape) {\n    return scale * Math.pow(-Math.log(jStat._random_fn()), 1 / shape);\n  },\n\n  variance: function variance(scale, shape) {\n    return scale * scale * jStat.gammafn(1 + 2 / shape) -\n        Math.pow(jStat.weibull.mean(scale, shape), 2);\n  }\n});\n\n\n\n// extend uniform function with static methods\njStat.extend(jStat.uniform, {\n  pdf: function pdf(x, a, b) {\n    return (x < a || x > b) ? 0 : 1 / (b - a);\n  },\n\n  cdf: function cdf(x, a, b) {\n    if (x < a)\n      return 0;\n    else if (x < b)\n      return (x - a) / (b - a);\n    return 1;\n  },\n\n  inv: function(p, a, b) {\n    return a + (p * (b - a));\n  },\n\n  mean: function mean(a, b) {\n    return 0.5 * (a + b);\n  },\n\n  median: function median(a, b) {\n    return jStat.mean(a, b);\n  },\n\n  mode: function mode(/*a, b*/) {\n    throw new Error('mode is not yet implemented');\n  },\n\n  sample: function sample(a, b) {\n    return (a / 2 + b / 2) + (b / 2 - a / 2) * (2 * jStat._random_fn() - 1);\n  },\n\n  variance: function variance(a, b) {\n    return Math.pow(b - a, 2) / 12;\n  }\n});\n\n\n// Got this from http://www.math.ucla.edu/~tom/distributions/binomial.html\nfunction betinc(x, a, b, eps) {\n  var a0 = 0;\n  var b0 = 1;\n  var a1 = 1;\n  var b1 = 1;\n  var m9 = 0;\n  var a2 = 0;\n  var c9;\n\n  while (Math.abs((a1 - a2) / a1) > eps) {\n    a2 = a1;\n    c9 = -(a + m9) * (a + b + m9) * x / (a + 2 * m9) / (a + 2 * m9 + 1);\n    a0 = a1 + c9 * a0;\n    b0 = b1 + c9 * b0;\n    m9 = m9 + 1;\n    c9 = m9 * (b - m9) * x / (a + 2 * m9 - 1) / (a + 2 * m9);\n    a1 = a0 + c9 * a1;\n    b1 = b0 + c9 * b1;\n    a0 = a0 / b1;\n    b0 = b0 / b1;\n    a1 = a1 / b1;\n    b1 = 1;\n  }\n\n  return a1 / a;\n}\n\n\n// extend uniform function with static methods\njStat.extend(jStat.binomial, {\n  pdf: function pdf(k, n, p) {\n    return (p === 0 || p === 1) ?\n      ((n * p) === k ? 1 : 0) :\n      jStat.combination(n, k) * Math.pow(p, k) * Math.pow(1 - p, n - k);\n  },\n\n  cdf: function cdf(x, n, p) {\n    var betacdf;\n    var eps = 1e-10;\n\n    if (x < 0)\n      return 0;\n    if (x >= n)\n      return 1;\n    if (p < 0 || p > 1 || n <= 0)\n      return NaN;\n\n    x = Math.floor(x);\n    var z = p;\n    var a = x + 1;\n    var b = n - x;\n    var s = a + b;\n    var bt = Math.exp(jStat.gammaln(s) - jStat.gammaln(b) -\n                      jStat.gammaln(a) + a * Math.log(z) + b * Math.log(1 - z));\n\n    if (z < (a + 1) / (s + 2))\n      betacdf = bt * betinc(z, a, b, eps);\n    else\n      betacdf = 1 - bt * betinc(1 - z, b, a, eps);\n\n    return Math.round((1 - betacdf) * (1 / eps)) / (1 / eps);\n  }\n});\n\n\n\n// extend uniform function with static methods\njStat.extend(jStat.negbin, {\n  pdf: function pdf(k, r, p) {\n    if (k !== k >>> 0)\n      return false;\n    if (k < 0)\n      return 0;\n    return jStat.combination(k + r - 1, r - 1) *\n        Math.pow(1 - p, k) * Math.pow(p, r);\n  },\n\n  cdf: function cdf(x, r, p) {\n    var sum = 0,\n    k = 0;\n    if (x < 0) return 0;\n    for (; k <= x; k++) {\n      sum += jStat.negbin.pdf(k, r, p);\n    }\n    return sum;\n  }\n});\n\n\n\n// extend uniform function with static methods\njStat.extend(jStat.hypgeom, {\n  pdf: function pdf(k, N, m, n) {\n    // Hypergeometric PDF.\n\n    // A simplification of the CDF algorithm below.\n\n    // k = number of successes drawn\n    // N = population size\n    // m = number of successes in population\n    // n = number of items drawn from population\n\n    if(k !== k | 0) {\n      return false;\n    } else if(k < 0 || k < m - (N - n)) {\n      // It's impossible to have this few successes drawn.\n      return 0;\n    } else if(k > n || k > m) {\n      // It's impossible to have this many successes drawn.\n      return 0;\n    } else if (m * 2 > N) {\n      // More than half the population is successes.\n\n      if(n * 2 > N) {\n        // More than half the population is sampled.\n\n        return jStat.hypgeom.pdf(N - m - n + k, N, N - m, N - n)\n      } else {\n        // Half or less of the population is sampled.\n\n        return jStat.hypgeom.pdf(n - k, N, N - m, n);\n      }\n\n    } else if(n * 2 > N) {\n      // Half or less is successes.\n\n      return jStat.hypgeom.pdf(m - k, N, m, N - n);\n\n    } else if(m < n) {\n      // We want to have the number of things sampled to be less than the\n      // successes available. So swap the definitions of successful and sampled.\n      return jStat.hypgeom.pdf(k, N, n, m);\n    } else {\n      // If we get here, half or less of the population was sampled, half or\n      // less of it was successes, and we had fewer sampled things than\n      // successes. Now we can do this complicated iterative algorithm in an\n      // efficient way.\n\n      // The basic premise of the algorithm is that we partially normalize our\n      // intermediate product to keep it in a numerically good region, and then\n      // finish the normalization at the end.\n\n      // This variable holds the scaled probability of the current number of\n      // successes.\n      var scaledPDF = 1;\n\n      // This keeps track of how much we have normalized.\n      var samplesDone = 0;\n\n      for(var i = 0; i < k; i++) {\n        // For every possible number of successes up to that observed...\n\n        while(scaledPDF > 1 && samplesDone < n) {\n          // Intermediate result is growing too big. Apply some of the\n          // normalization to shrink everything.\n\n          scaledPDF *= 1 - (m / (N - samplesDone));\n\n          // Say we've normalized by this sample already.\n          samplesDone++;\n        }\n\n        // Work out the partially-normalized hypergeometric PDF for the next\n        // number of successes\n        scaledPDF *= (n - i) * (m - i) / ((i + 1) * (N - m - n + i + 1));\n      }\n\n      for(; samplesDone < n; samplesDone++) {\n        // Apply all the rest of the normalization\n        scaledPDF *= 1 - (m / (N - samplesDone));\n      }\n\n      // Bound answer sanely before returning.\n      return Math.min(1, Math.max(0, scaledPDF));\n    }\n  },\n\n  cdf: function cdf(x, N, m, n) {\n    // Hypergeometric CDF.\n\n    // This algorithm is due to Prof. Thomas S. Ferguson, <<EMAIL>>,\n    // and comes from his hypergeometric test calculator at\n    // <http://www.math.ucla.edu/~tom/distributions/Hypergeometric.html>.\n\n    // x = number of successes drawn\n    // N = population size\n    // m = number of successes in population\n    // n = number of items drawn from population\n\n    if(x < 0 || x < m - (N - n)) {\n      // It's impossible to have this few successes drawn or fewer.\n      return 0;\n    } else if(x >= n || x >= m) {\n      // We will always have this many successes or fewer.\n      return 1;\n    } else if (m * 2 > N) {\n      // More than half the population is successes.\n\n      if(n * 2 > N) {\n        // More than half the population is sampled.\n\n        return jStat.hypgeom.cdf(N - m - n + x, N, N - m, N - n)\n      } else {\n        // Half or less of the population is sampled.\n\n        return 1 - jStat.hypgeom.cdf(n - x - 1, N, N - m, n);\n      }\n\n    } else if(n * 2 > N) {\n      // Half or less is successes.\n\n      return 1 - jStat.hypgeom.cdf(m - x - 1, N, m, N - n);\n\n    } else if(m < n) {\n      // We want to have the number of things sampled to be less than the\n      // successes available. So swap the definitions of successful and sampled.\n      return jStat.hypgeom.cdf(x, N, n, m);\n    } else {\n      // If we get here, half or less of the population was sampled, half or\n      // less of it was successes, and we had fewer sampled things than\n      // successes. Now we can do this complicated iterative algorithm in an\n      // efficient way.\n\n      // The basic premise of the algorithm is that we partially normalize our\n      // intermediate sum to keep it in a numerically good region, and then\n      // finish the normalization at the end.\n\n      // Holds the intermediate, scaled total CDF.\n      var scaledCDF = 1;\n\n      // This variable holds the scaled probability of the current number of\n      // successes.\n      var scaledPDF = 1;\n\n      // This keeps track of how much we have normalized.\n      var samplesDone = 0;\n\n      for(var i = 0; i < x; i++) {\n        // For every possible number of successes up to that observed...\n\n        while(scaledCDF > 1 && samplesDone < n) {\n          // Intermediate result is growing too big. Apply some of the\n          // normalization to shrink everything.\n\n          var factor = 1 - (m / (N - samplesDone));\n\n          scaledPDF *= factor;\n          scaledCDF *= factor;\n\n          // Say we've normalized by this sample already.\n          samplesDone++;\n        }\n\n        // Work out the partially-normalized hypergeometric PDF for the next\n        // number of successes\n        scaledPDF *= (n - i) * (m - i) / ((i + 1) * (N - m - n + i + 1));\n\n        // Add to the CDF answer.\n        scaledCDF += scaledPDF;\n      }\n\n      for(; samplesDone < n; samplesDone++) {\n        // Apply all the rest of the normalization\n        scaledCDF *= 1 - (m / (N - samplesDone));\n      }\n\n      // Bound answer sanely before returning.\n      return Math.min(1, Math.max(0, scaledCDF));\n    }\n  }\n});\n\n\n\n// extend uniform function with static methods\njStat.extend(jStat.poisson, {\n  pdf: function pdf(k, l) {\n    if (l < 0 || (k % 1) !== 0 || k < 0) {\n      return 0;\n    }\n\n    return Math.pow(l, k) * Math.exp(-l) / jStat.factorial(k);\n  },\n\n  cdf: function cdf(x, l) {\n    var sumarr = [],\n    k = 0;\n    if (x < 0) return 0;\n    for (; k <= x; k++) {\n      sumarr.push(jStat.poisson.pdf(k, l));\n    }\n    return jStat.sum(sumarr);\n  },\n\n  mean : function(l) {\n    return l;\n  },\n\n  variance : function(l) {\n    return l;\n  },\n\n  sampleSmall: function sampleSmall(l) {\n    var p = 1, k = 0, L = Math.exp(-l);\n    do {\n      k++;\n      p *= jStat._random_fn();\n    } while (p > L);\n    return k - 1;\n  },\n\n  sampleLarge: function sampleLarge(l) {\n    var lam = l;\n    var k;\n    var U, V, slam, loglam, a, b, invalpha, vr, us;\n\n    slam = Math.sqrt(lam);\n    loglam = Math.log(lam);\n    b = 0.931 + 2.53 * slam;\n    a = -0.059 + 0.02483 * b;\n    invalpha = 1.1239 + 1.1328 / (b - 3.4);\n    vr = 0.9277 - 3.6224 / (b - 2);\n\n    while (1) {\n      U = Math.random() - 0.5;\n      V = Math.random();\n      us = 0.5 - Math.abs(U);\n      k = Math.floor((2 * a / us + b) * U + lam + 0.43);\n      if ((us >= 0.07) && (V <= vr)) {\n          return k;\n      }\n      if ((k < 0) || ((us < 0.013) && (V > us))) {\n          continue;\n      }\n      /* log(V) == log(0.0) ok here */\n      /* if U==0.0 so that us==0.0, log is ok since always returns */\n      if ((Math.log(V) + Math.log(invalpha) - Math.log(a / (us * us) + b)) <= (-lam + k * loglam - jStat.loggam(k + 1))) {\n          return k;\n      }\n    }\n  },\n\n  sample: function sample(l) {\n    if (l < 10)\n      return this.sampleSmall(l);\n    else\n      return this.sampleLarge(l);\n  }\n});\n\n// extend triangular function with static methods\njStat.extend(jStat.triangular, {\n  pdf: function pdf(x, a, b, c) {\n    if (b <= a || c < a || c > b) {\n      return NaN;\n    } else {\n      if (x < a || x > b) {\n        return 0;\n      } else if (x < c) {\n          return (2 * (x - a)) / ((b - a) * (c - a));\n      } else if (x === c) {\n          return (2 / (b - a));\n      } else { // x > c\n          return (2 * (b - x)) / ((b - a) * (b - c));\n      }\n    }\n  },\n\n  cdf: function cdf(x, a, b, c) {\n    if (b <= a || c < a || c > b)\n      return NaN;\n    if (x <= a)\n      return 0;\n    else if (x >= b)\n      return 1;\n    if (x <= c)\n      return Math.pow(x - a, 2) / ((b - a) * (c - a));\n    else // x > c\n      return 1 - Math.pow(b - x, 2) / ((b - a) * (b - c));\n  },\n\n  inv: function inv(p, a, b, c) {\n    if (b <= a || c < a || c > b) {\n      return NaN;\n    } else {\n      if (p <= ((c - a) / (b - a))) {\n        return a + (b - a) * Math.sqrt(p * ((c - a) / (b - a)));\n      } else { // p > ((c - a) / (b - a))\n        return a + (b - a) * (1 - Math.sqrt((1 - p) * (1 - ((c - a) / (b - a)))));\n      }\n    }\n  },\n\n  mean: function mean(a, b, c) {\n    return (a + b + c) / 3;\n  },\n\n  median: function median(a, b, c) {\n    if (c <= (a + b) / 2) {\n      return b - Math.sqrt((b - a) * (b - c)) / Math.sqrt(2);\n    } else if (c > (a + b) / 2) {\n      return a + Math.sqrt((b - a) * (c - a)) / Math.sqrt(2);\n    }\n  },\n\n  mode: function mode(a, b, c) {\n    return c;\n  },\n\n  sample: function sample(a, b, c) {\n    var u = jStat._random_fn();\n    if (u < ((c - a) / (b - a)))\n      return a + Math.sqrt(u * (b - a) * (c - a))\n    return b - Math.sqrt((1 - u) * (b - a) * (b - c));\n  },\n\n  variance: function variance(a, b, c) {\n    return (a * a + b * b + c * c - a * b - a * c - b * c) / 18;\n  }\n});\n\n\n// extend arcsine function with static methods\njStat.extend(jStat.arcsine, {\n  pdf: function pdf(x, a, b) {\n    if (b <= a) return NaN;\n\n    return (x <= a || x >= b) ? 0 :\n      (2 / Math.PI) *\n        Math.pow(Math.pow(b - a, 2) -\n                  Math.pow(2 * x - a - b, 2), -0.5);\n  },\n\n  cdf: function cdf(x, a, b) {\n    if (x < a)\n      return 0;\n    else if (x < b)\n      return (2 / Math.PI) * Math.asin(Math.sqrt((x - a)/(b - a)));\n    return 1;\n  },\n\n  inv: function(p, a, b) {\n    return a + (0.5 - 0.5 * Math.cos(Math.PI * p)) * (b - a);\n  },\n\n  mean: function mean(a, b) {\n    if (b <= a) return NaN;\n    return (a + b) / 2;\n  },\n\n  median: function median(a, b) {\n    if (b <= a) return NaN;\n    return (a + b) / 2;\n  },\n\n  mode: function mode(/*a, b*/) {\n    throw new Error('mode is not yet implemented');\n  },\n\n  sample: function sample(a, b) {\n    return ((a + b) / 2) + ((b - a) / 2) *\n      Math.sin(2 * Math.PI * jStat.uniform.sample(0, 1));\n  },\n\n  variance: function variance(a, b) {\n    if (b <= a) return NaN;\n    return Math.pow(b - a, 2) / 8;\n  }\n});\n\n\nfunction laplaceSign(x) { return x / Math.abs(x); }\n\njStat.extend(jStat.laplace, {\n  pdf: function pdf(x, mu, b) {\n    return (b <= 0) ? 0 : (Math.exp(-Math.abs(x - mu) / b)) / (2 * b);\n  },\n\n  cdf: function cdf(x, mu, b) {\n    if (b <= 0) { return 0; }\n\n    if(x < mu) {\n      return 0.5 * Math.exp((x - mu) / b);\n    } else {\n      return 1 - 0.5 * Math.exp(- (x - mu) / b);\n    }\n  },\n\n  mean: function(mu/*, b*/) {\n    return mu;\n  },\n\n  median: function(mu/*, b*/) {\n    return mu;\n  },\n\n  mode: function(mu/*, b*/) {\n    return mu;\n  },\n\n  variance: function(mu, b) {\n    return 2 * b * b;\n  },\n\n  sample: function sample(mu, b) {\n    var u = jStat._random_fn() - 0.5;\n\n    return mu - (b * laplaceSign(u) * Math.log(1 - (2 * Math.abs(u))));\n  }\n});\n\nfunction tukeyWprob(w, rr, cc) {\n  var nleg = 12;\n  var ihalf = 6;\n\n  var C1 = -30;\n  var C2 = -50;\n  var C3 = 60;\n  var bb   = 8;\n  var wlar = 3;\n  var wincr1 = 2;\n  var wincr2 = 3;\n  var xleg = [\n    0.981560634246719250690549090149,\n    0.904117256370474856678465866119,\n    0.769902674194304687036893833213,\n    0.587317954286617447296702418941,\n    0.367831498998180193752691536644,\n    0.125233408511468915472441369464\n  ];\n  var aleg = [\n    0.047175336386511827194615961485,\n    0.106939325995318430960254718194,\n    0.160078328543346226334652529543,\n    0.203167426723065921749064455810,\n    0.233492536538354808760849898925,\n    0.249147045813402785000562436043\n  ];\n\n  var qsqz = w * 0.5;\n\n  // if w >= 16 then the integral lower bound (occurs for c=20)\n  // is 0.99999999999995 so return a value of 1.\n\n  if (qsqz >= bb)\n    return 1.0;\n\n  // find (f(w/2) - 1) ^ cc\n  // (first term in integral of hartley's form).\n\n  var pr_w = 2 * jStat.normal.cdf(qsqz, 0, 1, 1, 0) - 1; // erf(qsqz / M_SQRT2)\n  // if pr_w ^ cc < 2e-22 then set pr_w = 0\n  if (pr_w >= Math.exp(C2 / cc))\n    pr_w = Math.pow(pr_w, cc);\n  else\n    pr_w = 0.0;\n\n  // if w is large then the second component of the\n  // integral is small, so fewer intervals are needed.\n\n  var wincr;\n  if (w > wlar)\n    wincr = wincr1;\n  else\n    wincr = wincr2;\n\n  // find the integral of second term of hartley's form\n  // for the integral of the range for equal-length\n  // intervals using legendre quadrature.  limits of\n  // integration are from (w/2, 8).  two or three\n  // equal-length intervals are used.\n\n  // blb and bub are lower and upper limits of integration.\n\n  var blb = qsqz;\n  var binc = (bb - qsqz) / wincr;\n  var bub = blb + binc;\n  var einsum = 0.0;\n\n  // integrate over each interval\n\n  var cc1 = cc - 1.0;\n  for (var wi = 1; wi <= wincr; wi++) {\n    var elsum = 0.0;\n    var a = 0.5 * (bub + blb);\n\n    // legendre quadrature with order = nleg\n\n    var b = 0.5 * (bub - blb);\n\n    for (var jj = 1; jj <= nleg; jj++) {\n      var j, xx;\n      if (ihalf < jj) {\n        j = (nleg - jj) + 1;\n        xx = xleg[j-1];\n      } else {\n        j = jj;\n        xx = -xleg[j-1];\n      }\n      var c = b * xx;\n      var ac = a + c;\n\n      // if exp(-qexpo/2) < 9e-14,\n      // then doesn't contribute to integral\n\n      var qexpo = ac * ac;\n      if (qexpo > C3)\n        break;\n\n      var pplus = 2 * jStat.normal.cdf(ac, 0, 1, 1, 0);\n      var pminus= 2 * jStat.normal.cdf(ac, w, 1, 1, 0);\n\n      // if rinsum ^ (cc-1) < 9e-14,\n      // then doesn't contribute to integral\n\n      var rinsum = (pplus * 0.5) - (pminus * 0.5);\n      if (rinsum >= Math.exp(C1 / cc1)) {\n        rinsum = (aleg[j-1] * Math.exp(-(0.5 * qexpo))) * Math.pow(rinsum, cc1);\n        elsum += rinsum;\n      }\n    }\n    elsum *= (((2.0 * b) * cc) / Math.sqrt(2 * Math.PI));\n    einsum += elsum;\n    blb = bub;\n    bub += binc;\n  }\n\n  // if pr_w ^ rr < 9e-14, then return 0\n  pr_w += einsum;\n  if (pr_w <= Math.exp(C1 / rr))\n    return 0;\n\n  pr_w = Math.pow(pr_w, rr);\n  if (pr_w >= 1) // 1 was iMax was eps\n    return 1;\n  return pr_w;\n}\n\nfunction tukeyQinv(p, c, v) {\n  var p0 = 0.322232421088;\n  var q0 = 0.993484626060e-01;\n  var p1 = -1.0;\n  var q1 = 0.588581570495;\n  var p2 = -0.342242088547;\n  var q2 = 0.531103462366;\n  var p3 = -0.204231210125;\n  var q3 = 0.103537752850;\n  var p4 = -0.453642210148e-04;\n  var q4 = 0.38560700634e-02;\n  var c1 = 0.8832;\n  var c2 = 0.2368;\n  var c3 = 1.214;\n  var c4 = 1.208;\n  var c5 = 1.4142;\n  var vmax = 120.0;\n\n  var ps = 0.5 - 0.5 * p;\n  var yi = Math.sqrt(Math.log(1.0 / (ps * ps)));\n  var t = yi + (((( yi * p4 + p3) * yi + p2) * yi + p1) * yi + p0)\n     / (((( yi * q4 + q3) * yi + q2) * yi + q1) * yi + q0);\n  if (v < vmax) t += (t * t * t + t) / v / 4.0;\n  var q = c1 - c2 * t;\n  if (v < vmax) q += -c3 / v + c4 * t / v;\n  return t * (q * Math.log(c - 1.0) + c5);\n}\n\njStat.extend(jStat.tukey, {\n  cdf: function cdf(q, nmeans, df) {\n    // Identical implementation as the R ptukey() function as of commit 68947\n    var rr = 1;\n    var cc = nmeans;\n\n    var nlegq = 16;\n    var ihalfq = 8;\n\n    var eps1 = -30.0;\n    var eps2 = 1.0e-14;\n    var dhaf  = 100.0;\n    var dquar = 800.0;\n    var deigh = 5000.0;\n    var dlarg = 25000.0;\n    var ulen1 = 1.0;\n    var ulen2 = 0.5;\n    var ulen3 = 0.25;\n    var ulen4 = 0.125;\n    var xlegq = [\n      0.989400934991649932596154173450,\n      0.944575023073232576077988415535,\n      0.865631202387831743880467897712,\n      0.755404408355003033895101194847,\n      0.617876244402643748446671764049,\n      0.458016777657227386342419442984,\n      0.281603550779258913230460501460,\n      0.950125098376374401853193354250e-1\n    ];\n    var alegq = [\n      0.271524594117540948517805724560e-1,\n      0.622535239386478928628438369944e-1,\n      0.951585116824927848099251076022e-1,\n      0.124628971255533872052476282192,\n      0.149595988816576732081501730547,\n      0.169156519395002538189312079030,\n      0.182603415044923588866763667969,\n      0.189450610455068496285396723208\n    ];\n\n    if (q <= 0)\n      return 0;\n\n    // df must be > 1\n    // there must be at least two values\n\n    if (df < 2 || rr < 1 || cc < 2) return NaN;\n\n    if (!Number.isFinite(q))\n      return 1;\n\n    if (df > dlarg)\n      return tukeyWprob(q, rr, cc);\n\n    // calculate leading constant\n\n    var f2 = df * 0.5;\n    var f2lf = ((f2 * Math.log(df)) - (df * Math.log(2))) - jStat.gammaln(f2);\n    var f21 = f2 - 1.0;\n\n    // integral is divided into unit, half-unit, quarter-unit, or\n    // eighth-unit length intervals depending on the value of the\n    // degrees of freedom.\n\n    var ff4 = df * 0.25;\n    var ulen;\n    if      (df <= dhaf)  ulen = ulen1;\n    else if (df <= dquar) ulen = ulen2;\n    else if (df <= deigh) ulen = ulen3;\n    else                  ulen = ulen4;\n\n    f2lf += Math.log(ulen);\n\n    // integrate over each subinterval\n\n    var ans = 0.0;\n\n    for (var i = 1; i <= 50; i++) {\n      var otsum = 0.0;\n\n      // legendre quadrature with order = nlegq\n      // nodes (stored in xlegq) are symmetric around zero.\n\n      var twa1 = (2 * i - 1) * ulen;\n\n      for (var jj = 1; jj <= nlegq; jj++) {\n        var j, t1;\n        if (ihalfq < jj) {\n          j = jj - ihalfq - 1;\n          t1 = (f2lf + (f21 * Math.log(twa1 + (xlegq[j] * ulen))))\n              - (((xlegq[j] * ulen) + twa1) * ff4);\n        } else {\n          j = jj - 1;\n          t1 = (f2lf + (f21 * Math.log(twa1 - (xlegq[j] * ulen))))\n              + (((xlegq[j] * ulen) - twa1) * ff4);\n        }\n\n        // if exp(t1) < 9e-14, then doesn't contribute to integral\n        var qsqz;\n        if (t1 >= eps1) {\n          if (ihalfq < jj) {\n            qsqz = q * Math.sqrt(((xlegq[j] * ulen) + twa1) * 0.5);\n          } else {\n            qsqz = q * Math.sqrt(((-(xlegq[j] * ulen)) + twa1) * 0.5);\n          }\n\n          // call wprob to find integral of range portion\n\n          var wprb = tukeyWprob(qsqz, rr, cc);\n          var rotsum = (wprb * alegq[j]) * Math.exp(t1);\n          otsum += rotsum;\n        }\n        // end legendre integral for interval i\n        // L200:\n      }\n\n      // if integral for interval i < 1e-14, then stop.\n      // However, in order to avoid small area under left tail,\n      // at least  1 / ulen  intervals are calculated.\n      if (i * ulen >= 1.0 && otsum <= eps2)\n        break;\n\n      // end of interval i\n      // L330:\n\n      ans += otsum;\n    }\n\n    if (otsum > eps2) { // not converged\n      throw new Error('tukey.cdf failed to converge');\n    }\n    if (ans > 1)\n      ans = 1;\n    return ans;\n  },\n\n  inv: function(p, nmeans, df) {\n    // Identical implementation as the R qtukey() function as of commit 68947\n    var rr = 1;\n    var cc = nmeans;\n\n    var eps = 0.0001;\n    var maxiter = 50;\n\n    // df must be > 1 ; there must be at least two values\n    if (df < 2 || rr < 1 || cc < 2) return NaN;\n\n    if (p < 0 || p > 1) return NaN;\n    if (p === 0) return 0;\n    if (p === 1) return Infinity;\n\n    // Initial value\n\n    var x0 = tukeyQinv(p, cc, df);\n\n    // Find prob(value < x0)\n\n    var valx0 = jStat.tukey.cdf(x0, nmeans, df) - p;\n\n    // Find the second iterate and prob(value < x1).\n    // If the first iterate has probability value\n    // exceeding p then second iterate is 1 less than\n    // first iterate; otherwise it is 1 greater.\n\n    var x1;\n    if (valx0 > 0.0)\n      x1 = Math.max(0.0, x0 - 1.0);\n    else\n      x1 = x0 + 1.0;\n    var valx1 = jStat.tukey.cdf(x1, nmeans, df) - p;\n\n    // Find new iterate\n\n    var ans;\n    for(var iter = 1; iter < maxiter; iter++) {\n      ans = x1 - ((valx1 * (x1 - x0)) / (valx1 - valx0));\n      valx0 = valx1;\n\n      // New iterate must be >= 0\n\n      x0 = x1;\n      if (ans < 0.0) {\n        ans = 0.0;\n        valx1 = -p;\n      }\n      // Find prob(value < new iterate)\n\n      valx1 = jStat.tukey.cdf(ans, nmeans, df) - p;\n      x1 = ans;\n\n      // If the difference between two successive\n      // iterates is less than eps, stop\n\n      var xabs = Math.abs(x1 - x0);\n      if (xabs < eps)\n        return ans;\n    }\n\n    throw new Error('tukey.inv failed to converge');\n  }\n});\n\n}(jStat, Math));\n/* Provides functions for the solution of linear system of equations, integration, extrapolation,\n * interpolation, eigenvalue problems, differential equations and PCA analysis. */\n\n(function(jStat, Math) {\n\nvar push = Array.prototype.push;\nvar isArray = jStat.utils.isArray;\n\nfunction isUsable(arg) {\n  return isArray(arg) || arg instanceof jStat;\n}\n\njStat.extend({\n\n  // add a vector/matrix to a vector/matrix or scalar\n  add: function add(arr, arg) {\n    // check if arg is a vector or scalar\n    if (isUsable(arg)) {\n      if (!isUsable(arg[0])) arg = [ arg ];\n      return jStat.map(arr, function(value, row, col) {\n        return value + arg[row][col];\n      });\n    }\n    return jStat.map(arr, function(value) { return value + arg; });\n  },\n\n  // subtract a vector or scalar from the vector\n  subtract: function subtract(arr, arg) {\n    // check if arg is a vector or scalar\n    if (isUsable(arg)) {\n      if (!isUsable(arg[0])) arg = [ arg ];\n      return jStat.map(arr, function(value, row, col) {\n        return value - arg[row][col] || 0;\n      });\n    }\n    return jStat.map(arr, function(value) { return value - arg; });\n  },\n\n  // matrix division\n  divide: function divide(arr, arg) {\n    if (isUsable(arg)) {\n      if (!isUsable(arg[0])) arg = [ arg ];\n      return jStat.multiply(arr, jStat.inv(arg));\n    }\n    return jStat.map(arr, function(value) { return value / arg; });\n  },\n\n  // matrix multiplication\n  multiply: function multiply(arr, arg) {\n    var row, col, nrescols, sum, nrow, ncol, res, rescols;\n    // eg: arr = 2 arg = 3 -> 6 for res[0][0] statement closure\n    if (arr.length === undefined && arg.length === undefined) {\n      return arr * arg;\n    }\n    nrow = arr.length,\n    ncol = arr[0].length,\n    res = jStat.zeros(nrow, nrescols = (isUsable(arg)) ? arg[0].length : ncol),\n    rescols = 0;\n    if (isUsable(arg)) {\n      for (; rescols < nrescols; rescols++) {\n        for (row = 0; row < nrow; row++) {\n          sum = 0;\n          for (col = 0; col < ncol; col++)\n          sum += arr[row][col] * arg[col][rescols];\n          res[row][rescols] = sum;\n        }\n      }\n      return (nrow === 1 && rescols === 1) ? res[0][0] : res;\n    }\n    return jStat.map(arr, function(value) { return value * arg; });\n  },\n\n  // outer([1,2,3],[4,5,6])\n  // ===\n  // [[1],[2],[3]] times [[4,5,6]]\n  // ->\n  // [[4,5,6],[8,10,12],[12,15,18]]\n  outer:function outer(A, B) {\n    return jStat.multiply(A.map(function(t){ return [t] }), [B]);\n  },\n\n\n  // Returns the dot product of two matricies\n  dot: function dot(arr, arg) {\n    if (!isUsable(arr[0])) arr = [ arr ];\n    if (!isUsable(arg[0])) arg = [ arg ];\n    // convert column to row vector\n    var left = (arr[0].length === 1 && arr.length !== 1) ? jStat.transpose(arr) : arr,\n    right = (arg[0].length === 1 && arg.length !== 1) ? jStat.transpose(arg) : arg,\n    res = [],\n    row = 0,\n    nrow = left.length,\n    ncol = left[0].length,\n    sum, col;\n    for (; row < nrow; row++) {\n      res[row] = [];\n      sum = 0;\n      for (col = 0; col < ncol; col++)\n      sum += left[row][col] * right[row][col];\n      res[row] = sum;\n    }\n    return (res.length === 1) ? res[0] : res;\n  },\n\n  // raise every element by a scalar\n  pow: function pow(arr, arg) {\n    return jStat.map(arr, function(value) { return Math.pow(value, arg); });\n  },\n\n  // exponentiate every element\n  exp: function exp(arr) {\n    return jStat.map(arr, function(value) { return Math.exp(value); });\n  },\n\n  // generate the natural log of every element\n  log: function exp(arr) {\n    return jStat.map(arr, function(value) { return Math.log(value); });\n  },\n\n  // generate the absolute values of the vector\n  abs: function abs(arr) {\n    return jStat.map(arr, function(value) { return Math.abs(value); });\n  },\n\n  // computes the p-norm of the vector\n  // In the case that a matrix is passed, uses the first row as the vector\n  norm: function norm(arr, p) {\n    var nnorm = 0,\n    i = 0;\n    // check the p-value of the norm, and set for most common case\n    if (isNaN(p)) p = 2;\n    // check if multi-dimensional array, and make vector correction\n    if (isUsable(arr[0])) arr = arr[0];\n    // vector norm\n    for (; i < arr.length; i++) {\n      nnorm += Math.pow(Math.abs(arr[i]), p);\n    }\n    return Math.pow(nnorm, 1 / p);\n  },\n\n  // computes the angle between two vectors in rads\n  // In case a matrix is passed, this uses the first row as the vector\n  angle: function angle(arr, arg) {\n    return Math.acos(jStat.dot(arr, arg) / (jStat.norm(arr) * jStat.norm(arg)));\n  },\n\n  // augment one matrix by another\n  // Note: this function returns a matrix, not a jStat object\n  aug: function aug(a, b) {\n    var newarr = [];\n    var i;\n    for (i = 0; i < a.length; i++) {\n      newarr.push(a[i].slice());\n    }\n    for (i = 0; i < newarr.length; i++) {\n      push.apply(newarr[i], b[i]);\n    }\n    return newarr;\n  },\n\n  // The inv() function calculates the inverse of a matrix\n  // Create the inverse by augmenting the matrix by the identity matrix of the\n  // appropriate size, and then use G-J elimination on the augmented matrix.\n  inv: function inv(a) {\n    var rows = a.length;\n    var cols = a[0].length;\n    var b = jStat.identity(rows, cols);\n    var c = jStat.gauss_jordan(a, b);\n    var result = [];\n    var i = 0;\n    var j;\n\n    //We need to copy the inverse portion to a new matrix to rid G-J artifacts\n    for (; i < rows; i++) {\n      result[i] = [];\n      for (j = cols; j < c[0].length; j++)\n        result[i][j - cols] = c[i][j];\n    }\n    return result;\n  },\n\n  // calculate the determinant of a matrix\n  det: function det(a) {\n    if (a.length === 2) {\n      return a[0][0] * a[1][1] - a[0][1] * a[1][0];\n    }\n\n    var determinant = 0;\n    for (var i = 0; i < a.length; i++) {\n      // build a sub matrix without column `i`\n      var submatrix = [];\n      for (var row = 1; row < a.length; row++) {\n        submatrix[row - 1] = [];\n        for (var col = 0; col < a.length; col++) {\n          if (col < i) {\n            submatrix[row - 1][col] = a[row][col];\n          } else if (col > i) {\n            submatrix[row - 1][col - 1] = a[row][col];\n          }\n        }\n      }\n\n      // alternate between + and - between determinants\n      var sign = i % 2 ? -1 : 1;\n      determinant += det(submatrix) * a[0][i] * sign;\n    }\n\n    return determinant\n  },\n\n  gauss_elimination: function gauss_elimination(a, b) {\n    var i = 0,\n    j = 0,\n    n = a.length,\n    m = a[0].length,\n    factor = 1,\n    sum = 0,\n    x = [],\n    maug, pivot, temp, k;\n    a = jStat.aug(a, b);\n    maug = a[0].length;\n    for(i = 0; i < n; i++) {\n      pivot = a[i][i];\n      j = i;\n      for (k = i + 1; k < m; k++) {\n        if (pivot < Math.abs(a[k][i])) {\n          pivot = a[k][i];\n          j = k;\n        }\n      }\n      if (j != i) {\n        for(k = 0; k < maug; k++) {\n          temp = a[i][k];\n          a[i][k] = a[j][k];\n          a[j][k] = temp;\n        }\n      }\n      for (j = i + 1; j < n; j++) {\n        factor = a[j][i] / a[i][i];\n        for(k = i; k < maug; k++) {\n          a[j][k] = a[j][k] - factor * a[i][k];\n        }\n      }\n    }\n    for (i = n - 1; i >= 0; i--) {\n      sum = 0;\n      for (j = i + 1; j<= n - 1; j++) {\n        sum = sum + x[j] * a[i][j];\n      }\n      x[i] =(a[i][maug - 1] - sum) / a[i][i];\n    }\n    return x;\n  },\n\n  gauss_jordan: function gauss_jordan(a, b) {\n    var m = jStat.aug(a, b);\n    var h = m.length;\n    var w = m[0].length;\n    var c = 0;\n    var x, y, y2;\n    // find max pivot\n    for (y = 0; y < h; y++) {\n      var maxrow = y;\n      for (y2 = y+1; y2 < h; y2++) {\n        if (Math.abs(m[y2][y]) > Math.abs(m[maxrow][y]))\n          maxrow = y2;\n      }\n      var tmp = m[y];\n      m[y] = m[maxrow];\n      m[maxrow] = tmp\n      for (y2 = y+1; y2 < h; y2++) {\n        c = m[y2][y] / m[y][y];\n        for (x = y; x < w; x++) {\n          m[y2][x] -= m[y][x] * c;\n        }\n      }\n    }\n    // backsubstitute\n    for (y = h-1; y >= 0; y--) {\n      c = m[y][y];\n      for (y2 = 0; y2 < y; y2++) {\n        for (x = w-1; x > y-1; x--) {\n          m[y2][x] -= m[y][x] * m[y2][y] / c;\n        }\n      }\n      m[y][y] /= c;\n      for (x = h; x < w; x++) {\n        m[y][x] /= c;\n      }\n    }\n    return m;\n  },\n\n  // solve equation\n  // Ax=b\n  // A is upper triangular matrix\n  // A=[[1,2,3],[0,4,5],[0,6,7]]\n  // b=[1,2,3]\n  // triaUpSolve(A,b) // -> [2.666,0.1666,1.666]\n  // if you use matrix style\n  // A=[[1,2,3],[0,4,5],[0,6,7]]\n  // b=[[1],[2],[3]]\n  // will return [[2.666],[0.1666],[1.666]]\n  triaUpSolve: function triaUpSolve(A, b) {\n    var size = A[0].length;\n    var x = jStat.zeros(1, size)[0];\n    var parts;\n    var matrix_mode = false;\n\n    if (b[0].length != undefined) {\n      b = b.map(function(i){ return i[0] });\n      matrix_mode = true;\n    }\n\n    jStat.arange(size - 1, -1, -1).forEach(function(i) {\n      parts = jStat.arange(i + 1, size).map(function(j) {\n        return x[j] * A[i][j];\n      });\n      x[i] = (b[i] - jStat.sum(parts)) / A[i][i];\n    });\n\n    if (matrix_mode)\n      return x.map(function(i){ return [i] });\n    return x;\n  },\n\n  triaLowSolve: function triaLowSolve(A, b) {\n    // like to triaUpSolve but A is lower triangular matrix\n    var size = A[0].length;\n    var x = jStat.zeros(1, size)[0];\n    var parts;\n\n    var matrix_mode=false;\n    if (b[0].length != undefined) {\n      b = b.map(function(i){ return i[0] });\n      matrix_mode = true;\n    }\n\n    jStat.arange(size).forEach(function(i) {\n      parts = jStat.arange(i).map(function(j) {\n        return A[i][j] * x[j];\n      });\n      x[i] = (b[i] - jStat.sum(parts)) / A[i][i];\n    })\n\n    if (matrix_mode)\n      return x.map(function(i){ return [i] });\n    return x;\n  },\n\n\n  // A -> [L,U]\n  // A=LU\n  // L is lower triangular matrix\n  // U is upper triangular matrix\n  lu: function lu(A) {\n    var size = A.length;\n    //var L=jStat.diagonal(jStat.ones(1,size)[0]);\n    var L = jStat.identity(size);\n    var R = jStat.zeros(A.length, A[0].length);\n    var parts;\n    jStat.arange(size).forEach(function(t) {\n      R[0][t] = A[0][t];\n    });\n    jStat.arange(1, size).forEach(function(l) {\n      jStat.arange(l).forEach(function(i) {\n        parts = jStat.arange(i).map(function(jj) {\n          return L[l][jj] * R[jj][i];\n        });\n        L[l][i] = (A[l][i] - jStat.sum(parts)) / R[i][i];\n      });\n      jStat.arange(l, size).forEach(function(j) {\n        parts = jStat.arange(l).map(function(jj) {\n          return L[l][jj] * R[jj][j];\n        });\n        R[l][j] = A[parts.length][j] - jStat.sum(parts);\n      });\n    });\n    return [L, R];\n  },\n\n  // A -> T\n  // A=TT'\n  // T is lower triangular matrix\n  cholesky: function cholesky(A) {\n    var size = A.length;\n    var T = jStat.zeros(A.length, A[0].length);\n    var parts;\n    jStat.arange(size).forEach(function(i) {\n      parts = jStat.arange(i).map(function(t) {\n        return Math.pow(T[i][t],2);\n      });\n      T[i][i] = Math.sqrt(A[i][i] - jStat.sum(parts));\n      jStat.arange(i + 1, size).forEach(function(j) {\n        parts = jStat.arange(i).map(function(t) {\n          return T[i][t] * T[j][t];\n        });\n        T[j][i] = (A[i][j] - jStat.sum(parts)) / T[i][i];\n      });\n    });\n    return T;\n  },\n\n\n  gauss_jacobi: function gauss_jacobi(a, b, x, r) {\n    var i = 0;\n    var j = 0;\n    var n = a.length;\n    var l = [];\n    var u = [];\n    var d = [];\n    var xv, c, h, xk;\n    for (; i < n; i++) {\n      l[i] = [];\n      u[i] = [];\n      d[i] = [];\n      for (j = 0; j < n; j++) {\n        if (i > j) {\n          l[i][j] = a[i][j];\n          u[i][j] = d[i][j] = 0;\n        } else if (i < j) {\n          u[i][j] = a[i][j];\n          l[i][j] = d[i][j] = 0;\n        } else {\n          d[i][j] = a[i][j];\n          l[i][j] = u[i][j] = 0;\n        }\n      }\n    }\n    h = jStat.multiply(jStat.multiply(jStat.inv(d), jStat.add(l, u)), -1);\n    c = jStat.multiply(jStat.inv(d), b);\n    xv = x;\n    xk = jStat.add(jStat.multiply(h, x), c);\n    i = 2;\n    while (Math.abs(jStat.norm(jStat.subtract(xk,xv))) > r) {\n      xv = xk;\n      xk = jStat.add(jStat.multiply(h, xv), c);\n      i++;\n    }\n    return xk;\n  },\n\n  gauss_seidel: function gauss_seidel(a, b, x, r) {\n    var i = 0;\n    var n = a.length;\n    var l = [];\n    var u = [];\n    var d = [];\n    var j, xv, c, h, xk;\n    for (; i < n; i++) {\n      l[i] = [];\n      u[i] = [];\n      d[i] = [];\n      for (j = 0; j < n; j++) {\n        if (i > j) {\n          l[i][j] = a[i][j];\n          u[i][j] = d[i][j] = 0;\n        } else if (i < j) {\n          u[i][j] = a[i][j];\n          l[i][j] = d[i][j] = 0;\n        } else {\n          d[i][j] = a[i][j];\n          l[i][j] = u[i][j] = 0;\n        }\n      }\n    }\n    h = jStat.multiply(jStat.multiply(jStat.inv(jStat.add(d, l)), u), -1);\n    c = jStat.multiply(jStat.inv(jStat.add(d, l)), b);\n    xv = x;\n    xk = jStat.add(jStat.multiply(h, x), c);\n    i = 2;\n    while (Math.abs(jStat.norm(jStat.subtract(xk, xv))) > r) {\n      xv = xk;\n      xk = jStat.add(jStat.multiply(h, xv), c);\n      i = i + 1;\n    }\n    return xk;\n  },\n\n  SOR: function SOR(a, b, x, r, w) {\n    var i = 0;\n    var n = a.length;\n    var l = [];\n    var u = [];\n    var d = [];\n    var j, xv, c, h, xk;\n    for (; i < n; i++) {\n      l[i] = [];\n      u[i] = [];\n      d[i] = [];\n      for (j = 0; j < n; j++) {\n        if (i > j) {\n          l[i][j] = a[i][j];\n          u[i][j] = d[i][j] = 0;\n        } else if (i < j) {\n          u[i][j] = a[i][j];\n          l[i][j] = d[i][j] = 0;\n        } else {\n          d[i][j] = a[i][j];\n          l[i][j] = u[i][j] = 0;\n        }\n      }\n    }\n    h = jStat.multiply(jStat.inv(jStat.add(d, jStat.multiply(l, w))),\n                       jStat.subtract(jStat.multiply(d, 1 - w),\n                                      jStat.multiply(u, w)));\n    c = jStat.multiply(jStat.multiply(jStat.inv(jStat.add(d,\n        jStat.multiply(l, w))), b), w);\n    xv = x;\n    xk = jStat.add(jStat.multiply(h, x), c);\n    i = 2;\n    while (Math.abs(jStat.norm(jStat.subtract(xk, xv))) > r) {\n      xv = xk;\n      xk = jStat.add(jStat.multiply(h, xv), c);\n      i++;\n    }\n    return xk;\n  },\n\n  householder: function householder(a) {\n    var m = a.length;\n    var n = a[0].length;\n    var i = 0;\n    var w = [];\n    var p = [];\n    var alpha, r, k, j, factor;\n    for (; i < m - 1; i++) {\n      alpha = 0;\n      for (j = i + 1; j < n; j++)\n      alpha += (a[j][i] * a[j][i]);\n      factor = (a[i + 1][i] > 0) ? -1 : 1;\n      alpha = factor * Math.sqrt(alpha);\n      r = Math.sqrt((((alpha * alpha) - a[i + 1][i] * alpha) / 2));\n      w = jStat.zeros(m, 1);\n      w[i + 1][0] = (a[i + 1][i] - alpha) / (2 * r);\n      for (k = i + 2; k < m; k++) w[k][0] = a[k][i] / (2 * r);\n      p = jStat.subtract(jStat.identity(m, n),\n          jStat.multiply(jStat.multiply(w, jStat.transpose(w)), 2));\n      a = jStat.multiply(p, jStat.multiply(a, p));\n    }\n    return a;\n  },\n\n  // A -> [Q,R]\n  // Q is orthogonal matrix\n  // R is upper triangular\n  QR: (function() {\n    // x -> Q\n    // find a orthogonal matrix Q st.\n    // Qx=y\n    // y is [||x||,0,0,...]\n\n    // quick ref\n    var sum   = jStat.sum;\n    var range = jStat.arange;\n\n    function qr2(x) {\n      // quick impletation\n      // https://www.stat.wisc.edu/~larget/math496/qr.html\n\n      var n = x.length;\n      var p = x[0].length;\n\n      var r = jStat.zeros(p, p);\n      x = jStat.copy(x);\n\n      var i,j,k;\n      for(j = 0; j < p; j++){\n        r[j][j] = Math.sqrt(sum(range(n).map(function(i){\n          return x[i][j] * x[i][j];\n        })));\n        for(i = 0; i < n; i++){\n          x[i][j] = x[i][j] / r[j][j];\n        }\n        for(k = j+1; k < p; k++){\n          r[j][k] = sum(range(n).map(function(i){\n            return x[i][j] * x[i][k];\n          }));\n          for(i = 0; i < n; i++){\n            x[i][k] = x[i][k] - x[i][j]*r[j][k];\n          }\n        }\n      }\n      return [x, r];\n    }\n\n    return qr2;\n  }()),\n\n  lstsq: (function() {\n    // solve least squard problem for Ax=b as QR decomposition way if b is\n    // [[b1],[b2],[b3]] form will return [[x1],[x2],[x3]] array form solution\n    // else b is [b1,b2,b3] form will return [x1,x2,x3] array form solution\n    function R_I(A) {\n      A = jStat.copy(A);\n      var size = A.length;\n      var I = jStat.identity(size);\n      jStat.arange(size - 1, -1, -1).forEach(function(i) {\n        jStat.sliceAssign(\n            I, { row: i }, jStat.divide(jStat.slice(I, { row: i }), A[i][i]));\n        jStat.sliceAssign(\n            A, { row: i }, jStat.divide(jStat.slice(A, { row: i }), A[i][i]));\n        jStat.arange(i).forEach(function(j) {\n          var c = jStat.multiply(A[j][i], -1);\n          var Aj = jStat.slice(A, { row: j });\n          var cAi = jStat.multiply(jStat.slice(A, { row: i }), c);\n          jStat.sliceAssign(A, { row: j }, jStat.add(Aj, cAi));\n          var Ij = jStat.slice(I, { row: j });\n          var cIi = jStat.multiply(jStat.slice(I, { row: i }), c);\n          jStat.sliceAssign(I, { row: j }, jStat.add(Ij, cIi));\n        })\n      });\n      return I;\n    }\n\n    function qr_solve(A, b){\n      var array_mode = false;\n      if (b[0].length === undefined) {\n        // [c1,c2,c3] mode\n        b = b.map(function(x){ return [x] });\n        array_mode = true;\n      }\n      var QR = jStat.QR(A);\n      var Q = QR[0];\n      var R = QR[1];\n      var attrs = A[0].length;\n      var Q1 = jStat.slice(Q,{col:{end:attrs}});\n      var R1 = jStat.slice(R,{row:{end:attrs}});\n      var RI = R_I(R1);\n      var Q2 = jStat.transpose(Q1);\n\n      if(Q2[0].length === undefined){\n        Q2 = [Q2]; // The confusing jStat.multifly implementation threat nature process again.\n      }\n\n      var x = jStat.multiply(jStat.multiply(RI, Q2), b);\n\n      if(x.length === undefined){\n        x = [[x]]; // The confusing jStat.multifly implementation threat nature process again.\n      }\n\n\n      if (array_mode)\n        return x.map(function(i){ return i[0] });\n      return x;\n    }\n\n    return qr_solve;\n  }()),\n\n  jacobi: function jacobi(a) {\n    var condition = 1;\n    var n = a.length;\n    var e = jStat.identity(n, n);\n    var ev = [];\n    var b, i, j, p, q, maxim, theta, s;\n    // condition === 1 only if tolerance is not reached\n    while (condition === 1) {\n      maxim = a[0][1];\n      p = 0;\n      q = 1;\n      for (i = 0; i < n; i++) {\n        for (j = 0; j < n; j++) {\n          if (i != j) {\n            if (maxim < Math.abs(a[i][j])) {\n              maxim = Math.abs(a[i][j]);\n              p = i;\n              q = j;\n            }\n          }\n        }\n      }\n      if (a[p][p] === a[q][q])\n        theta = (a[p][q] > 0) ? Math.PI / 4 : -Math.PI / 4;\n      else\n        theta = Math.atan(2 * a[p][q] / (a[p][p] - a[q][q])) / 2;\n      s = jStat.identity(n, n);\n      s[p][p] = Math.cos(theta);\n      s[p][q] = -Math.sin(theta);\n      s[q][p] = Math.sin(theta);\n      s[q][q] = Math.cos(theta);\n      // eigen vector matrix\n      e = jStat.multiply(e, s);\n      b = jStat.multiply(jStat.multiply(jStat.inv(s), a), s);\n      a = b;\n      condition = 0;\n      for (i = 1; i < n; i++) {\n        for (j = 1; j < n; j++) {\n          if (i != j && Math.abs(a[i][j]) > 0.001) {\n            condition = 1;\n          }\n        }\n      }\n    }\n    for (i = 0; i < n; i++) ev.push(a[i][i]);\n    //returns both the eigenvalue and eigenmatrix\n    return [e, ev];\n  },\n\n  rungekutta: function rungekutta(f, h, p, t_j, u_j, order) {\n    var k1, k2, u_j1, k3, k4;\n    if (order === 2) {\n      while (t_j <= p) {\n        k1 = h * f(t_j, u_j);\n        k2 = h * f(t_j + h, u_j + k1);\n        u_j1 = u_j + (k1 + k2) / 2;\n        u_j = u_j1;\n        t_j = t_j + h;\n      }\n    }\n    if (order === 4) {\n      while (t_j <= p) {\n        k1 = h * f(t_j, u_j);\n        k2 = h * f(t_j + h / 2, u_j + k1 / 2);\n        k3 = h * f(t_j + h / 2, u_j + k2 / 2);\n        k4 = h * f(t_j +h, u_j + k3);\n        u_j1 = u_j + (k1 + 2 * k2 + 2 * k3 + k4) / 6;\n        u_j = u_j1;\n        t_j = t_j + h;\n      }\n    }\n    return u_j;\n  },\n\n  romberg: function romberg(f, a, b, order) {\n    var i = 0;\n    var h = (b - a) / 2;\n    var x = [];\n    var h1 = [];\n    var g = [];\n    var m, a1, j, k, I;\n    while (i < order / 2) {\n      I = f(a);\n      for (j = a, k = 0; j <= b; j = j + h, k++) x[k] = j;\n      m = x.length;\n      for (j = 1; j < m - 1; j++) {\n        I += (((j % 2) !== 0) ? 4 : 2) * f(x[j]);\n      }\n      I = (h / 3) * (I + f(b));\n      g[i] = I;\n      h /= 2;\n      i++;\n    }\n    a1 = g.length;\n    m = 1;\n    while (a1 !== 1) {\n      for (j = 0; j < a1 - 1; j++)\n      h1[j] = ((Math.pow(4, m)) * g[j + 1] - g[j]) / (Math.pow(4, m) - 1);\n      a1 = h1.length;\n      g = h1;\n      h1 = [];\n      m++;\n    }\n    return g;\n  },\n\n  richardson: function richardson(X, f, x, h) {\n    function pos(X, x) {\n      var i = 0;\n      var n = X.length;\n      var p;\n      for (; i < n; i++)\n        if (X[i] === x) p = i;\n      return p;\n    }\n    var h_min = Math.abs(x - X[pos(X, x) + 1]);\n    var i = 0;\n    var g = [];\n    var h1 = [];\n    var y1, y2, m, a, j;\n    while (h >= h_min) {\n      y1 = pos(X, x + h);\n      y2 = pos(X, x);\n      g[i] = (f[y1] - 2 * f[y2] + f[2 * y2 - y1]) / (h * h);\n      h /= 2;\n      i++;\n    }\n    a = g.length;\n    m = 1;\n    while (a != 1) {\n      for (j = 0; j < a - 1; j++)\n        h1[j] = ((Math.pow(4, m)) * g[j + 1] - g[j]) / (Math.pow(4, m) - 1);\n      a = h1.length;\n      g = h1;\n      h1 = [];\n      m++;\n    }\n    return g;\n  },\n\n  simpson: function simpson(f, a, b, n) {\n    var h = (b - a) / n;\n    var I = f(a);\n    var x = [];\n    var j = a;\n    var k = 0;\n    var i = 1;\n    var m;\n    for (; j <= b; j = j + h, k++)\n      x[k] = j;\n    m = x.length;\n    for (; i < m - 1; i++) {\n      I += ((i % 2 !== 0) ? 4 : 2) * f(x[i]);\n    }\n    return (h / 3) * (I + f(b));\n  },\n\n  hermite: function hermite(X, F, dF, value) {\n    var n = X.length;\n    var p = 0;\n    var i = 0;\n    var l = [];\n    var dl = [];\n    var A = [];\n    var B = [];\n    var j;\n    for (; i < n; i++) {\n      l[i] = 1;\n      for (j = 0; j < n; j++) {\n        if (i != j) l[i] *= (value - X[j]) / (X[i] - X[j]);\n      }\n      dl[i] = 0;\n      for (j = 0; j < n; j++) {\n        if (i != j) dl[i] += 1 / (X [i] - X[j]);\n      }\n      A[i] = (1 - 2 * (value - X[i]) * dl[i]) * (l[i] * l[i]);\n      B[i] = (value - X[i]) * (l[i] * l[i]);\n      p += (A[i] * F[i] + B[i] * dF[i]);\n    }\n    return p;\n  },\n\n  lagrange: function lagrange(X, F, value) {\n    var p = 0;\n    var i = 0;\n    var j, l;\n    var n = X.length;\n    for (; i < n; i++) {\n      l = F[i];\n      for (j = 0; j < n; j++) {\n        // calculating the lagrange polynomial L_i\n        if (i != j) l *= (value - X[j]) / (X[i] - X[j]);\n      }\n      // adding the lagrange polynomials found above\n      p += l;\n    }\n    return p;\n  },\n\n  cubic_spline: function cubic_spline(X, F, value) {\n    var n = X.length;\n    var i = 0, j;\n    var A = [];\n    var B = [];\n    var alpha = [];\n    var c = [];\n    var h = [];\n    var b = [];\n    var d = [];\n    for (; i < n - 1; i++)\n      h[i] = X[i + 1] - X[i];\n    alpha[0] = 0;\n    for (i = 1; i < n - 1; i++) {\n      alpha[i] = (3 / h[i]) * (F[i + 1] - F[i]) -\n          (3 / h[i-1]) * (F[i] - F[i-1]);\n    }\n    for (i = 1; i < n - 1; i++) {\n      A[i] = [];\n      B[i] = [];\n      A[i][i-1] = h[i-1];\n      A[i][i] = 2 * (h[i - 1] + h[i]);\n      A[i][i+1] = h[i];\n      B[i][0] = alpha[i];\n    }\n    c = jStat.multiply(jStat.inv(A), B);\n    for (j = 0; j < n - 1; j++) {\n      b[j] = (F[j + 1] - F[j]) / h[j] - h[j] * (c[j + 1][0] + 2 * c[j][0]) / 3;\n      d[j] = (c[j + 1][0] - c[j][0]) / (3 * h[j]);\n    }\n    for (j = 0; j < n; j++) {\n      if (X[j] > value) break;\n    }\n    j -= 1;\n    return F[j] + (value - X[j]) * b[j] + jStat.sq(value-X[j]) *\n        c[j] + (value - X[j]) * jStat.sq(value - X[j]) * d[j];\n  },\n\n  gauss_quadrature: function gauss_quadrature() {\n    throw new Error('gauss_quadrature not yet implemented');\n  },\n\n  PCA: function PCA(X) {\n    var m = X.length;\n    var n = X[0].length;\n    var i = 0;\n    var j, temp1;\n    var u = [];\n    var D = [];\n    var result = [];\n    var temp2 = [];\n    var Y = [];\n    var Bt = [];\n    var B = [];\n    var C = [];\n    var V = [];\n    var Vt = [];\n    for (i = 0; i < m; i++) {\n      u[i] = jStat.sum(X[i]) / n;\n    }\n    for (i = 0; i < n; i++) {\n      B[i] = [];\n      for(j = 0; j < m; j++) {\n        B[i][j] = X[j][i] - u[j];\n      }\n    }\n    B = jStat.transpose(B);\n    for (i = 0; i < m; i++) {\n      C[i] = [];\n      for (j = 0; j < m; j++) {\n        C[i][j] = (jStat.dot([B[i]], [B[j]])) / (n - 1);\n      }\n    }\n    result = jStat.jacobi(C);\n    V = result[0];\n    D = result[1];\n    Vt = jStat.transpose(V);\n    for (i = 0; i < D.length; i++) {\n      for (j = i; j < D.length; j++) {\n        if(D[i] < D[j])  {\n          temp1 = D[i];\n          D[i] = D[j];\n          D[j] = temp1;\n          temp2 = Vt[i];\n          Vt[i] = Vt[j];\n          Vt[j] = temp2;\n        }\n      }\n    }\n    Bt = jStat.transpose(B);\n    for (i = 0; i < m; i++) {\n      Y[i] = [];\n      for (j = 0; j < Bt.length; j++) {\n        Y[i][j] = jStat.dot([Vt[i]], [Bt[j]]);\n      }\n    }\n    return [X, D, Vt, Y];\n  }\n});\n\n// extend jStat.fn with methods that require one argument\n(function(funcs) {\n  for (var i = 0; i < funcs.length; i++) (function(passfunc) {\n    jStat.fn[passfunc] = function(arg, func) {\n      var tmpthis = this;\n      // check for callback\n      if (func) {\n        setTimeout(function() {\n          func.call(tmpthis, jStat.fn[passfunc].call(tmpthis, arg));\n        }, 15);\n        return this;\n      }\n      if (typeof jStat[passfunc](this, arg) === 'number')\n        return jStat[passfunc](this, arg);\n      else\n        return jStat(jStat[passfunc](this, arg));\n    };\n  }(funcs[i]));\n}('add divide multiply subtract dot pow exp log abs norm angle'.split(' ')));\n\n}(jStat, Math));\n(function(jStat, Math) {\n\nvar slice = [].slice;\nvar isNumber = jStat.utils.isNumber;\nvar isArray = jStat.utils.isArray;\n\n// flag==true denotes use of sample standard deviation\n// Z Statistics\njStat.extend({\n  // 2 different parameter lists:\n  // (value, mean, sd)\n  // (value, array, flag)\n  zscore: function zscore() {\n    var args = slice.call(arguments);\n    if (isNumber(args[1])) {\n      return (args[0] - args[1]) / args[2];\n    }\n    return (args[0] - jStat.mean(args[1])) / jStat.stdev(args[1], args[2]);\n  },\n\n  // 3 different paramter lists:\n  // (value, mean, sd, sides)\n  // (zscore, sides)\n  // (value, array, sides, flag)\n  ztest: function ztest() {\n    var args = slice.call(arguments);\n    var z;\n    if (isArray(args[1])) {\n      // (value, array, sides, flag)\n      z = jStat.zscore(args[0],args[1],args[3]);\n      return (args[2] === 1) ?\n        (jStat.normal.cdf(-Math.abs(z), 0, 1)) :\n        (jStat.normal.cdf(-Math.abs(z), 0, 1)*2);\n    } else {\n      if (args.length > 2) {\n        // (value, mean, sd, sides)\n        z = jStat.zscore(args[0],args[1],args[2]);\n        return (args[3] === 1) ?\n          (jStat.normal.cdf(-Math.abs(z),0,1)) :\n          (jStat.normal.cdf(-Math.abs(z),0,1)* 2);\n      } else {\n        // (zscore, sides)\n        z = args[0];\n        return (args[1] === 1) ?\n          (jStat.normal.cdf(-Math.abs(z),0,1)) :\n          (jStat.normal.cdf(-Math.abs(z),0,1)*2);\n      }\n    }\n  }\n});\n\njStat.extend(jStat.fn, {\n  zscore: function zscore(value, flag) {\n    return (value - this.mean()) / this.stdev(flag);\n  },\n\n  ztest: function ztest(value, sides, flag) {\n    var zscore = Math.abs(this.zscore(value, flag));\n    return (sides === 1) ?\n      (jStat.normal.cdf(-zscore, 0, 1)) :\n      (jStat.normal.cdf(-zscore, 0, 1) * 2);\n  }\n});\n\n// T Statistics\njStat.extend({\n  // 2 parameter lists\n  // (value, mean, sd, n)\n  // (value, array)\n  tscore: function tscore() {\n    var args = slice.call(arguments);\n    return (args.length === 4) ?\n      ((args[0] - args[1]) / (args[2] / Math.sqrt(args[3]))) :\n      ((args[0] - jStat.mean(args[1])) /\n       (jStat.stdev(args[1], true) / Math.sqrt(args[1].length)));\n  },\n\n  // 3 different paramter lists:\n  // (value, mean, sd, n, sides)\n  // (tscore, n, sides)\n  // (value, array, sides)\n  ttest: function ttest() {\n    var args = slice.call(arguments);\n    var tscore;\n    if (args.length === 5) {\n      tscore = Math.abs(jStat.tscore(args[0], args[1], args[2], args[3]));\n      return (args[4] === 1) ?\n        (jStat.studentt.cdf(-tscore, args[3]-1)) :\n        (jStat.studentt.cdf(-tscore, args[3]-1)*2);\n    }\n    if (isNumber(args[1])) {\n      tscore = Math.abs(args[0])\n      return (args[2] == 1) ?\n        (jStat.studentt.cdf(-tscore, args[1]-1)) :\n        (jStat.studentt.cdf(-tscore, args[1]-1) * 2);\n    }\n    tscore = Math.abs(jStat.tscore(args[0], args[1]))\n    return (args[2] == 1) ?\n      (jStat.studentt.cdf(-tscore, args[1].length-1)) :\n      (jStat.studentt.cdf(-tscore, args[1].length-1) * 2);\n  }\n});\n\njStat.extend(jStat.fn, {\n  tscore: function tscore(value) {\n    return (value - this.mean()) / (this.stdev(true) / Math.sqrt(this.cols()));\n  },\n\n  ttest: function ttest(value, sides) {\n    return (sides === 1) ?\n      (1 - jStat.studentt.cdf(Math.abs(this.tscore(value)), this.cols()-1)) :\n      (jStat.studentt.cdf(-Math.abs(this.tscore(value)), this.cols()-1)*2);\n  }\n});\n\n// F Statistics\njStat.extend({\n  // Paramter list is as follows:\n  // (array1, array2, array3, ...)\n  // or it is an array of arrays\n  // array of arrays conversion\n  anovafscore: function anovafscore() {\n    var args = slice.call(arguments),\n    expVar, sample, sampMean, sampSampMean, tmpargs, unexpVar, i, j;\n    if (args.length === 1) {\n      tmpargs = new Array(args[0].length);\n      for (i = 0; i < args[0].length; i++) {\n        tmpargs[i] = args[0][i];\n      }\n      args = tmpargs;\n    }\n    // Builds sample array\n    sample = new Array();\n    for (i = 0; i < args.length; i++) {\n      sample = sample.concat(args[i]);\n    }\n    sampMean = jStat.mean(sample);\n    // Computes the explained variance\n    expVar = 0;\n    for (i = 0; i < args.length; i++) {\n      expVar = expVar + args[i].length * Math.pow(jStat.mean(args[i]) - sampMean, 2);\n    }\n    expVar /= (args.length - 1);\n    // Computes unexplained variance\n    unexpVar = 0;\n    for (i = 0; i < args.length; i++) {\n      sampSampMean = jStat.mean(args[i]);\n      for (j = 0; j < args[i].length; j++) {\n        unexpVar += Math.pow(args[i][j] - sampSampMean, 2);\n      }\n    }\n    unexpVar /= (sample.length - args.length);\n    return expVar / unexpVar;\n  },\n\n  // 2 different paramter setups\n  // (array1, array2, array3, ...)\n  // (anovafscore, df1, df2)\n  anovaftest: function anovaftest() {\n    var args = slice.call(arguments),\n    df1, df2, n, i;\n    if (isNumber(args[0])) {\n      return 1 - jStat.centralF.cdf(args[0], args[1], args[2]);\n    }\n    var anovafscore = jStat.anovafscore(args);\n    df1 = args.length - 1;\n    n = 0;\n    for (i = 0; i < args.length; i++) {\n      n = n + args[i].length;\n    }\n    df2 = n - df1 - 1;\n    return 1 - jStat.centralF.cdf(anovafscore, df1, df2);\n  },\n\n  ftest: function ftest(fscore, df1, df2) {\n    return 1 - jStat.centralF.cdf(fscore, df1, df2);\n  }\n});\n\njStat.extend(jStat.fn, {\n  anovafscore: function anovafscore() {\n    return jStat.anovafscore(this.toArray());\n  },\n\n  anovaftes: function anovaftes() {\n    var n = 0;\n    var i;\n    for (i = 0; i < this.length; i++) {\n      n = n + this[i].length;\n    }\n    return jStat.ftest(this.anovafscore(), this.length - 1, n - this.length);\n  }\n});\n\n// Tukey's range test\njStat.extend({\n  // 2 parameter lists\n  // (mean1, mean2, n1, n2, sd)\n  // (array1, array2, sd)\n  qscore: function qscore() {\n    var args = slice.call(arguments);\n    var mean1, mean2, n1, n2, sd;\n    if (isNumber(args[0])) {\n        mean1 = args[0];\n        mean2 = args[1];\n        n1 = args[2];\n        n2 = args[3];\n        sd = args[4];\n    } else {\n        mean1 = jStat.mean(args[0]);\n        mean2 = jStat.mean(args[1]);\n        n1 = args[0].length;\n        n2 = args[1].length;\n        sd = args[2];\n    }\n    return Math.abs(mean1 - mean2) / (sd * Math.sqrt((1 / n1 + 1 / n2) / 2));\n  },\n\n  // 3 different parameter lists:\n  // (qscore, n, k)\n  // (mean1, mean2, n1, n2, sd, n, k)\n  // (array1, array2, sd, n, k)\n  qtest: function qtest() {\n    var args = slice.call(arguments);\n\n    var qscore;\n    if (args.length === 3) {\n      qscore = args[0];\n      args = args.slice(1);\n    } else if (args.length === 7) {\n      qscore = jStat.qscore(args[0], args[1], args[2], args[3], args[4]);\n      args = args.slice(5);\n    } else {\n      qscore = jStat.qscore(args[0], args[1], args[2]);\n      args = args.slice(3);\n    }\n\n    var n = args[0];\n    var k = args[1];\n\n    return 1 - jStat.tukey.cdf(qscore, k, n - k);\n  },\n\n  tukeyhsd: function tukeyhsd(arrays) {\n    var sd = jStat.pooledstdev(arrays);\n    var means = arrays.map(function (arr) {return jStat.mean(arr);});\n    var n = arrays.reduce(function (n, arr) {return n + arr.length;}, 0);\n\n    var results = [];\n    for (var i = 0; i < arrays.length; ++i) {\n        for (var j = i + 1; j < arrays.length; ++j) {\n            var p = jStat.qtest(means[i], means[j], arrays[i].length, arrays[j].length, sd, n, arrays.length);\n            results.push([[i, j], p]);\n        }\n    }\n\n    return results;\n  }\n});\n\n// Error Bounds\njStat.extend({\n  // 2 different parameter setups\n  // (value, alpha, sd, n)\n  // (value, alpha, array)\n  normalci: function normalci() {\n    var args = slice.call(arguments),\n    ans = new Array(2),\n    change;\n    if (args.length === 4) {\n      change = Math.abs(jStat.normal.inv(args[1] / 2, 0, 1) *\n                        args[2] / Math.sqrt(args[3]));\n    } else {\n      change = Math.abs(jStat.normal.inv(args[1] / 2, 0, 1) *\n                        jStat.stdev(args[2]) / Math.sqrt(args[2].length));\n    }\n    ans[0] = args[0] - change;\n    ans[1] = args[0] + change;\n    return ans;\n  },\n\n  // 2 different parameter setups\n  // (value, alpha, sd, n)\n  // (value, alpha, array)\n  tci: function tci() {\n    var args = slice.call(arguments),\n    ans = new Array(2),\n    change;\n    if (args.length === 4) {\n      change = Math.abs(jStat.studentt.inv(args[1] / 2, args[3] - 1) *\n                        args[2] / Math.sqrt(args[3]));\n    } else {\n      change = Math.abs(jStat.studentt.inv(args[1] / 2, args[2].length - 1) *\n                        jStat.stdev(args[2], true) / Math.sqrt(args[2].length));\n    }\n    ans[0] = args[0] - change;\n    ans[1] = args[0] + change;\n    return ans;\n  },\n\n  significant: function significant(pvalue, alpha) {\n    return pvalue < alpha;\n  }\n});\n\njStat.extend(jStat.fn, {\n  normalci: function normalci(value, alpha) {\n    return jStat.normalci(value, alpha, this.toArray());\n  },\n\n  tci: function tci(value, alpha) {\n    return jStat.tci(value, alpha, this.toArray());\n  }\n});\n\n// internal method for calculating the z-score for a difference of proportions test\nfunction differenceOfProportions(p1, n1, p2, n2) {\n  if (p1 > 1 || p2 > 1 || p1 <= 0 || p2 <= 0) {\n    throw new Error(\"Proportions should be greater than 0 and less than 1\")\n  }\n  var pooled = (p1 * n1 + p2 * n2) / (n1 + n2);\n  var se = Math.sqrt(pooled * (1 - pooled) * ((1/n1) + (1/n2)));\n  return (p1 - p2) / se;\n}\n\n// Difference of Proportions\njStat.extend(jStat.fn, {\n  oneSidedDifferenceOfProportions: function oneSidedDifferenceOfProportions(p1, n1, p2, n2) {\n    var z = differenceOfProportions(p1, n1, p2, n2);\n    return jStat.ztest(z, 1);\n  },\n\n  twoSidedDifferenceOfProportions: function twoSidedDifferenceOfProportions(p1, n1, p2, n2) {\n    var z = differenceOfProportions(p1, n1, p2, n2);\n    return jStat.ztest(z, 2);\n  }\n});\n\n}(jStat, Math));\njStat.models = (function(){\n  function sub_regress(exog) {\n    var var_count = exog[0].length;\n    var modelList = jStat.arange(var_count).map(function(endog_index) {\n      var exog_index =\n          jStat.arange(var_count).filter(function(i){return i!==endog_index});\n      return ols(jStat.col(exog, endog_index).map(function(x){ return x[0] }),\n                 jStat.col(exog, exog_index))\n    });\n    return modelList;\n  }\n\n  // do OLS model regress\n  // exog have include const columns ,it will not generate it .In fact, exog is\n  // \"design matrix\" look at\n  //https://en.wikipedia.org/wiki/Design_matrix\n  function ols(endog, exog) {\n    var nobs = endog.length;\n    var df_model = exog[0].length - 1;\n    var df_resid = nobs-df_model - 1;\n    var coef = jStat.lstsq(exog, endog);\n    var predict =\n        jStat.multiply(exog, coef.map(function(x) { return [x] }))\n            .map(function(p) { return p[0] });\n    var resid = jStat.subtract(endog, predict);\n    var ybar = jStat.mean(endog);\n    // constant cause problem\n    // var SST = jStat.sum(endog.map(function(y) {\n    //   return Math.pow(y-ybar,2);\n    // }));\n    var SSE = jStat.sum(predict.map(function(f) {\n      return Math.pow(f - ybar, 2);\n    }));\n    var SSR = jStat.sum(endog.map(function(y, i) {\n      return Math.pow(y - predict[i], 2);\n    }));\n    var SST = SSE + SSR;\n    var R2 = (SSE / SST);\n    return {\n        exog:exog,\n        endog:endog,\n        nobs:nobs,\n        df_model:df_model,\n        df_resid:df_resid,\n        coef:coef,\n        predict:predict,\n        resid:resid,\n        ybar:ybar,\n        SST:SST,\n        SSE:SSE,\n        SSR:SSR,\n        R2:R2\n    };\n  }\n\n  // H0: b_I=0\n  // H1: b_I!=0\n  function t_test(model) {\n    var subModelList = sub_regress(model.exog);\n    //var sigmaHat=jStat.stdev(model.resid);\n    var sigmaHat = Math.sqrt(model.SSR / (model.df_resid));\n    var seBetaHat = subModelList.map(function(mod) {\n      var SST = mod.SST;\n      var R2 = mod.R2;\n      return sigmaHat / Math.sqrt(SST * (1 - R2));\n    });\n    var tStatistic = model.coef.map(function(coef, i) {\n      return (coef - 0) / seBetaHat[i];\n    });\n    var pValue = tStatistic.map(function(t) {\n      var leftppf = jStat.studentt.cdf(t, model.df_resid);\n      return (leftppf > 0.5 ? 1 - leftppf : leftppf) * 2;\n    });\n    var c = jStat.studentt.inv(0.975, model.df_resid);\n    var interval95 = model.coef.map(function(coef, i) {\n      var d = c * seBetaHat[i];\n      return [coef - d, coef + d];\n    })\n    return {\n        se: seBetaHat,\n        t: tStatistic,\n        p: pValue,\n        sigmaHat: sigmaHat,\n        interval95: interval95\n    };\n  }\n\n  function F_test(model) {\n    var F_statistic =\n        (model.R2 / model.df_model) / ((1 - model.R2) / model.df_resid);\n    var fcdf = function(x, n1, n2) {\n      return jStat.beta.cdf(x / (n2 / n1 + x), n1 / 2, n2 / 2)\n    }\n    var pvalue = 1 - fcdf(F_statistic, model.df_model, model.df_resid);\n    return { F_statistic: F_statistic, pvalue: pvalue };\n  }\n\n  function ols_wrap(endog, exog) {\n    var model = ols(endog,exog);\n    var ttest = t_test(model);\n    var ftest = F_test(model);\n    // Provide the Wherry / Ezekiel / McNemar / Cohen Adjusted R^2\n    // Which matches the 'adjusted R^2' provided by R's lm package\n    var adjust_R2 =\n        1 - (1 - model.R2) * ((model.nobs - 1) / (model.df_resid));\n    model.t = ttest;\n    model.f = ftest;\n    model.adjust_R2 = adjust_R2;\n    return model;\n  }\n\n  return { ols: ols_wrap };\n})();\n//To regress, simply build X matrix\n//(append column of 1's) using\n//buildxmatrix and build the Y\n//matrix using buildymatrix\n//(simply the transpose)\n//and run regress.\n\n\n\n//Regressions\n\njStat.extend({\n  buildxmatrix: function buildxmatrix(){\n    //Parameters will be passed in as such\n    //(array1,array2,array3,...)\n    //as (x1,x2,x3,...)\n    //needs to be (1,x1,x2,x3,...)\n    var matrixRows = new Array(arguments.length);\n    for(var i=0;i<arguments.length;i++){\n      var array = [1];\n      matrixRows[i]= array.concat(arguments[i]);\n    }\n    return jStat(matrixRows);\n\n  },\n\n  builddxmatrix: function builddxmatrix() {\n    //Paramters will be passed in as such\n    //([array1,array2,...]\n    var matrixRows = new Array(arguments[0].length);\n    for(var i=0;i<arguments[0].length;i++){\n      var array = [1]\n      matrixRows[i]= array.concat(arguments[0][i]);\n    }\n    return jStat(matrixRows);\n\n  },\n\n  buildjxmatrix: function buildjxmatrix(jMat) {\n    //Builds from jStat Matrix\n    var pass = new Array(jMat.length)\n    for(var i=0;i<jMat.length;i++){\n      pass[i] = jMat[i];\n    }\n    return jStat.builddxmatrix(pass);\n\n  },\n\n  buildymatrix: function buildymatrix(array){\n    return jStat(array).transpose();\n  },\n\n  buildjymatrix: function buildjymatrix(jMat){\n    return jMat.transpose();\n  },\n\n  matrixmult: function matrixmult(A,B){\n    var i, j, k, result, sum;\n    if (A.cols() == B.rows()) {\n      if(B.rows()>1){\n        result = [];\n        for (i = 0; i < A.rows(); i++) {\n          result[i] = [];\n          for (j = 0; j < B.cols(); j++) {\n            sum = 0;\n            for (k = 0; k < A.cols(); k++) {\n              sum += A.toArray()[i][k] * B.toArray()[k][j];\n            }\n            result[i][j] = sum;\n          }\n        }\n        return jStat(result);\n      }\n      result = [];\n      for (i = 0; i < A.rows(); i++) {\n        result[i] = [];\n        for (j = 0; j < B.cols(); j++) {\n          sum = 0;\n          for (k = 0; k < A.cols(); k++) {\n            sum += A.toArray()[i][k] * B.toArray()[j];\n          }\n          result[i][j] = sum;\n        }\n      }\n      return jStat(result);\n    }\n  },\n\n  //regress and regresst to be fixed\n\n  regress: function regress(jMatX,jMatY){\n    //print(\"regressin!\");\n    //print(jMatX.toArray());\n    var innerinv = jStat.xtranspxinv(jMatX);\n    //print(innerinv);\n    var xtransp = jMatX.transpose();\n    var next = jStat.matrixmult(jStat(innerinv),xtransp);\n    return jStat.matrixmult(next,jMatY);\n\n  },\n\n  regresst: function regresst(jMatX,jMatY,sides){\n    var beta = jStat.regress(jMatX,jMatY);\n\n    var compile = {};\n    compile.anova = {};\n    var jMatYBar = jStat.jMatYBar(jMatX, beta);\n    compile.yBar = jMatYBar;\n    var yAverage = jMatY.mean();\n    compile.anova.residuals = jStat.residuals(jMatY, jMatYBar);\n\n    compile.anova.ssr = jStat.ssr(jMatYBar, yAverage);\n    compile.anova.msr = compile.anova.ssr / (jMatX[0].length - 1);\n\n    compile.anova.sse = jStat.sse(jMatY, jMatYBar);\n    compile.anova.mse =\n        compile.anova.sse / (jMatY.length - (jMatX[0].length - 1) - 1);\n\n    compile.anova.sst = jStat.sst(jMatY, yAverage);\n    compile.anova.mst = compile.anova.sst / (jMatY.length - 1);\n\n    compile.anova.r2 = 1 - (compile.anova.sse / compile.anova.sst);\n    if (compile.anova.r2 < 0) compile.anova.r2 = 0;\n\n    compile.anova.fratio = compile.anova.msr / compile.anova.mse;\n    compile.anova.pvalue =\n        jStat.anovaftest(compile.anova.fratio,\n                         jMatX[0].length - 1,\n                         jMatY.length - (jMatX[0].length - 1) - 1);\n\n    compile.anova.rmse = Math.sqrt(compile.anova.mse);\n\n    compile.anova.r2adj = 1 - (compile.anova.mse / compile.anova.mst);\n    if (compile.anova.r2adj < 0) compile.anova.r2adj = 0;\n\n    compile.stats = new Array(jMatX[0].length);\n    var covar = jStat.xtranspxinv(jMatX);\n    var sds, ts, ps;\n\n    for(var i=0; i<beta.length;i++){\n      sds=Math.sqrt(compile.anova.mse * Math.abs(covar[i][i]));\n      ts= Math.abs(beta[i] / sds);\n      ps= jStat.ttest(ts, jMatY.length - jMatX[0].length - 1, sides);\n\n      compile.stats[i]=[beta[i], sds, ts, ps];\n    }\n\n    compile.regress = beta;\n    return compile;\n  },\n\n  xtranspx: function xtranspx(jMatX){\n    return jStat.matrixmult(jMatX.transpose(),jMatX);\n  },\n\n\n  xtranspxinv: function xtranspxinv(jMatX){\n    var inner = jStat.matrixmult(jMatX.transpose(),jMatX);\n    var innerinv = jStat.inv(inner);\n    return innerinv;\n  },\n\n  jMatYBar: function jMatYBar(jMatX, beta) {\n    var yBar = jStat.matrixmult(jMatX, beta);\n    return new jStat(yBar);\n  },\n\n  residuals: function residuals(jMatY, jMatYBar) {\n    return jStat.matrixsubtract(jMatY, jMatYBar);\n  },\n\n  ssr: function ssr(jMatYBar, yAverage) {\n    var ssr = 0;\n    for(var i = 0; i < jMatYBar.length; i++) {\n      ssr += Math.pow(jMatYBar[i] - yAverage, 2);\n    }\n    return ssr;\n  },\n\n  sse: function sse(jMatY, jMatYBar) {\n    var sse = 0;\n    for(var i = 0; i < jMatY.length; i++) {\n      sse += Math.pow(jMatY[i] - jMatYBar[i], 2);\n    }\n    return sse;\n  },\n\n  sst: function sst(jMatY, yAverage) {\n    var sst = 0;\n    for(var i = 0; i < jMatY.length; i++) {\n      sst += Math.pow(jMatY[i] - yAverage, 2);\n    }\n    return sst;\n  },\n\n  matrixsubtract: function matrixsubtract(A,B){\n    var ans = new Array(A.length);\n    for(var i=0;i<A.length;i++){\n      ans[i] = new Array(A[i].length);\n      for(var j=0;j<A[i].length;j++){\n        ans[i][j]=A[i][j]-B[i][j];\n      }\n    }\n    return jStat(ans);\n  }\n});\n  // Make it compatible with previous version.\n  jStat.jStat = jStat;\n\n  return jStat;\n});\n", "/* bessel.js (C) 2013-present SheetJS -- http://sheetjs.com */\n/* vim: set ts=2: */\n/*exported BESSEL */\nvar BESSEL;\n(function (factory) {\n  /*jshint ignore:start */\n  if(typeof DO_NOT_EXPORT_BESSEL === 'undefined') {\n    if('object' === typeof exports) {\n      factory(exports);\n    } else if ('function' === typeof define && define.amd) {\n      define(function () {\n        var module = {};\n        factory(module);\n        return module;\n      });\n    } else {\n      factory(BESSEL = {});\n    }\n  } else {\n    factory(BESSEL = {});\n  }\n  /*jshint ignore:end */\n}(function(BESSEL) {\nBESSEL.version = '1.0.2';\nvar M = Math;\n\nfunction _horner(arr, v) { for(var i = 0, z = 0; i < arr.length; ++i) z = v * z + arr[i]; return z; }\nfunction _bessel_iter(x, n, f0, f1, sign) {\n  if(n === 0) return f0;\n  if(n === 1) return f1;\n  var tdx = 2 / x, f2 = f1;\n  for(var o = 1; o < n; ++o) {\n    f2 = f1 * o * tdx + sign * f0;\n    f0 = f1; f1 = f2;\n  }\n  return f2;\n}\nfunction _bessel_wrap(bessel0, bessel1, name, nonzero, sign) {\n  return function bessel(x,n) {\n    if(nonzero) {\n      if(x === 0) return (nonzero == 1 ? -Infinity : Infinity);\n      else if(x < 0) return NaN;\n    }\n    if(n === 0) return bessel0(x);\n    if(n === 1) return bessel1(x);\n    if(n < 0) return NaN;\n    n|=0;\n    var b0 = bessel0(x), b1 = bessel1(x);\n    return _bessel_iter(x, n, b0, b1, sign);\n  };\n}\nvar besselj = (function() {\n  var W = 0.636619772; // 2 / Math.PI\n\n  var b0_a1a = [57568490574.0, -13362590354.0, 651619640.7, -11214424.18, 77392.33017, -184.9052456].reverse();\n  var b0_a2a = [57568490411.0, 1029532985.0, 9494680.718, 59272.64853, 267.8532712, 1.0].reverse();\n  var b0_a1b = [1.0, -0.1098628627e-2, 0.2734510407e-4, -0.2073370639e-5, 0.2093887211e-6].reverse();\n  var b0_a2b = [-0.1562499995e-1, 0.1430488765e-3, -0.6911147651e-5, 0.7621095161e-6, -0.934935152e-7].reverse();\n\n  function bessel0(x) {\n    var a=0, a1=0, a2=0, y = x * x;\n    if(x < 8) {\n      a1 = _horner(b0_a1a, y);\n      a2 = _horner(b0_a2a, y);\n      a = a1 / a2;\n    } else {\n      var xx = x - 0.785398164;\n      y = 64 / y;\n      a1 = _horner(b0_a1b, y);\n      a2 = _horner(b0_a2b, y);\n      a = M.sqrt(W/x)*(M.cos(xx)*a1-M.sin(xx)*a2*8/x);\n    }\n    return a;\n  }\n\n  var b1_a1a = [72362614232.0, -7895059235.0, 242396853.1, -2972611.439, 15704.48260, -30.16036606].reverse();\n  var b1_a2a = [144725228442.0, 2300535178.0, 18583304.74, 99447.43394, 376.9991397, 1.0].reverse();\n  var b1_a1b = [1.0, 0.183105e-2, -0.3516396496e-4, 0.2457520174e-5, -0.240337019e-6].reverse();\n  var b1_a2b = [0.04687499995, -0.2002690873e-3, 0.8449199096e-5, -0.88228987e-6, 0.105787412e-6].reverse();\n\n  function bessel1(x) {\n    var a=0, a1=0, a2=0, y = x*x, xx = M.abs(x) - 2.356194491;\n    if(Math.abs(x)< 8) {\n      a1 = x*_horner(b1_a1a, y);\n      a2 = _horner(b1_a2a, y);\n      a = a1 / a2;\n    } else {\n      y = 64 / y;\n      a1=_horner(b1_a1b, y);\n      a2=_horner(b1_a2b, y);\n      a=M.sqrt(W/M.abs(x))*(M.cos(xx)*a1-M.sin(xx)*a2*8/M.abs(x));\n      if(x < 0) a = -a;\n    }\n    return a;\n  }\n\n  return function besselj(x, n) {\n    n = Math.round(n);\n    if(!isFinite(x)) return isNaN(x) ? x : 0;\n    if(n < 0) return ((n%2)?-1:1)*besselj(x, -n);\n    if(x < 0) return ((n%2)?-1:1)*besselj(-x, n);\n    if(n === 0) return bessel0(x);\n    if(n === 1) return bessel1(x);\n    if(x === 0) return 0;\n\n    var ret=0.0;\n    if(x > n) {\n      ret = _bessel_iter(x, n, bessel0(x), bessel1(x),-1);\n    } else {\n      var m=2*M.floor((n+M.floor(M.sqrt(40*n)))/2);\n      var jsum=false;\n      var bjp=0.0, sum=0.0;\n      var bj=1.0, bjm = 0.0;\n      var tox = 2 / x;\n      for (var j=m;j>0;j--) {\n        bjm=j*tox*bj-bjp;\n        bjp=bj;\n        bj=bjm;\n        if (M.abs(bj) > 1E10) {\n          bj *= 1E-10;\n          bjp *= 1E-10;\n          ret *= 1E-10;\n          sum *= 1E-10;\n        }\n        if (jsum) sum += bj;\n        jsum=!jsum;\n        if (j == n) ret=bjp;\n      }\n      sum=2.0*sum-bj;\n      ret /= sum;\n    }\n    return ret;\n  };\n})();\nvar bessely = (function() {\n  var W = 0.636619772;\n\n  var b0_a1a = [-2957821389.0, 7062834065.0, -512359803.6, 10879881.29, -86327.92757, 228.4622733].reverse();\n  var b0_a2a = [40076544269.0, 745249964.8, 7189466.438, 47447.26470, 226.1030244, 1.0].reverse();\n  var b0_a1b = [1.0, -0.1098628627e-2, 0.2734510407e-4, -0.2073370639e-5, 0.2093887211e-6].reverse();\n  var b0_a2b = [-0.1562499995e-1, 0.1430488765e-3, -0.6911147651e-5, 0.7621095161e-6, -0.934945152e-7].reverse();\n\n  function bessel0(x) {\n    var a=0, a1=0, a2=0, y = x * x, xx = x - 0.785398164;\n    if(x < 8) {\n      a1 = _horner(b0_a1a, y);\n      a2 = _horner(b0_a2a, y);\n      a = a1/a2 + W * besselj(x,0) * M.log(x);\n    } else {\n      y = 64 / y;\n      a1 = _horner(b0_a1b, y);\n      a2 = _horner(b0_a2b, y);\n      a = M.sqrt(W/x)*(M.sin(xx)*a1+M.cos(xx)*a2*8/x);\n    }\n    return a;\n  }\n\n  var b1_a1a = [-0.4900604943e13, 0.1275274390e13, -0.5153438139e11, 0.7349264551e9, -0.4237922726e7, 0.8511937935e4].reverse();\n  var b1_a2a = [0.2499580570e14, 0.4244419664e12, 0.3733650367e10, 0.2245904002e8, 0.1020426050e6, 0.3549632885e3, 1].reverse();\n  var b1_a1b = [1.0, 0.183105e-2, -0.3516396496e-4, 0.2457520174e-5, -0.240337019e-6].reverse();\n  var b1_a2b = [0.04687499995, -0.2002690873e-3, 0.8449199096e-5, -0.88228987e-6, 0.105787412e-6].reverse();\n\n  function bessel1(x) {\n    var a=0, a1=0, a2=0, y = x*x, xx = x - 2.356194491;\n    if(x < 8) {\n      a1 = x*_horner(b1_a1a, y);\n      a2 = _horner(b1_a2a, y);\n      a = a1/a2 + W * (besselj(x,1) * M.log(x) - 1 / x);\n    } else {\n      y = 64 / y;\n      a1=_horner(b1_a1b, y);\n      a2=_horner(b1_a2b, y);\n      a=M.sqrt(W/x)*(M.sin(xx)*a1+M.cos(xx)*a2*8/x);\n    }\n    return a;\n  }\n\n  return _bessel_wrap(bessel0, bessel1, 'BESSELY', 1, -1);\n})();\nvar besseli = (function() {\n  var b0_a = [1.0, 3.5156229, 3.0899424, 1.2067492, 0.2659732, 0.360768e-1, 0.45813e-2].reverse();\n  var b0_b = [0.39894228, 0.1328592e-1, 0.225319e-2, -0.157565e-2, 0.916281e-2, -0.2057706e-1, 0.2635537e-1, -0.1647633e-1, 0.392377e-2].reverse();\n\n  function bessel0(x) {\n    if(x <= 3.75) return _horner(b0_a, x*x/(3.75*3.75));\n    return M.exp(M.abs(x))/M.sqrt(M.abs(x))*_horner(b0_b, 3.75/M.abs(x));\n  }\n\n  var b1_a = [0.5, 0.87890594, 0.51498869, 0.15084934, 0.2658733e-1, 0.301532e-2, 0.32411e-3].reverse();\n  var b1_b = [0.39894228, -0.3988024e-1, -0.362018e-2, 0.163801e-2, -0.1031555e-1, 0.2282967e-1, -0.2895312e-1, 0.1787654e-1, -0.420059e-2].reverse();\n\n  function bessel1(x) {\n    if(x < 3.75) return x * _horner(b1_a, x*x/(3.75*3.75));\n    return (x < 0 ? -1 : 1) * M.exp(M.abs(x))/M.sqrt(M.abs(x))*_horner(b1_b, 3.75/M.abs(x));\n  }\n\n  return function besseli(x, n) {\n    n = Math.round(n);\n    if(n === 0) return bessel0(x);\n    if(n === 1) return bessel1(x);\n    if(n < 0) return NaN;\n    if(M.abs(x) === 0) return 0;\n    if(x == Infinity) return Infinity;\n\n    var ret = 0.0, j, tox = 2 / M.abs(x), bip = 0.0, bi=1.0, bim=0.0;\n    var m=2*M.round((n+M.round(M.sqrt(40*n)))/2);\n    for (j=m;j>0;j--) {\n      bim=j*tox*bi + bip;\n      bip=bi; bi=bim;\n      if (M.abs(bi) > 1E10) {\n        bi *= 1E-10;\n        bip *= 1E-10;\n        ret *= 1E-10;\n      }\n      if(j == n) ret = bip;\n    }\n    ret *= besseli(x, 0) / bi;\n    return x < 0 && (n%2) ? -ret : ret;\n  };\n\n})();\n\nvar besselk = (function() {\n  var b0_a = [-0.57721566, 0.42278420, 0.23069756, 0.3488590e-1, 0.262698e-2, 0.10750e-3, 0.74e-5].reverse();\n  var b0_b = [1.25331414, -0.7832358e-1, 0.2189568e-1, -0.1062446e-1, 0.587872e-2, -0.251540e-2, 0.53208e-3].reverse();\n\n  function bessel0(x) {\n    if(x <= 2) return -M.log(x/2) * besseli(x,0) + _horner(b0_a, x*x/4);\n    return M.exp(-x) / M.sqrt(x) * _horner(b0_b, 2/x);\n  }\n\n  var b1_a = [1.0, 0.15443144, -0.67278579, -0.18156897, -0.1919402e-1, -0.110404e-2, -0.4686e-4].reverse();\n  var b1_b = [1.25331414, 0.23498619, -0.3655620e-1, 0.1504268e-1, -0.780353e-2, 0.325614e-2, -0.68245e-3].reverse();\n\n  function bessel1(x) {\n    if(x <= 2) return M.log(x/2) * besseli(x,1) + (1/x) * _horner(b1_a, x*x/4);\n    return M.exp(-x)/M.sqrt(x)*_horner(b1_b, 2/x);\n  }\n\n  return _bessel_wrap(bessel0, bessel1, 'BESSELK', 2, 1);\n})();\nBESSEL.besselj = besselj;\nBESSEL.bessely = bessely;\nBESSEL.besseli = besseli;\nBESSEL.besselk = besselk;\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAU,QAAQ,SAAS;AACxB,UAAI,OAAO,YAAY,UAAU;AAC7B,eAAO,UAAU,QAAQ;AAAA,MAC7B,WAAW,OAAO,WAAW,cAAc,OAAO,KAAK;AACnD,eAAO,OAAO;AAAA,MAClB,OAAO;AACH,eAAO,QAAQ,QAAQ;AAAA,MAC3B;AAAA,IACJ,GAAG,SAAM,WAAY;AACrB,UAAI,QAAS,SAASA,OAAMC,YAAW;AAGvC,YAAI,SAAS,MAAM,UAAU;AAC7B,YAAI,QAAQ,MAAM,UAAU;AAC5B,YAAI,WAAW,OAAO,UAAU;AAIhC,iBAAS,QAAQ,GAAG,GAAG;AACrB,cAAI,MAAM,IAAI,IAAI,IAAI;AACtB,iBAAOD,MAAK;AAAA,YAAI;AAAA,YACA,KAAK,CAAC,EAAEA,MAAK,IAAM,MAAM,IAAK,MAAM,CAAC,GAAI,IAAIA,MAAK;AAAA,UAAO;AAAA,QAC3E;AAGA,YAAI,UAAU,MAAM,WAAW,SAASE,SAAQ,KAAK;AACnD,iBAAO,SAAS,KAAK,GAAG,MAAM;AAAA,QAChC;AAGA,iBAAS,WAAW,KAAK;AACvB,iBAAO,SAAS,KAAK,GAAG,MAAM;AAAA,QAChC;AAGA,iBAAS,SAAS,KAAK;AACrB,iBAAQ,OAAO,QAAQ,WAAY,MAAM,QAAQ,IAAI;AAAA,QACvD;AAIA,iBAAS,SAAS,KAAK;AACrB,iBAAO,OAAO,MAAM,CAAC,GAAG,GAAG;AAAA,QAC7B;AAIA,iBAASC,SAAQ;AACf,iBAAO,IAAIA,OAAM,MAAM,SAAS;AAAA,QAClC;AAIA,QAAAA,OAAM,KAAKA,OAAM;AAKjB,QAAAA,OAAM,QAAQ,SAAS,MAAM,MAAM;AAEjC,cAAI,QAAQ,KAAK,CAAC,CAAC,GAAG;AAEpB,gBAAI,QAAQ,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG;AAEvB,kBAAI,WAAW,KAAK,CAAC,CAAC;AACpB,qBAAK,CAAC,IAAIA,OAAM,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAEtC,uBAAS,IAAI,GAAG,IAAI,KAAK,CAAC,EAAE,QAAQ;AAClC,qBAAK,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AACrB,mBAAK,SAAS,KAAK,CAAC,EAAE;AAAA,YAGxB,OAAO;AACL,mBAAK,CAAC,IAAI,WAAW,KAAK,CAAC,CAAC,IAAIA,OAAM,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AACpE,mBAAK,SAAS;AAAA,YAChB;AAAA,UAGF,WAAW,SAAS,KAAK,CAAC,CAAC,GAAG;AAC5B,iBAAK,CAAC,IAAIA,OAAM,IAAI,MAAM,MAAM,IAAI;AACpC,iBAAK,SAAS;AAAA,UAGhB,WAAW,KAAK,CAAC,aAAaA,QAAO;AAEnC,mBAAOA,OAAM,KAAK,CAAC,EAAE,QAAQ,CAAC;AAAA,UAKhC,OAAO;AACL,iBAAK,CAAC,IAAI,CAAC;AACX,iBAAK,SAAS;AAAA,UAChB;AAEA,iBAAO;AAAA,QACT;AACA,QAAAA,OAAM,MAAM,YAAYA,OAAM;AAC9B,QAAAA,OAAM,MAAM,cAAcA;AAK1B,QAAAA,OAAM,QAAQ;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAGA,QAAAA,OAAM,aAAaH,MAAK;AACxB,QAAAG,OAAM,YAAY,SAAS,UAAU,IAAI;AACvC,cAAI,OAAO,OAAO;AAChB,kBAAM,IAAI,UAAU,sBAAsB;AAC5C,UAAAA,OAAM,aAAa;AAAA,QACrB;AAKA,QAAAA,OAAM,SAAS,SAAS,OAAO,KAAK;AAClC,cAAI,GAAG;AAEP,cAAI,UAAU,WAAW,GAAG;AAC1B,iBAAK,KAAK;AACR,cAAAA,OAAM,CAAC,IAAI,IAAI,CAAC;AAClB,mBAAO;AAAA,UACT;AAEA,eAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,iBAAK,KAAK,UAAU,CAAC;AACnB,kBAAI,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC;AAAA,UAC3B;AAEA,iBAAO;AAAA,QACT;AAIA,QAAAA,OAAM,OAAO,SAAS,KAAK,KAAK;AAC9B,iBAAO,IAAI,UAAU;AAAA,QACvB;AAIA,QAAAA,OAAM,OAAO,SAAS,KAAK,KAAK;AAC9B,iBAAO,IAAI,CAAC,EAAE,UAAU;AAAA,QAC1B;AAIA,QAAAA,OAAM,aAAa,SAAS,WAAW,KAAK;AAC1C,iBAAO;AAAA,YACL,MAAMA,OAAM,KAAK,GAAG;AAAA,YACpB,MAAMA,OAAM,KAAK,GAAG;AAAA,UACtB;AAAA,QACF;AAIA,QAAAA,OAAM,MAAM,SAAS,IAAI,KAAK,OAAO;AACnC,cAAI,QAAQ,KAAK,GAAG;AAClB,mBAAO,MAAM,IAAI,SAAS,GAAG;AAC3B,qBAAOA,OAAM,IAAI,KAAK,CAAC;AAAA,YACzB,CAAC;AAAA,UACH;AACA,iBAAO,IAAI,KAAK;AAAA,QAClB;AAKA,QAAAA,OAAM,OAAO,SAAS,KAAK,KAAK,GAAG;AACjC,iBAAOA,OAAM,IAAI,KAAK,CAAC;AAAA,QACzB;AAKA,QAAAA,OAAM,MAAM,SAAS,IAAI,KAAK,OAAO;AACnC,cAAI,QAAQ,KAAK,GAAG;AAClB,gBAAI,SAASA,OAAM,OAAO,IAAI,MAAM,EAAE,IAAI,WAAW;AACnD,qBAAO,IAAI,MAAM,MAAM,MAAM;AAAA,YAC/B,CAAC;AACD,kBAAM,QAAQ,SAAS,KAAKC,IAAE;AAC5B,cAAAD,OAAM,OAAO,IAAI,MAAM,EAAE,QAAQ,SAAS,GAAG;AAC3C,uBAAO,CAAC,EAAEC,EAAC,IAAI,IAAI,CAAC,EAAE,GAAG;AAAA,cAC3B,CAAC;AAAA,YACH,CAAC;AACD,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,IAAI,MAAM,IAAI,MAAM;AACjC,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC9B,mBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC;AAC5B,iBAAO;AAAA,QACT;AAKA,QAAAD,OAAM,OAAO,SAAS,KAAK,KAAK,GAAG;AACjC,iBAAOA,OAAM,IAAI,KAAK,CAAC,EAAE,IAAI,SAAS,GAAE;AAAE,mBAAO,EAAE,CAAC;AAAA,UAAE,CAAC;AAAA,QACzD;AAIA,QAAAA,OAAM,OAAO,SAAS,KAAK,KAAK;AAC9B,cAAI,OAAOA,OAAM,KAAK,GAAG;AACzB,cAAI,MAAM,IAAI,MAAM,IAAI;AACxB,mBAAS,MAAM,GAAG,MAAM,MAAM;AAC5B,gBAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC;AAC3B,iBAAO;AAAA,QACT;AAIA,QAAAA,OAAM,WAAW,SAAS,SAAS,KAAK;AACtC,cAAI,OAAOA,OAAM,KAAK,GAAG,IAAI;AAC7B,cAAI,MAAM,IAAI,MAAM,IAAI;AACxB,mBAAS,IAAI,GAAG,QAAQ,GAAG,QAAQ;AACjC,gBAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;AACxB,iBAAO;AAAA,QACT;AAGA,QAAAA,OAAM,YAAY,SAAS,UAAU,KAAK;AACxC,cAAI,MAAM,CAAC;AACX,cAAI,QAAQ,MAAM,MAAM,GAAG;AAG3B,cAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;AACjB,kBAAM,CAAC,GAAG;AAEZ,iBAAO,IAAI;AACX,iBAAO,IAAI,CAAC,EAAE;AAEd,eAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACzB,qBAAS,IAAI,MAAM,IAAI;AACvB,iBAAK,IAAI,GAAG,IAAI,MAAM;AACpB,qBAAO,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;AACtB,gBAAI,KAAK,MAAM;AAAA,UACjB;AAGA,iBAAO,IAAI,WAAW,IAAI,IAAI,CAAC,IAAI;AAAA,QACrC;AAKA,QAAAA,OAAM,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS;AAC3C,cAAI,KAAK,MAAM,MAAM,KAAK;AAE1B,cAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;AACjB,kBAAM,CAAC,GAAG;AAEZ,iBAAO,IAAI;AACX,iBAAO,IAAI,CAAC,EAAE;AACd,gBAAM,UAAU,MAAM,IAAI,MAAM,IAAI;AAEpC,eAAK,MAAM,GAAG,MAAM,MAAM,OAAO;AAE/B,gBAAI,CAAC,IAAI,GAAG;AACV,kBAAI,GAAG,IAAI,IAAI,MAAM,IAAI;AAC3B,iBAAK,MAAM,GAAG,MAAM,MAAM;AACxB,kBAAI,GAAG,EAAE,GAAG,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,KAAK,GAAG;AAAA,UAChD;AAEA,iBAAO,IAAI,WAAW,IAAI,IAAI,CAAC,IAAI;AAAA,QACrC;AAIA,QAAAA,OAAM,YAAY,SAAS,UAAU,KAAK,MAAM,SAAS;AACvD,cAAI,KAAK,MAAM,MAAM,KAAK;AAE1B,cAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;AACjB,kBAAM,CAAC,GAAG;AAEZ,iBAAO,IAAI;AACX,iBAAO,IAAI,CAAC,EAAE;AACd,gBAAM,UAAU,MAAM,IAAI,MAAM,IAAI;AAEpC,eAAK,MAAM,GAAG,MAAM,MAAM,OAAO;AAE/B,gBAAI,CAAC,IAAI,GAAG;AACV,kBAAI,GAAG,IAAI,IAAI,MAAM,IAAI;AAC3B,gBAAI,OAAO;AACT,kBAAI,GAAG,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;AAC1B,iBAAK,MAAM,GAAG,MAAM,MAAM;AACxB,kBAAI,GAAG,EAAE,GAAG,IAAI,KAAK,IAAI,GAAG,EAAE,MAAI,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC;AAAA,UACvD;AACA,iBAAO,IAAI,WAAW,IAAI,IAAI,CAAC,IAAI;AAAA,QACrC;AAIA,QAAAA,OAAM,QAAQ,SAAS,MAAM,KAAK,MAAM;AACtC,iBAAOA,OAAM,IAAI,KAAK,MAAM,IAAI;AAAA,QAClC;AAIA,QAAAA,OAAM,SAAS,SAAU,OAAO,MAAM,MAAM,MAAM;AAChD,cAAI,MAAM,IAAI,MAAM,IAAI;AACxB,cAAI,GAAG;AAEP,cAAI,WAAW,IAAI,GAAG;AACpB,mBAAO;AACP,mBAAO;AAAA,UACT;AAEA,eAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACzB,gBAAI,CAAC,IAAI,IAAI,MAAM,IAAI;AACvB,iBAAK,IAAI,GAAG,IAAI,MAAM;AACpB,kBAAI,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,UACzB;AAEA,iBAAO;AAAA,QACT;AAGA,iBAAS,UAAU;AAAE,iBAAO;AAAA,QAAG;AAI/B,QAAAA,OAAM,QAAQ,SAAS,MAAM,MAAM,MAAM;AACvC,cAAI,CAAC,SAAS,IAAI;AAChB,mBAAO;AACT,iBAAOA,OAAM,OAAO,MAAM,MAAM,OAAO;AAAA,QACzC;AAGA,iBAAS,SAAS;AAAE,iBAAO;AAAA,QAAG;AAI9B,QAAAA,OAAM,OAAO,SAAS,KAAK,MAAM,MAAM;AACrC,cAAI,CAAC,SAAS,IAAI;AAChB,mBAAO;AACT,iBAAOA,OAAM,OAAO,MAAM,MAAM,MAAM;AAAA,QACxC;AAIA,QAAAA,OAAM,OAAO,SAAS,KAAK,MAAM,MAAM;AACrC,cAAI,CAAC,SAAS,IAAI;AAChB,mBAAO;AACT,iBAAOA,OAAM,OAAO,MAAM,MAAMA,OAAM,UAAU;AAAA,QAClD;AAGA,iBAAS,SAAS,GAAG,GAAG;AAAE,iBAAO,MAAM,IAAI,IAAI;AAAA,QAAG;AAIlD,QAAAA,OAAM,WAAW,SAAS,SAAS,MAAM,MAAM;AAC7C,cAAI,CAAC,SAAS,IAAI;AAChB,mBAAO;AACT,iBAAOA,OAAM,OAAO,MAAM,MAAM,QAAQ;AAAA,QAC1C;AAIA,QAAAA,OAAM,YAAY,SAAS,UAAU,KAAK;AACxC,cAAI,OAAO,IAAI;AACf,cAAI,KAAK;AAET,cAAI,IAAI,WAAW,IAAI,CAAC,EAAE;AACxB,mBAAO;AAET,eAAK,MAAM,GAAG,MAAM,MAAM,OAAO;AAC/B,iBAAK,MAAM,GAAG,MAAM,MAAM;AACxB,kBAAI,IAAI,GAAG,EAAE,GAAG,MAAM,IAAI,GAAG,EAAE,GAAG;AAChC,uBAAO;AAAA,UACb;AAEA,iBAAO;AAAA,QACT;AAIA,QAAAA,OAAM,QAAQ,SAAS,MAAM,KAAK;AAChC,iBAAOA,OAAM,MAAM,KAAK,OAAO;AAAA,QACjC;AAIA,QAAAA,OAAM,MAAM,SAAS,IAAI,KAAK,KAAK,QAAQ,MAAM;AAC/C,cAAI,CAAC,WAAW,IAAI;AAClB,mBAAO;AAET,cAAI,MAAM,CAAC;AACX,cAAI,QAAQ,QAAQ,KAAK,GAAG;AAC5B,cAAI,QAAQ,MAAM,QAAQ,MAAM,WAAW,SAAS,KAAK;AACzD,cAAI,UAAU;AACd,cAAI;AAIJ,eAAK,MAAM,GACN,WAAW,OAAO,MAAM,QACxB,OAAO,WAAW,MAAM,QAAQ,OAAO,QAAQ,OAAO,OAAO;AAChE,gBAAI,KAAM,OAAO,KAAK,SAAS,GAAG,IAAI,OAAQ;AAAA,UAChD;AAEA,iBAAO;AAAA,QACT;AAMA,QAAAA,OAAM,SAAS,SAAS,OAAO,OAAO,KAAK,MAAM;AAC/C,cAAI,KAAK,CAAC;AACV,cAAI;AACJ,iBAAO,QAAQ;AACf,cAAI,QAAQF,YAAW;AACrB,kBAAM;AACN,oBAAQ;AAAA,UACV;AACA,cAAI,UAAU,OAAO,SAAS,GAAG;AAC/B,mBAAO,CAAC;AAAA,UACV;AACA,cAAI,QAAQ,OAAO,OAAO,GAAG;AAC3B,mBAAO,CAAC;AAAA,UACV;AACA,cAAI,QAAQ,OAAO,OAAO,GAAG;AAC3B,mBAAO,CAAC;AAAA,UACV;AACA,cAAI,OAAO,GAAG;AACZ,iBAAK,IAAI,OAAO,IAAI,KAAK,KAAK,MAAM;AAClC,iBAAG,KAAK,CAAC;AAAA,YACX;AAAA,UACF,OAAO;AACL,iBAAK,IAAI,OAAO,IAAI,KAAK,KAAK,MAAM;AAClC,iBAAG,KAAK,CAAC;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAOA,QAAAE,OAAM,QAAS,WAAU;AACvB,mBAAS,OAAO,MAAM,OAAO,KAAK,MAAM;AAEtC,gBAAI;AACJ,gBAAI,KAAK,CAAC;AACV,gBAAI,SAAS,KAAK;AAClB,gBAAI,UAAUF,cAAa,QAAQA,cAAa,SAASA,YAAW;AAClE,qBAAOE,OAAM,KAAK,IAAI;AAAA,YACxB;AAEA,oBAAQ,SAAS;AACjB,kBAAM,OAAO,KAAK;AAClB,oBAAQ,SAAS,IAAI,QAAQ,SAAS;AACtC,kBAAM,OAAO,IAAI,MAAM,SAAS;AAChC,mBAAO,QAAQ;AACf,gBAAI,UAAU,OAAO,SAAS,GAAG;AAC/B,qBAAO,CAAC;AAAA,YACV;AACA,gBAAI,QAAQ,OAAO,OAAO,GAAG;AAC3B,qBAAO,CAAC;AAAA,YACV;AACA,gBAAI,QAAQ,OAAO,OAAO,GAAG;AAC3B,qBAAO,CAAC;AAAA,YACV;AACA,gBAAI,OAAO,GAAG;AACZ,mBAAK,IAAI,OAAO,IAAI,KAAK,KAAK,MAAM;AAClC,mBAAG,KAAK,KAAK,CAAC,CAAC;AAAA,cACjB;AAAA,YACF,OAAO;AACL,mBAAK,IAAI,OAAO,IAAI,KAAI,KAAK,MAAM;AACjC,mBAAG,KAAK,KAAK,CAAC,CAAC;AAAA,cACjB;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAEA,mBAASE,OAAM,MAAM,SAAS;AAC5B,gBAAI,UAAU;AACd,sBAAU,WAAW,CAAC;AACtB,gBAAI,SAAS,QAAQ,GAAG,GAAG;AACzB,kBAAI,SAAS,QAAQ,GAAG;AACtB,uBAAO,KAAK,QAAQ,GAAG,EAAE,QAAQ,GAAG;AACtC,kBAAI,MAAMF,OAAM,KAAK,MAAM,QAAQ,GAAG;AACtC,yBAAW,QAAQ,OAAO,CAAC;AAC3B,qBAAO,OAAO,KAAK,SAAS,OAAO,SAAS,KAAK,SAAS,IAAI;AAAA,YAChE;AAEA,gBAAI,SAAS,QAAQ,GAAG,GAAG;AACzB,kBAAI,MAAMA,OAAM,KAAK,MAAM,QAAQ,GAAG;AACtC,yBAAW,QAAQ,OAAO,CAAC;AAC3B,qBAAO,OAAO,KAAK,SAAS,OAAO,SAAS,KAAK,SAAS,IAAI;AAAA,YAChE;AAEA,uBAAW,QAAQ,OAAO,CAAC;AAC3B,uBAAW,QAAQ,OAAO,CAAC;AAC3B,gBAAI,OAAO,OAAO,MAAM,SAAS,OAAO,SAAS,KAAK,SAAS,IAAI;AACnE,mBAAO,KAAK,IAAI,SAASG,MAAK;AAC5B,qBAAO,OAAOA,MAAK,SAAS,OAAO,SAAS,KAAK,SAAS,IAAI;AAAA,YAChE,CAAC;AAAA,UACH;AAEA,iBAAOD;AAAA,QACT,EAAE;AAMF,QAAAF,OAAM,cAAc,SAAS,YAAY,GAAG,SAAS,GAAG;AACtD,cAAI,IAAI;AACR,cAAI,SAAS,QAAQ,GAAG,GAAG;AACzB,gBAAI,SAAS,QAAQ,GAAG;AACtB,qBAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ,GAAG,IAAI;AACvC,oBAAQ,MAAM,QAAQ,OAAO,CAAC;AAC9B,oBAAQ,IAAI,QAAQ,QAAQ,IAAI,SAAS;AACzC,oBAAQ,IAAI,MAAM,QAAQ,IAAI,OAAO,EAAE,CAAC,EAAE;AAC1C,oBAAQ,IAAI,OAAO,QAAQ,IAAI,QAAQ;AACvC,iBAAKA,OAAM;AAAA,cAAO,QAAQ,IAAI;AAAA,cACRH,MAAK,IAAI,EAAE,QAAQ,QAAQ,IAAI,GAAG;AAAA,cAClC,QAAQ,IAAI;AAAA,YAAI;AACtC,gBAAI,IAAI,QAAQ;AAChB,eAAG,QAAQ,SAASO,IAAG,GAAG;AACxB,gBAAE,CAAC,EAAEA,EAAC,IAAI,EAAE,CAAC;AAAA,YACf,CAAC;AACD,mBAAO;AAAA,UACT;AAEA,cAAI,SAAS,QAAQ,GAAG,GAAG;AACzB,oBAAQ,MAAM,QAAQ,OAAO,CAAC;AAC9B,oBAAQ,IAAI,QAAQ,QAAQ,IAAI,SAAS;AACzC,oBAAQ,IAAI,MAAM,QAAQ,IAAI,OAAO,EAAE;AACvC,oBAAQ,IAAI,OAAO,QAAQ,IAAI,QAAQ;AACvC,iBAAKJ,OAAM;AAAA,cAAO,QAAQ,IAAI;AAAA,cACRH,MAAK,IAAI,EAAE,CAAC,EAAE,QAAQ,QAAQ,IAAI,GAAG;AAAA,cACrC,QAAQ,IAAI;AAAA,YAAI;AACtC,gBAAI,IAAI,QAAQ;AAChB,eAAG,QAAQ,SAASQ,IAAG,GAAG;AACxB,gBAAEA,EAAC,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,YACf,CAAC;AACD,mBAAO;AAAA,UACT;AAEA,cAAI,EAAE,CAAC,EAAE,WAAWP,YAAW;AAC7B,gBAAI,CAAC,CAAC;AAAA,UACR;AACA,kBAAQ,IAAI,QAAQ,QAAQ,IAAI,SAAS;AACzC,kBAAQ,IAAI,MAAM,QAAQ,IAAI,OAAO,EAAE;AACvC,kBAAQ,IAAI,OAAO,QAAQ,IAAI,QAAQ;AACvC,kBAAQ,IAAI,QAAQ,QAAQ,IAAI,SAAS;AACzC,kBAAQ,IAAI,MAAM,QAAQ,IAAI,OAAO,EAAE,CAAC,EAAE;AAC1C,kBAAQ,IAAI,OAAO,QAAQ,IAAI,QAAQ;AACvC,eAAKE,OAAM;AAAA,YAAO,QAAQ,IAAI;AAAA,YACRH,MAAK,IAAI,EAAE,QAAQ,QAAQ,IAAI,GAAG;AAAA,YAClC,QAAQ,IAAI;AAAA,UAAI;AACtC,eAAKG,OAAM;AAAA,YAAO,QAAQ,IAAI;AAAA,YACRH,MAAK,IAAI,EAAE,CAAC,EAAE,QAAQ,QAAQ,IAAI,GAAG;AAAA,YACrC,QAAQ,IAAI;AAAA,UAAI;AACtC,aAAG,QAAQ,SAASQ,IAAG,GAAG;AACxB,eAAG,QAAQ,SAASD,IAAG,GAAG;AACxB,gBAAEC,EAAC,EAAED,EAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,YAClB,CAAC;AAAA,UACH,CAAC;AACD,iBAAO;AAAA,QACT;AAKA,QAAAJ,OAAM,WAAW,SAAS,SAAS,WAAW;AAC5C,cAAI,MAAMA,OAAM,MAAM,UAAU,QAAQ,UAAU,MAAM;AACxD,oBAAU,QAAQ,SAAS,GAAG,GAAG;AAC/B,gBAAI,CAAC,EAAE,CAAC,IAAI;AAAA,UACd,CAAC;AACD,iBAAO;AAAA,QACT;AAIA,QAAAA,OAAM,OAAO,SAAS,KAAK,GAAG;AAC5B,iBAAO,EAAE,IAAI,SAAS,KAAK;AACzB,gBAAI,SAAS,GAAG;AACd,qBAAO;AACT,mBAAO,IAAI,IAAI,SAAS,GAAG;AACzB,qBAAO;AAAA,YACT,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAQA,YAAI,SAASA,OAAM;AAGnB,eAAO,SAAS;AAKhB,eAAO,OAAO,MAAM,UAAU;AAC9B,eAAO,OAAO,MAAM,UAAU;AAC9B,eAAO,SAAS,MAAM,UAAU;AAChC,eAAO,QAAQ,MAAM,UAAU;AAI/B,eAAO,UAAU,SAAS,UAAU;AAClC,iBAAO,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;AAAA,QAChE;AAIA,eAAO,MAAM,SAAS,IAAI,MAAM,SAAS;AACvC,iBAAOA,OAAMA,OAAM,IAAI,MAAM,MAAM,OAAO,CAAC;AAAA,QAC7C;AAIA,eAAO,YAAY,SAAS,UAAU,MAAM,SAAS;AACnD,iBAAOA,OAAMA,OAAM,UAAU,MAAM,MAAM,OAAO,CAAC;AAAA,QACnD;AAIA,eAAO,QAAQ,SAAS,MAAM,MAAM;AAClC,UAAAA,OAAM,MAAM,MAAM,IAAI;AACtB,iBAAO;AAAA,QACT;AAIA,SAAC,SAAS,OAAO;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,aAAC,SAAS,UAAU;AACzD,qBAAO,QAAQ,IAAI,SAAS,MAAM;AAChC,oBAAI,OAAO,MACX;AAEA,oBAAI,MAAM;AACR,6BAAW,WAAW;AACpB,yBAAK,KAAK,MAAM,OAAO,QAAQ,EAAE,KAAK,IAAI,CAAC;AAAA,kBAC7C,CAAC;AACD,yBAAO;AAAA,gBACT;AACA,0BAAUA,OAAM,QAAQ,EAAE,IAAI;AAC9B,uBAAO,QAAQ,OAAO,IAAIA,OAAM,OAAO,IAAI;AAAA,cAC7C;AAAA,YACF,GAAG,MAAM,CAAC,CAAC;AAAA,QACb,GAAG,+DAA+D,MAAM,GAAG,CAAC;AAI5E,SAAC,SAAS,OAAO;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,aAAC,SAAS,UAAU;AACzD,qBAAO,QAAQ,IAAI,SAAS,OAAO,MAAM;AACvC,oBAAI,OAAO;AAEX,oBAAI,MAAM;AACR,6BAAW,WAAW;AACpB,yBAAK,KAAK,MAAM,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,CAAC;AAAA,kBACpD,CAAC;AACD,yBAAO;AAAA,gBACT;AACA,uBAAOA,OAAMA,OAAM,QAAQ,EAAE,MAAM,KAAK,CAAC;AAAA,cAC3C;AAAA,YACF,GAAG,MAAM,CAAC,CAAC;AAAA,QACb,GAAG,UAAU,MAAM,GAAG,CAAC;AAIvB,SAAC,SAAS,OAAO;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,aAAC,SAAS,UAAU;AACzD,qBAAO,QAAQ,IAAI,WAAW;AAC5B,uBAAOA,OAAMA,OAAM,QAAQ,EAAE,MAAM,MAAM,SAAS,CAAC;AAAA,cACrD;AAAA,YACF,GAAG,MAAM,CAAC,CAAC;AAAA,QACb,GAAG,kCAAkC,MAAM,GAAG,CAAC;AAI/C,eAAOA;AAAA,MAEP,EAAE,IAAI;AACN,OAAC,SAASA,QAAOH,OAAM;AAEvB,YAAI,aAAaG,OAAM,MAAM;AAG7B,iBAAS,OAAO,GAAG,GAAG;AAAE,iBAAO,IAAI;AAAA,QAAG;AAEtC,iBAAS,KAAK,KAAK,KAAK,KAAK;AAC3B,iBAAOH,MAAK,IAAI,KAAKA,MAAK,IAAI,KAAK,GAAG,CAAC;AAAA,QACzC;AAIA,QAAAG,OAAM,MAAM,SAAS,IAAI,KAAK;AAC5B,cAAIM,OAAM;AACV,cAAI,IAAI,IAAI;AACZ,iBAAO,EAAE,KAAK;AACZ,YAAAA,QAAO,IAAI,CAAC;AACd,iBAAOA;AAAA,QACT;AAIA,QAAAN,OAAM,UAAU,SAAS,QAAQ,KAAK;AACpC,cAAI,MAAM;AACV,cAAI,IAAI,IAAI;AACZ,iBAAO,EAAE,KAAK;AACZ,mBAAO,IAAI,CAAC,IAAI,IAAI,CAAC;AACvB,iBAAO;AAAA,QACT;AAIA,QAAAA,OAAM,WAAW,SAAS,SAAS,KAAK;AACtC,cAAI,OAAOA,OAAM,KAAK,GAAG;AACzB,cAAI,MAAM;AACV,cAAI,IAAI,IAAI;AACZ,cAAI;AACJ,iBAAO,EAAE,KAAK,GAAG;AACf,kBAAM,IAAI,CAAC,IAAI;AACf,mBAAO,MAAM;AAAA,UACf;AACA,iBAAO;AAAA,QACT;AAGA,QAAAA,OAAM,SAAS,SAAS,OAAO,KAAK;AAClC,cAAI,MAAM;AACV,cAAI,IAAI,IAAI;AACZ,iBAAO,EAAE,KAAK;AACZ,mBAAO,IAAI,CAAC;AACd,iBAAO;AAAA,QACT;AAGA,QAAAA,OAAM,UAAU,SAAS,QAAQ,KAAK;AACpC,cAAI,OAAO;AACX,cAAI,IAAI,IAAI;AACZ,iBAAO,EAAE,KAAK;AACZ,oBAAQ,IAAI,CAAC;AACf,iBAAO;AAAA,QACT;AAIA,QAAAA,OAAM,MAAM,SAAS,IAAI,KAAK;AAC5B,cAAI,MAAM,IAAI,CAAC;AACf,cAAI,IAAI;AACR,iBAAO,EAAE,IAAI,IAAI;AACf,gBAAI,IAAI,CAAC,IAAI;AACX,oBAAM,IAAI,CAAC;AACf,iBAAO;AAAA,QACT;AAIA,QAAAA,OAAM,MAAM,SAAS,IAAI,KAAK;AAC5B,cAAI,OAAO,IAAI,CAAC;AAChB,cAAI,IAAI;AACR,iBAAO,EAAE,IAAI,IAAI;AACf,gBAAI,IAAI,CAAC,IAAI;AACX,qBAAO,IAAI,CAAC;AAChB,iBAAO;AAAA,QACT;AAIA,QAAAA,OAAM,SAAS,SAAS,OAAO,KAAK;AAClC,cAAI,OAAO,CAAC,GAAG,OAAO,CAAC;AACvB,mBAAQ,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAClC,gBAAI,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG;AACjB,mBAAK,IAAI,CAAC,CAAC,IAAI;AACf,mBAAK,KAAK,IAAI,CAAC,CAAC;AAAA,YAClB;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAIA,QAAAA,OAAM,OAAO,SAAS,KAAK,KAAK;AAC9B,iBAAOA,OAAM,IAAI,GAAG,IAAI,IAAI;AAAA,QAC9B;AAIA,QAAAA,OAAM,YAAY,SAAS,UAAU,KAAK;AACxC,iBAAOA,OAAM,SAAS,GAAG,IAAI,IAAI;AAAA,QACnC;AAIA,QAAAA,OAAM,UAAU,SAAS,QAAQ,KAAK;AACpC,cAAI,OAAO,IAAI,IAAIH,MAAK,GAAG;AAC3B,cAAI,aAAaG,OAAM,KAAK,IAAI;AAChC,iBAAOH,MAAK,IAAI,UAAU;AAAA,QAC5B;AAIA,QAAAG,OAAM,SAAS,SAAS,OAAO,KAAK;AAClC,cAAI,SAAS,IAAI;AACjB,cAAI,OAAO,IAAI,MAAM,EAAE,KAAK,MAAM;AAElC,iBAAO,EAAE,SAAS,MACb,KAAM,SAAS,IAAK,CAAE,IAAI,KAAM,SAAS,CAAE,KAAK,IACjD,KAAM,SAAS,IAAK,CAAE;AAAA,QAC5B;AAIA,QAAAA,OAAM,SAAS,SAAS,OAAO,KAAK;AAClC,iBAAOA,OAAM,UAAU,KAAK,SAAU,GAAG,GAAG;AAAE,mBAAO,IAAI;AAAA,UAAG,CAAC;AAAA,QAC/D;AAIA,QAAAA,OAAM,UAAU,SAAS,QAAQ,KAAK;AACpC,iBAAOA,OAAM,UAAU,KAAK,SAAU,GAAG,GAAG;AAAE,mBAAO,IAAI;AAAA,UAAG,CAAC;AAAA,QAC/D;AAIA,QAAAA,OAAM,OAAO,SAAS,KAAK,KAAK;AAC9B,cAAI,QAAQ,CAAC;AACb,cAAI,SAAS,IAAI;AACjB,cAAI;AACJ,eAAK,IAAI,GAAG,IAAI,QAAQ;AACtB,kBAAM,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;AAChC,iBAAO;AAAA,QACT;AAIA,QAAAA,OAAM,OAAO,SAAU,KAAK;AAC1B,cAAI;AACJ,cAAI,kBAAkB,CAAC;AACvB,cAAI,eAAe,CAAC;AACpB,eAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC/B,gBAAI,SAAS,IAAI,CAAC;AAClB,gBAAI,aAAa,MAAM,GAAG;AACxB,2BAAa,MAAM;AAAA,YACrB,OAAO;AACL,2BAAa,MAAM,IAAI;AACvB,8BAAgB,KAAK,MAAM;AAAA,YAC7B;AAAA,UACF;AAEA,cAAI,wBAAwB,gBAAgB,KAAK,MAAM;AACvD,cAAI,cAAc,CAAC;AACnB,cAAI,cAAc;AAClB,eAAK,IAAI,GAAG,IAAI,sBAAsB,QAAQ,KAAK;AACjD,gBAAI,SAAS,sBAAsB,CAAC;AACpC,gBAAI,QAAQ,aAAa,MAAM;AAC/B,gBAAI,QAAQ;AACZ,gBAAI,OAAO,cAAc,QAAQ;AACjC,gBAAI,QAAQ,QAAQ,QAAQ;AAC5B,wBAAY,MAAM,IAAI;AACtB,2BAAe;AAAA,UACjB;AAEA,iBAAO,IAAI,IAAI,SAAUO,SAAQ;AAC/B,mBAAO,YAAYA,OAAM;AAAA,UAC3B,CAAC;AAAA,QACH;AAMA,QAAAP,OAAM,OAAO,SAAS,KAAK,KAAK;AAC9B,cAAI,SAAS,IAAI;AACjB,cAAI,OAAO,IAAI,MAAM,EAAE,KAAK,MAAM;AAClC,cAAI,QAAQ;AACZ,cAAI,WAAW;AACf,cAAI,cAAc;AAClB,cAAI,WAAW,CAAC;AAChB,cAAI;AAEJ,eAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,gBAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,GAAG;AAC3B;AAAA,YACF,OAAO;AACL,kBAAI,QAAQ,UAAU;AACpB,2BAAW,CAAC,KAAK,CAAC,CAAC;AACnB,2BAAW;AACX,8BAAc;AAAA,cAChB,WAES,UAAU,UAAU;AAC3B,yBAAS,KAAK,KAAK,CAAC,CAAC;AACrB;AAAA,cACF;AAEA,sBAAQ;AAAA,YACV;AAAA,UACF;AAEA,iBAAO,gBAAgB,IAAI,SAAS,CAAC,IAAI;AAAA,QAC3C;AAIA,QAAAA,OAAM,QAAQ,SAAS,MAAM,KAAK;AAChC,iBAAOA,OAAM,IAAI,GAAG,IAAIA,OAAM,IAAI,GAAG;AAAA,QACvC;AAIA,QAAAA,OAAM,WAAW,SAAS,SAAS,KAAK,MAAM;AAC5C,iBAAOA,OAAM,SAAS,GAAG,KAAK,IAAI,UAAU,OAAO,IAAI;AAAA,QACzD;AAGA,QAAAA,OAAM,iBAAiB,SAAS,eAAe,KAAK;AAClD,cAAI,WAAW,IAAI,OAAO,SAAU,GAAG,SAAS;AAAC,mBAAO,IAAIA,OAAM,SAAS,OAAO;AAAA,UAAE,GAAG,CAAC;AACxF,cAAI,QAAQ,IAAI,OAAO,SAAU,GAAG,SAAS;AAAC,mBAAO,IAAI,QAAQ;AAAA,UAAO,GAAG,CAAC;AAC5E,iBAAO,YAAY,QAAQ,IAAI;AAAA,QACjC;AAGA,QAAAA,OAAM,YAAY,SAAU,KAAK;AAC/B,cAAI,OAAOA,OAAM,KAAK,GAAG;AACzB,cAAI,SAAS,IAAI;AACjB,cAAI,MAAM,IAAI,MAAM,MAAM;AAC1B,mBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAI,CAAC,IAAI,IAAI,CAAC,IAAI;AAAA,UACpB;AACA,iBAAO;AAAA,QACT;AAIA,QAAAA,OAAM,QAAQ,SAAS,MAAM,KAAK,MAAM;AACtC,iBAAOH,MAAK,KAAKG,OAAM,SAAS,KAAK,IAAI,CAAC;AAAA,QAC5C;AAGA,QAAAA,OAAM,cAAc,SAAS,YAAY,KAAK;AAC5C,iBAAOH,MAAK,KAAKG,OAAM,eAAe,GAAG,CAAC;AAAA,QAC5C;AAGA,QAAAA,OAAM,UAAU,SAAS,QAAQ,KAAK;AACpC,cAAI,OAAOA,OAAM,KAAK,GAAG;AACzB,cAAI,IAAI,CAAC;AACT,mBAAS,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,cAAE,KAAKH,MAAK,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,UAChC;AACA,iBAAOG,OAAM,KAAK,CAAC;AAAA,QACrB;AAIA,QAAAA,OAAM,SAAS,SAAS,OAAO,KAAK;AAClC,cAAI,SAASA,OAAM,OAAO,GAAG;AAC7B,cAAI,IAAI,CAAC;AACT,mBAAS,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,cAAE,KAAKH,MAAK,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC;AAAA,UAClC;AACA,iBAAOG,OAAM,OAAO,CAAC;AAAA,QACvB;AAIA,QAAAA,OAAM,WAAW,SAAS,SAAS,KAAK;AACtC,iBAAOA,OAAM,MAAM,GAAG,IAAIA,OAAM,KAAK,GAAG;AAAA,QAC1C;AAIA,QAAAA,OAAM,YAAY,SAAS,UAAU,KAAK;AACxC,cAAI,SAAS,IAAI;AACjB,cAAI,OAAO,IAAI,MAAM,EAAE,KAAK,MAAM;AAClC,iBAAO;AAAA,YACL,KAAMH,MAAK,MAAO,SAAU,CAAC,IAAI,CAAE;AAAA,YACnC,KAAMA,MAAK,MAAO,SAAU,CAAC,IAAI,CAAE;AAAA,YACnC,KAAMA,MAAK,MAAO,SAAU,IAAI,CAAC,IAAI,CAAE;AAAA,UACzC;AAAA,QACF;AAKA,QAAAG,OAAM,YAAY,SAAS,UAAU,KAAK,gBAAgB,QAAQ,OAAO;AACvE,cAAI,cAAc,IAAI,MAAM,EAAE,KAAK,MAAM;AACzC,cAAI,eAAe,CAAC,eAAe,MAAM;AACzC,cAAI,IAAI,IAAI;AACZ,cAAI,GAAG,GAAG,GAAG,OAAO,GAAG;AAEvB,cAAI,OAAO,WAAW;AACpB,qBAAS,IAAI;AACf,cAAI,OAAO,UAAU;AACnB,oBAAQ,IAAI;AAEd,eAAK,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC1C,gBAAI,eAAe,CAAC;AACpB,gBAAI,SAAS,KAAK,IAAI,SAAS;AAC/B,oBAAQ,IAAI,IAAI;AAChB,gBAAIH,MAAK,MAAM,KAAK,OAAO,GAAG,IAAI,CAAC,CAAC;AACpC,oBAAQ,KAAK,QAAQ,GAAG,GAAG,CAAC;AAC5B,yBAAa,CAAC,KAAK,IAAI,SAAS,YAAY,IAAI,CAAC,IAAI,QAAQ,YAAY,CAAC;AAAA,UAC5E;AAEA,iBAAO;AAAA,QACT;AAIA,QAAAG,OAAM,aAAa,SAAS,WAAW,KAAK,GAAG,WAAW;AACxD,cAAI,OAAO,IAAI,MAAM,EAAE,KAAK,MAAM;AAClC,cAAI,YAAY,KAAK,KAAK,UAAU,YAAY,IAAI,QAAQ,YAAY,IAAI;AAC5E,cAAI,QAAQ,SAAS,SAAS;AAC9B,cAAI,OAAO,YAAY;AACvB,cAAI,QAAQ,IAAI,KAAK,QAAQ;AAC3B,mBAAO,KAAK,QAAQ,CAAC,IAAI,QAAQ,KAAK,KAAK,IAAI,KAAK,QAAQ,CAAC;AAAA,UAC/D,OAAO;AACL,mBAAO,KAAK,QAAQ,CAAC;AAAA,UACvB;AAAA,QACF;AAKA,QAAAA,OAAM,oBAAoB,SAAS,kBAAkB,KAAK,OAAO,MAAM;AACrE,cAAI,UAAU;AACd,cAAI,MAAM,IAAI;AACd,cAAI,SAAS;AACb,cAAI,OAAO;AAEX,cAAI,SAAS;AACX,qBAAS;AAEX,eAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,oBAAQ,IAAI,CAAC;AACb,gBAAK,UAAU,QAAQ,SAClB,CAAC,UAAU,SAAS,OAAQ;AAC/B;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,UAAU;AAAA,QACnB;AAIA,QAAAA,OAAM,YAAY,SAAS,UAAU,KAAK,QAAQ;AAChD,mBAAS,UAAU;AACnB,cAAI,QAAQA,OAAM,IAAI,GAAG;AACzB,cAAI,YAAYA,OAAM,IAAI,GAAG,IAAI,SAAS;AAC1C,cAAI,MAAM,IAAI;AACd,cAAI,OAAO,CAAC;AACZ,cAAI;AAEJ,eAAK,IAAI,GAAG,IAAI,QAAQ;AACtB,iBAAK,CAAC,IAAI;AACZ,eAAK,IAAI,GAAG,IAAI,KAAK;AACnB,iBAAKH,MAAK,IAAIA,MAAK,OAAQ,IAAI,CAAC,IAAI,SAAS,QAAS,GAAG,SAAS,CAAC,CAAC,KAAK;AAE3E,iBAAO;AAAA,QACT;AAIA,QAAAG,OAAM,aAAa,SAAS,WAAW,MAAM,MAAM;AACjD,cAAI,IAAIA,OAAM,KAAK,IAAI;AACvB,cAAI,IAAIA,OAAM,KAAK,IAAI;AACvB,cAAI,UAAU,KAAK;AACnB,cAAI,SAAS,IAAI,MAAM,OAAO;AAC9B,cAAI;AAEJ,eAAK,IAAI,GAAG,IAAI,SAAS;AACvB,mBAAO,CAAC,KAAK,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,IAAI;AAEzC,iBAAOA,OAAM,IAAI,MAAM,KAAK,UAAU;AAAA,QACxC;AAIA,QAAAA,OAAM,YAAY,SAAS,UAAU,MAAM,MAAM;AAC/C,iBAAOA,OAAM,WAAW,MAAM,IAAI,IAC9BA,OAAM,MAAM,MAAM,CAAC,IACnBA,OAAM,MAAM,MAAM,CAAC;AAAA,QACzB;AAGA,QAAAA,OAAM,gBAAiB,SAAU,MAAM,MAAM;AAC3C,iBAAOA,OAAM,KAAK,IAAI;AACtB,iBAAOA,OAAM,KAAK,IAAI;AAEtB,iBAAOA,OAAM,UAAU,MAAM,IAAI;AAAA,QACnC;AAIA,QAAAA,OAAM,aAAa,SAAS,WAAW,KAAK,GAAG;AAC7C,cAAI,KAAKA,OAAM,KAAK,GAAG;AACvB,cAAI,QAAQA,OAAM,MAAM,GAAG;AAC3B,cAAI,MAAM,IAAI;AACd,cAAI,UAAU;AAEd,mBAAS,IAAI,GAAG,IAAI,KAAK;AACvB,uBAAWH,MAAK,KAAK,IAAI,CAAC,IAAI,MAAM,OAAO,CAAC;AAE9C,iBAAO,UAAU,IAAI;AAAA,QACvB;AAGA,QAAAG,OAAM,WAAW,SAAS,SAAS,KAAK;AACtC,iBAAOA,OAAM,WAAW,KAAK,CAAC;AAAA,QAChC;AAGA,QAAAA,OAAM,WAAW,SAAS,SAAS,KAAK;AACtC,iBAAOA,OAAM,WAAW,KAAK,CAAC,IAAI;AAAA,QACpC;AAGA,YAAI,SAASA,OAAM;AAQnB,SAAC,SAAS,OAAO;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,aAAC,SAAS,UAAU;AAGzD,qBAAO,QAAQ,IAAI,SAAS,UAAU,MAAM;AAC1C,oBAAI,MAAM,CAAC;AACX,oBAAIC,KAAI;AACR,oBAAI,UAAU;AAEd,oBAAI,WAAW,QAAQ,GAAG;AACxB,yBAAO;AACP,6BAAW;AAAA,gBACb;AAEA,oBAAI,MAAM;AACR,6BAAW,WAAW;AACpB,yBAAK,KAAK,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAS,QAAQ,CAAC;AAAA,kBAC7D,CAAC;AACD,yBAAO;AAAA,gBACT;AAEA,oBAAI,KAAK,SAAS,GAAG;AACnB,4BAAU,aAAa,OAAO,OAAO,KAAK,UAAU;AACpD,yBAAOA,KAAI,QAAQ,QAAQA;AACzB,wBAAIA,EAAC,IAAID,OAAM,QAAQ,EAAE,QAAQC,EAAC,CAAC;AACrC,yBAAO;AAAA,gBACT;AAEA,uBAAOD,OAAM,QAAQ,EAAE,KAAK,CAAC,GAAG,QAAQ;AAAA,cAC1C;AAAA,YACF,GAAG,MAAM,CAAC,CAAC;AAAA,QACb,GAAI,iBAAkB,MAAM,GAAG,CAAC;AAIhC,SAAC,SAAS,OAAO;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,aAAC,SAAS,UAAU;AAGzD,qBAAO,QAAQ,IAAI,SAAS,UAAU,MAAM;AAC1C,oBAAI,MAAM,CAAC;AACX,oBAAIC,KAAI;AACR,oBAAI,UAAU;AAEd,oBAAI,WAAW,QAAQ,GAAG;AACxB,yBAAO;AACP,6BAAW;AAAA,gBACb;AAEA,oBAAI,MAAM;AACR,6BAAW,WAAW;AACpB,yBAAK,KAAK,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAS,QAAQ,CAAC;AAAA,kBAC7D,CAAC;AACD,yBAAO;AAAA,gBACT;AAEA,oBAAI,KAAK,SAAS,GAAG;AACnB,sBAAI,aAAa;AACf,8BAAU,aAAa,OAAO,OAAO,KAAK,UAAU;AACtD,yBAAOA,KAAI,QAAQ,QAAQA;AACzB,wBAAIA,EAAC,IAAID,OAAM,QAAQ,EAAE,QAAQC,EAAC,CAAC;AACrC,yBAAO,aAAa,OACdD,OAAM,QAAQ,EAAEA,OAAM,MAAM,SAAS,GAAG,CAAC,IACzC;AAAA,gBACR;AAEA,uBAAOA,OAAM,QAAQ,EAAE,KAAK,CAAC,GAAG,QAAQ;AAAA,cAC1C;AAAA,YACF,GAAG,MAAM,CAAC,CAAC;AAAA,QACb,GAAI,+LAEyD,MAAM,GAAG,CAAC;AAKvE,SAAC,SAAS,OAAO;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,aAAC,SAAS,UAAU;AACzD,qBAAO,QAAQ,IAAI,WAAW;AAC5B,oBAAI,MAAM,CAAC;AACX,oBAAIC,KAAI;AACR,oBAAI,UAAU;AACd,oBAAI,OAAO,MAAM,UAAU,MAAM,KAAK,SAAS;AAC/C,oBAAI;AAIJ,oBAAI,WAAW,KAAK,KAAK,SAAS,CAAC,CAAC,GAAG;AACrC,qCAAmB,KAAK,KAAK,SAAS,CAAC;AACvC,sBAAI,aAAa,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC;AAE9C,6BAAW,WAAW;AACpB,qCAAiB;AAAA,sBAAK;AAAA,sBACA,OAAO,QAAQ,EAAE,MAAM,SAAS,UAAU;AAAA,oBAAC;AAAA,kBACnE,CAAC;AACD,yBAAO;AAAA,gBAGT,OAAO;AACL,qCAAmB;AACnB,sBAAI,kBAAkB,SAASO,iBAAgB,QAAQ;AACrD,2BAAOR,OAAM,QAAQ,EAAE,MAAM,SAAS,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC;AAAA,kBAC7D;AAAA,gBACF;AAGA,oBAAI,KAAK,SAAS,GAAG;AACnB,4BAAU,QAAQ,UAAU;AAC5B,yBAAOC,KAAI,QAAQ,QAAQA;AACzB,wBAAIA,EAAC,IAAI,gBAAgB,QAAQA,EAAC,CAAC;AACrC,yBAAO;AAAA,gBACT;AAGA,uBAAO,gBAAgB,KAAK,CAAC,CAAC;AAAA,cAChC;AAAA,YACF,GAAG,MAAM,CAAC,CAAC;AAAA,QACb,GAAG,8BAA8B,MAAM,GAAG,CAAC;AAAA,MAE3C,GAAE,OAAO,IAAI;AAEb,OAAC,SAASD,QAAOH,OAAM;AAGvB,QAAAG,OAAM,UAAU,SAAS,QAAQ,GAAG;AAClC,cAAI,IAAI;AACR,cAAI,MAAM;AAAA,YACR;AAAA,YAAmB;AAAA,YAAoB;AAAA,YACvC;AAAA,YAAoB;AAAA,YAAuB;AAAA,UAC7C;AACA,cAAI,MAAM;AACV,cAAI,IAAI,GAAG;AACX,iBAAO,IAAI,KAAK,KAAK;AACrB,kBAAQ,KAAK,OAAOH,MAAK,IAAI,GAAG;AAChC,iBAAO,IAAI,GAAG;AACZ,mBAAO,IAAI,CAAC,IAAI,EAAE;AACpB,iBAAOA,MAAK,IAAI,qBAAqB,MAAM,EAAE,IAAI;AAAA,QACnD;AAOA,QAAAG,OAAM,SAAS,SAAS,OAAO,GAAG;AAChC,cAAI,IAAI,IAAI,IAAI,IAAI;AACpB,cAAI,GAAG;AAEP,cAAI,IAAI;AAAA,YAAC;AAAA,YAAuB;AAAA,YACxB;AAAA,YAAuB;AAAA,YACvB;AAAA,YAAuB;AAAA,YACvB;AAAA,YAAuB;AAAA,YACvB;AAAA,YAAuB;AAAA,UAAqB;AACpD,eAAK;AACL,cAAI;AACJ,cAAK,KAAK,KAAS,KAAK,GAAM;AAC1B,mBAAO;AAAA,UACX;AACA,cAAI,KAAK,GAAK;AACV,gBAAIH,MAAK,MAAM,IAAI,CAAC;AACpB,iBAAK,IAAI;AAAA,UACb;AACA,eAAK,KAAO,KAAK;AACjB,eAAK,IAAIA,MAAK;AACd,gBAAM,EAAE,CAAC;AACT,eAAK,IAAI,GAAG,KAAK,GAAG,KAAK;AACrB,mBAAO;AACP,mBAAO,EAAE,CAAC;AAAA,UACd;AACA,eAAK,MAAM,KAAK,MAAMA,MAAK,IAAI,EAAE,KAAK,KAAK,OAAOA,MAAK,IAAI,EAAE,IAAI;AACjE,cAAI,KAAK,GAAK;AACV,iBAAK,IAAI,GAAG,KAAK,GAAG,KAAK;AACrB,oBAAMA,MAAK,IAAI,KAAK,CAAG;AACvB,oBAAM;AAAA,YACV;AAAA,UACJ;AACA,iBAAO;AAAA,QACT;AAGA,QAAAG,OAAM,UAAU,SAAS,QAAQ,GAAG;AAClC,cAAI,IAAI;AAAA,YAAC;AAAA,YAAoB;AAAA,YAAmB;AAAA,YACvC;AAAA,YAAmB;AAAA,YAAmB;AAAA,YACtC;AAAA,YAAqB;AAAA,UAC9B;AACA,cAAI,IAAI;AAAA,YAAC;AAAA,YAAmB;AAAA,YAAoB;AAAA,YACvC;AAAA,YAAoB;AAAA,YAAoB;AAAA,YACxC;AAAA,YAAoB;AAAA,UAAkB;AAC/C,cAAI,OAAO;AACX,cAAI,IAAI;AACR,cAAI,OAAO;AACX,cAAI,OAAO;AACX,cAAI,IAAI;AACR,cAAI,GAAG,GAAG,IAAI;AACd,cAAI,IAAI,mBAAmB;AACzB,mBAAO;AAAA,UACT;AACA,cAAI,KAAK,GAAG;AACV,kBAAM,IAAI,IAAI;AACd,gBAAI,KAAK;AACP,sBAAQ,EAAE,IAAI,KAAK,IAAI,MAAMH,MAAK,KAAKA,MAAK,IAAIA,MAAK,KAAK,GAAG;AAC7D,kBAAI,IAAI;AAAA,YACV,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AACA,eAAK;AACL,cAAI,IAAI,GAAG;AACT,gBAAI;AAAA,UACN,OAAO;AACL,iBAAK,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,UAC/B;AACA,eAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,oBAAQ,OAAO,EAAE,CAAC,KAAK;AACvB,mBAAO,OAAO,IAAI,EAAE,CAAC;AAAA,UACvB;AACA,gBAAM,OAAO,OAAO;AACpB,cAAI,KAAK,GAAG;AACV,mBAAO;AAAA,UACT,WAAW,KAAK,GAAG;AACjB,iBAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,qBAAO;AACP;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM;AACR,kBAAM,OAAO;AAAA,UACf;AACA,iBAAO;AAAA,QACT;AAKA,QAAAG,OAAM,SAAS,SAAS,OAAO,GAAG,GAAG;AACnC,iBAAOA,OAAM,YAAY,GAAG,CAAC,IAAIA,OAAM,QAAQ,CAAC;AAAA,QAClD;AAIA,QAAAA,OAAM,cAAc,SAAS,YAAY,GAAG,GAAG;AAC7C,cAAI,MAAMA,OAAM,QAAQ,CAAC;AACzB,cAAI,KAAK;AACT,cAAI,MAAM,IAAI;AACd,cAAI,MAAM;AACV,cAAI,IAAI,IAAI,IAAI;AAChB,cAAI,IAAI,IAAI;AACZ,cAAI,IAAI,IAAI;AACZ,cAAI,IAAI;AACR,cAAI,IAAI;AAER,cAAI,QAAQ,CAAC,EAAEH,MAAK,IAAK,KAAK,IAAK,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AAChE,cAAI;AAEJ,cAAI,IAAI,KAAK,KAAK,GAAG;AACnB,mBAAO;AAAA,UACT,WAAW,IAAI,IAAI,GAAG;AACpB,mBAAO,KAAK,OAAO,KAAK;AACtB,qBAAO,OAAO,IAAI,EAAE;AAAA,YACtB;AACA,mBAAQ,MAAMA,MAAK,IAAI,CAAC,IAAI,IAAIA,MAAK,IAAI,CAAC,IAAK,GAAI;AAAA,UACrD;AAEA,iBAAO,KAAK,OAAO,KAAK;AACtB,iBAAK,CAAC,KAAK,IAAI;AACf,iBAAK;AACL,gBAAI,KAAK,IAAI;AACb,gBAAI,IAAI,KAAK;AACb,gBAAI,IAAI;AACR,iBAAK,IAAI;AAAA,UACX;AAEA,iBAAQ,IAAI,IAAIA,MAAK,IAAI,CAAC,IAAI,IAAIA,MAAK,IAAI,CAAC,IAAK,GAAI;AAAA,QACvD;AAGA,QAAAG,OAAM,cAAc,SAAS,YAAY,GAAG;AAC1C,iBAAO,IAAI,IAAI,MAAMA,OAAM,QAAQ,IAAI,CAAC;AAAA,QAC1C;AAGA,QAAAA,OAAM,YAAY,SAAS,UAAU,GAAG;AACtC,iBAAO,IAAI,IAAI,MAAMA,OAAM,QAAQ,IAAI,CAAC;AAAA,QAC1C;AAGA,QAAAA,OAAM,cAAc,SAAS,YAAY,GAAG,GAAG;AAE7C,iBAAQ,IAAI,OAAO,IAAI,MACjBH,MAAK,IAAIG,OAAM,cAAc,GAAG,CAAC,CAAC,IACjCA,OAAM,UAAU,CAAC,IAAIA,OAAM,UAAU,CAAC,IAAKA,OAAM,UAAU,IAAI,CAAC;AAAA,QACzE;AAGA,QAAAA,OAAM,gBAAgB,SAAS,cAAc,GAAG,GAAE;AAChD,iBAAOA,OAAM,YAAY,CAAC,IAAIA,OAAM,YAAY,CAAC,IAAIA,OAAM,YAAY,IAAI,CAAC;AAAA,QAC9E;AAIA,QAAAA,OAAM,cAAc,SAAS,YAAY,GAAG,GAAG;AAC7C,iBAAOA,OAAM,UAAU,CAAC,IAAIA,OAAM,UAAU,IAAI,CAAC;AAAA,QACnD;AAIA,QAAAA,OAAM,SAAS,SAAS,OAAO,GAAG,GAAG;AAEnC,cAAI,KAAK,KAAK,KAAK;AACjB,mBAAO;AAET,iBAAQ,IAAI,IAAI,MACVH,MAAK,IAAIG,OAAM,OAAO,GAAG,CAAC,CAAC,IAC3BA,OAAM,QAAQ,CAAC,IAAIA,OAAM,QAAQ,CAAC,IAAIA,OAAM,QAAQ,IAAI,CAAC;AAAA,QACjE;AAIA,QAAAA,OAAM,SAAS,SAAS,OAAO,GAAG,GAAG;AACnC,iBAAOA,OAAM,QAAQ,CAAC,IAAIA,OAAM,QAAQ,CAAC,IAAIA,OAAM,QAAQ,IAAI,CAAC;AAAA,QAClE;AAKA,QAAAA,OAAM,SAAS,SAAS,OAAO,GAAG,GAAG,GAAG;AACtC,cAAI,QAAQ;AACZ,cAAI,IAAI;AACR,cAAI,MAAM,IAAI;AACd,cAAI,MAAM,IAAI;AACd,cAAI,MAAM,IAAI;AACd,cAAI,IAAI;AACR,cAAI,IAAI,IAAI,MAAM,IAAI;AACtB,cAAI,IAAI,IAAI,KAAK;AAGjB,cAAIH,MAAK,IAAI,CAAC,IAAI;AAChB,gBAAI;AACN,cAAI,IAAI;AACR,cAAI;AAEJ,iBAAO,KAAK,KAAK,KAAK;AACpB,iBAAK,IAAI;AACT,iBAAK,KAAK,IAAI,KAAK,MAAM,MAAM,OAAO,IAAI;AAE1C,gBAAI,IAAI,KAAK;AACb,gBAAIA,MAAK,IAAI,CAAC,IAAI;AAChB,kBAAI;AACN,gBAAI,IAAI,KAAK;AACb,gBAAIA,MAAK,IAAI,CAAC,IAAI;AAChB,kBAAI;AACN,gBAAI,IAAI;AACR,iBAAK,IAAI;AACT,iBAAK,EAAE,IAAI,MAAM,MAAM,KAAK,MAAM,IAAI,OAAO,MAAM;AAEnD,gBAAI,IAAI,KAAK;AACb,gBAAIA,MAAK,IAAI,CAAC,IAAI;AAChB,kBAAI;AACN,gBAAI,IAAI,KAAK;AACb,gBAAIA,MAAK,IAAI,CAAC,IAAI;AAChB,kBAAI;AACN,gBAAI,IAAI;AACR,kBAAM,IAAI;AACV,iBAAK;AACL,gBAAIA,MAAK,IAAI,MAAM,CAAG,IAAI;AACxB;AAAA,UACJ;AAEA,iBAAO;AAAA,QACT;AAIA,QAAAG,OAAM,YAAY,SAAS,UAAU,GAAG,GAAG;AACzC,cAAI,IAAI;AACR,cAAI,KAAK,IAAI;AACb,cAAI,MAAM;AACV,cAAI,MAAMA,OAAM,QAAQ,CAAC;AACzB,cAAI,GAAG,KAAK,GAAG,GAAG,IAAI,MAAM;AAE5B,cAAI,KAAK;AACP,mBAAOH,MAAK,IAAI,KAAK,IAAI,MAAMA,MAAK,KAAK,CAAC,CAAC;AAC7C,cAAI,KAAK;AACP,mBAAO;AACT,cAAI,IAAI,GAAG;AACT,mBAAOA,MAAK,IAAI,EAAE;AAClB,mBAAOA,MAAK,IAAI,MAAM,OAAO,KAAK,GAAG;AACrC,iBAAM,IAAI,MAAO,IAAI,IAAI;AACzB,gBAAIA,MAAK,KAAK,KAAKA,MAAK,IAAI,EAAE,CAAC;AAC/B,iBAAK,UAAU,IAAI,YAAY,IAAI,KAAK,UAAU,IAAI,YAAY;AAClE,gBAAI,IAAI;AACN,kBAAI,CAAC;AACP,gBAAIA,MAAK;AAAA,cAAI;AAAA,cACA,IAAIA,MAAK,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,IAAIA,MAAK,KAAK,CAAC,IAAI,CAAC;AAAA,YAAC;AAAA,UACxE,OAAO;AACL,gBAAI,IAAI,KAAK,QAAQ,IAAI;AACzB,gBAAI,IAAI;AACN,kBAAIA,MAAK,IAAI,IAAI,GAAG,IAAI,CAAC;AAAA;AAEzB,kBAAI,IAAIA,MAAK,IAAI,KAAK,IAAI,MAAM,IAAI,EAAE;AAAA,UAC1C;AAEA,iBAAM,IAAI,IAAI,KAAK;AACjB,gBAAI,KAAK;AACP,qBAAO;AACT,kBAAMG,OAAM,YAAY,GAAG,CAAC,IAAI;AAChC,gBAAI,IAAI;AACN,kBAAI,OAAOH,MAAK,IAAI,EAAE,IAAI,MAAM,MAAMA,MAAK,IAAI,CAAC,IAAI,KAAK;AAAA;AAEzD,kBAAIA,MAAK,IAAI,CAAC,IAAI,KAAKA,MAAK,IAAI,CAAC,IAAI,GAAG;AAC1C,gBAAI,MAAM;AACV,iBAAM,IAAI,KAAK,IAAI,MAAMA,MAAK,IAAI,GAAG,MAAM,IAAI,KAAK,IAAI,EAAE;AAC1D,gBAAI,KAAK;AACP,kBAAI,OAAO,IAAI;AACjB,gBAAIA,MAAK,IAAI,CAAC,IAAI,MAAM;AACtB;AAAA,UACJ;AAEA,iBAAO;AAAA,QACT;AAIA,QAAAG,OAAM,MAAM,SAAS,IAAI,GAAG;AAC1B,cAAI,MAAM;AAAA,YAAC;AAAA,YAAqB;AAAA,YAAuB;AAAA,YAC5C;AAAA,YAAuB;AAAA,YAAsB;AAAA,YAC7C;AAAA,YAAoB;AAAA,YAAqB;AAAA,YACzC;AAAA,YAAmB;AAAA,YAAiB;AAAA,YACpC;AAAA,YAAgB;AAAA,YAAgB;AAAA,YAChC;AAAA,YAAiB;AAAA,YAAe;AAAA,YAChC;AAAA,YAAe;AAAA,YAAa;AAAA,YAC5B;AAAA,YAAc;AAAA,YAAU;AAAA,YACxB;AAAA,YAAY;AAAA,YAAU;AAAA,YACtB;AAAA,UAAQ;AACnB,cAAI,IAAI,IAAI,SAAS;AACrB,cAAI,QAAQ;AACZ,cAAI,IAAI;AACR,cAAI,KAAK;AACT,cAAI,GAAG,IAAI,KAAK;AAEhB,cAAI,IAAI,GAAG;AACT,gBAAI,CAAC;AACL,oBAAQ;AAAA,UACV;AAEA,cAAI,KAAK,IAAI;AACb,eAAK,IAAI,IAAI;AAEb,iBAAM,IAAI,GAAG,KAAK;AAChB,kBAAM;AACN,gBAAI,KAAK,IAAI,KAAK,IAAI,CAAC;AACvB,iBAAK;AAAA,UACP;AAEA,gBAAM,IAAIH,MAAK,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE;AACxD,iBAAO,QAAQ,MAAM,IAAI,IAAI;AAAA,QAC/B;AAIA,QAAAG,OAAM,OAAO,SAAS,KAAK,GAAG;AAC5B,iBAAO,IAAIA,OAAM,IAAI,CAAC;AAAA,QACxB;AAIA,QAAAA,OAAM,UAAU,SAAS,QAAQ,GAAG;AAClC,cAAI,IAAI;AACR,cAAI,GAAG,KAAK,GAAG;AACf,cAAI,KAAK;AACP,mBAAO;AACT,cAAI,KAAK;AACP,mBAAO;AACT,eAAM,IAAI,IAAK,IAAI,IAAI;AACvB,cAAIH,MAAK,KAAK,KAAKA,MAAK,IAAI,KAAK,CAAC,CAAC;AACnC,cAAI,aAAa,UAAU,IAAI,YACd,IAAI,KAAK,UAAU,IAAI,YAAY;AACpD,iBAAO,IAAI,GAAG,KAAK;AACjB,kBAAMG,OAAM,KAAK,CAAC,IAAI;AACtB,iBAAK,OAAO,qBAAsBH,MAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;AAAA,UAC3D;AACA,iBAAQ,IAAI,IAAK,IAAI,CAAC;AAAA,QACxB;AAIA,QAAAG,OAAM,WAAW,SAAS,SAAS,GAAG,GAAG,GAAG;AAC1C,cAAI,MAAM;AACV,cAAI,KAAK,IAAI;AACb,cAAI,KAAK,IAAI;AACb,cAAI,IAAI;AACR,cAAI,KAAK,KAAK,IAAI,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;AAC1C,cAAI,KAAK;AACP,mBAAO;AACT,cAAI,KAAK;AACP,mBAAO;AACT,cAAI,KAAK,KAAK,KAAK,GAAG;AACpB,iBAAM,IAAI,MAAO,IAAI,IAAI;AACzB,gBAAIH,MAAK,KAAK,KAAKA,MAAK,IAAI,EAAE,CAAC;AAC/B,iBAAK,UAAU,IAAI,YAAY,IAAI,KAAI,UAAU,IAAI,YAAY;AACjE,gBAAI,IAAI;AACN,kBAAI,CAAC;AACP,kBAAM,IAAI,IAAI,KAAK;AACnB,gBAAI,KAAK,KAAK,IAAI,IAAI,KAAM,KAAK,IAAI,IAAI;AACzC,gBAAK,IAAIA,MAAK,KAAK,KAAK,CAAC,IAAI,KAAM,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,OAC7D,KAAK,IAAI,IAAI,KAAK,IAAI;AAC3B,gBAAI,KAAK,IAAI,IAAIA,MAAK,IAAI,IAAI,CAAC;AAAA,UACjC,OAAO;AACL,kBAAMA,MAAK,IAAI,KAAK,IAAI,EAAE;AAC1B,kBAAMA,MAAK,IAAI,KAAK,IAAI,EAAE;AAC1B,gBAAIA,MAAK,IAAI,IAAI,GAAG,IAAI;AACxB,gBAAIA,MAAK,IAAI,IAAI,GAAG,IAAI;AACxB,gBAAI,IAAI;AACR,gBAAI,IAAI,IAAI;AACV,kBAAIA,MAAK,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC;AAAA;AAE7B,kBAAI,IAAIA,MAAK,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC;AAAA,UAC3C;AACA,iBAAO,CAACG,OAAM,QAAQ,CAAC,IAAIA,OAAM,QAAQ,CAAC,IAAIA,OAAM,QAAQ,IAAI,CAAC;AACjE,iBAAM,IAAI,IAAI,KAAK;AACjB,gBAAI,MAAM,KAAK,MAAM;AACnB,qBAAO;AACT,kBAAMA,OAAM,MAAM,GAAG,GAAG,CAAC,IAAI;AAC7B,gBAAIH,MAAK,IAAI,KAAKA,MAAK,IAAI,CAAC,IAAI,KAAKA,MAAK,IAAI,IAAI,CAAC,IAAI,IAAI;AAC3D,gBAAI,MAAM;AACV,iBAAM,IAAI,KAAK,IAAI,MAAMA,MAAK,IAAI,GAAG,KAAK,KAAK,IAAI,MAAM,IAAI,GAAG;AAChE,gBAAI,KAAK;AACP,kBAAI,OAAO,IAAI;AACjB,gBAAI,KAAK;AACP,kBAAI,OAAO,IAAI,IAAI;AACrB,gBAAIA,MAAK,IAAI,CAAC,IAAI,MAAM,KAAK,IAAI;AAC/B;AAAA,UACJ;AACA,iBAAO;AAAA,QACT;AAIA,QAAAG,OAAM,QAAQ,SAAS,MAAM,GAAG,GAAG,GAAG;AAEpC,cAAI,KAAM,MAAM,KAAK,MAAM,IAAM,IAC/BH,MAAK,IAAIG,OAAM,QAAQ,IAAI,CAAC,IAAIA,OAAM,QAAQ,CAAC,IACtCA,OAAM,QAAQ,CAAC,IAAI,IAAIH,MAAK,IAAI,CAAC,IAAI,IACrCA,MAAK,IAAI,IAAI,CAAC,CAAC;AAC1B,cAAI,IAAI,KAAK,IAAI;AACf,mBAAO;AACT,cAAI,KAAK,IAAI,MAAM,IAAI,IAAI;AAEzB,mBAAO,KAAKG,OAAM,OAAO,GAAG,GAAG,CAAC,IAAI;AAEtC,iBAAO,IAAI,KAAKA,OAAM,OAAO,IAAI,GAAG,GAAG,CAAC,IAAI;AAAA,QAC9C;AAKA,QAAAA,OAAM,QAAQ,SAAS,MAAM,GAAG,GAAG;AACjC,cAAI,GAAG,GAAG,GAAG,GAAG;AAChB,cAAI,CAAC;AACH,gBAAI;AACN,cAAI;AACF,mBAAOA,OAAM,OAAO,GAAG,GAAG,WAAW;AAAE,qBAAOA,OAAM,MAAM;AAAA,YAAG,CAAC;AAChE,aAAG;AACD,gBAAIA,OAAM,WAAW;AACrB,gBAAI,UAAUA,OAAM,WAAW,IAAI;AACnC,gBAAI,IAAI;AACR,gBAAIH,MAAK,IAAI,CAAC,IAAI;AAClB,gBAAI,IAAI,IAAI,KAAK,QAAU,IAAI,UAAU;AAAA,UAC3C,SAAS,IAAI,YAAY,IAAI,WAAW,IAAI,IAAI,KAAKA,MAAK,IAAI,CAAC,IAAI,IAAI;AACvE,iBAAO,IAAI;AAAA,QACb;AAIA,QAAAG,OAAM,QAAQ,SAAS,MAAM,OAAO,GAAG,GAAG;AACxC,cAAI,QAAQ;AACZ,cAAI,IAAI,IAAI,GAAG,GAAG,GAAG;AACrB,cAAI,CAAC;AACH,gBAAI;AACN,cAAI,CAAC;AACH,oBAAQ;AACV,cAAI,GAAG;AACL,kBAAMA,OAAM,MAAM,GAAE,CAAC;AACrB,gBAAI,MAAM,WAAW;AAAE,qBAAOA,OAAM,MAAM,KAAK;AAAA,YAAG,CAAC;AACnD,mBAAO;AAAA,UACT;AACA,cAAI,QAAQ;AACV,qBAAS;AACX,eAAK,QAAQ,IAAI;AACjB,eAAK,IAAIH,MAAK,KAAK,IAAI,EAAE;AACzB,aAAG;AACD,eAAG;AACD,kBAAIG,OAAM,MAAM;AAChB,kBAAI,IAAI,KAAK;AAAA,YACf,SAAQ,KAAK;AACb,gBAAI,IAAI,IAAI;AACZ,gBAAIA,OAAM,WAAW;AAAA,UACvB,SAAQ,IAAI,IAAI,QAAQH,MAAK,IAAI,GAAG,CAAC,KAC7BA,MAAK,IAAI,CAAC,IAAI,MAAM,IAAE,IAAI,MAAM,IAAI,IAAIA,MAAK,IAAI,CAAC;AAE1D,cAAI,SAAS;AACX,mBAAO,KAAK;AAEd,aAAG;AACD,gBAAIG,OAAM,WAAW;AAAA,UACvB,SAAQ,MAAM;AACd,iBAAOH,MAAK,IAAI,GAAG,IAAI,KAAK,IAAI,KAAK;AAAA,QACvC;AAIA,SAAC,SAAS,OAAO;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,aAAC,SAAS,UAAU;AACzD,cAAAG,OAAM,GAAG,QAAQ,IAAI,WAAW;AAC9B,uBAAOA;AAAA,kBACHA,OAAM,IAAI,MAAM,SAAS,OAAO;AAAE,2BAAOA,OAAM,QAAQ,EAAE,KAAK;AAAA,kBAAG,CAAC;AAAA,gBAAC;AAAA,cACzE;AAAA,YACF,GAAG,MAAM,CAAC,CAAC;AAAA,QACb,GAAG,wCAAwC,MAAM,GAAG,CAAC;AAGrD,SAAC,SAAS,OAAO;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,aAAC,SAAS,UAAU;AACzD,cAAAA,OAAM,GAAG,QAAQ,IAAI,WAAW;AAC9B,uBAAOA,OAAMA,OAAM,QAAQ,EAAE,MAAM,MAAM,SAAS,CAAC;AAAA,cACrD;AAAA,YACF,GAAG,MAAM,CAAC,CAAC;AAAA,QACb,GAAG,QAAQ,MAAM,GAAG,CAAC;AAAA,MAErB,GAAE,OAAO,IAAI;AACb,OAAC,SAASA,QAAOH,OAAM;AAGvB,SAAC,SAAS,MAAM;AACd,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ;AAAK,aAAC,SAAS,MAAM;AAEpD,cAAAG,OAAM,IAAI,IAAI,SAAS,EAAE,GAAG,GAAG,GAAG;AAChC,oBAAI,EAAE,gBAAgB;AACpB,yBAAO,IAAI,EAAE,GAAG,GAAG,CAAC;AACtB,qBAAK,KAAK;AACV,qBAAK,KAAK;AACV,qBAAK,KAAK;AACV,uBAAO;AAAA,cACT;AAEA,cAAAA,OAAM,GAAG,IAAI,IAAI,SAAS,GAAG,GAAG,GAAG;AACjC,oBAAI,UAAUA,OAAM,IAAI,EAAE,GAAG,GAAG,CAAC;AACjC,wBAAQ,OAAO;AACf,uBAAO;AAAA,cACT;AAEA,cAAAA,OAAM,IAAI,EAAE,UAAU,SAAS,SAAS,KAAK;AAC3C,oBAAI,IAAI,KAAK;AACb,oBAAI,IAAI,KAAK;AACb,oBAAI,IAAI,KAAK;AACb,oBAAI;AACF,yBAAOA,OAAM,MAAM,KAAK,WAAW;AACjC,2BAAOA,OAAM,IAAI,EAAE,OAAO,GAAG,GAAG,CAAC;AAAA,kBACnC,CAAC;AAAA;AAED,yBAAOA,OAAM,IAAI,EAAE,OAAO,GAAG,GAAG,CAAC;AAAA,cACrC;AAEA,eAAC,SAAS,MAAM;AACd,yBAASC,KAAI,GAAGA,KAAI,KAAK,QAAQA;AAAK,mBAAC,SAAS,QAAQ;AACtD,oBAAAD,OAAM,IAAI,EAAE,UAAU,MAAM,IAAI,SAAS,GAAG;AAC1C,0BAAI,IAAI,KAAK;AACb,0BAAI,IAAI,KAAK;AACb,0BAAI,IAAI,KAAK;AACb,0BAAI,CAAC,KAAK,MAAM;AACd,4BAAI,KAAK;AACX,0BAAI,OAAO,MAAM,UAAU;AACzB,+BAAOA,OAAM,GAAG,IAAI,KAAK,GAAG,SAASS,IAAG;AACtC,iCAAOT,OAAM,IAAI,EAAE,MAAM,EAAES,IAAG,GAAG,GAAG,CAAC;AAAA,wBACvC,CAAC;AAAA,sBACH;AACA,6BAAOT,OAAM,IAAI,EAAE,MAAM,EAAE,GAAG,GAAG,GAAG,CAAC;AAAA,oBACvC;AAAA,kBACF,GAAG,KAAKC,EAAC,CAAC;AAAA,cACZ,GAAG,cAAc,MAAM,GAAG,CAAC;AAE3B,eAAC,SAAS,MAAM;AACd,yBAASA,KAAI,GAAGA,KAAI,KAAK,QAAQA;AAAK,mBAAC,SAAS,QAAQ;AACtD,oBAAAD,OAAM,IAAI,EAAE,UAAU,MAAM,IAAI,WAAW;AACzC,6BAAOA,OAAM,IAAI,EAAE,MAAM,EAAE,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAAA,oBACtD;AAAA,kBACF,GAAG,KAAKC,EAAC,CAAC;AAAA,cACZ,GAAG,4BAA4B,MAAM,GAAG,CAAC;AAAA,YAC3C,GAAG,KAAK,CAAC,CAAC;AAAA,QACZ,GACE,sMAGA,MAAM,GAAG,CAAC;AAKZ,QAAAD,OAAM,OAAOA,OAAM,MAAM;AAAA,UACvB,KAAK,SAAS,IAAI,GAAG,OAAO,MAAM;AAEhC,gBAAI,IAAI,KAAK,IAAI;AACf,qBAAO;AAET,gBAAI,SAAS,KAAK,QAAQ;AACxB,qBAAO;AAET,gBAAI,QAAQ,OAAO,OAAO,KAAK;AAC7B,qBAAQH,MAAK,IAAI,GAAG,QAAQ,CAAC,IAAIA,MAAK,IAAI,IAAI,GAAG,OAAO,CAAC,IACrDG,OAAM,OAAO,OAAO,IAAI;AAAA,YAC9B,OAAO;AACL,qBAAOH,MAAK,KAAK,QAAQ,KAAKA,MAAK,IAAI,CAAC,KACvB,OAAO,KAAKA,MAAK,IAAI,IAAI,CAAC,IAC3BG,OAAM,OAAO,OAAO,IAAI,CAAC;AAAA,YAC3C;AAAA,UACF;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,OAAO,MAAM;AAChC,mBAAQ,IAAI,KAAK,IAAI,KAAM,IAAI,KAAK,IAAIA,OAAM,MAAM,GAAG,OAAO,IAAI;AAAA,UACpE;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,OAAO,MAAM;AAChC,mBAAOA,OAAM,SAAS,GAAG,OAAO,IAAI;AAAA,UACtC;AAAA,UAEA,MAAM,SAAS,KAAK,OAAO,MAAM;AAC/B,mBAAO,SAAS,QAAQ;AAAA,UAC1B;AAAA,UAEA,QAAQ,SAAS,OAAO,OAAO,MAAM;AACnC,mBAAOA,OAAM,SAAS,KAAK,OAAO,IAAI;AAAA,UACxC;AAAA,UAEA,MAAM,SAAS,KAAK,OAAO,MAAM;AAC/B,oBAAQ,QAAQ,MAAQ,QAAQ,OAAO;AAAA,UACzC;AAAA;AAAA,UAGA,QAAQ,SAAS,OAAO,OAAO,MAAM;AACnC,gBAAI,IAAIA,OAAM,MAAM,KAAK;AACzB,mBAAO,KAAK,IAAIA,OAAM,MAAM,IAAI;AAAA,UAClC;AAAA,UAEA,UAAU,SAAS,SAAS,OAAO,MAAM;AACvC,mBAAQ,QAAQ,QAASH,MAAK,IAAI,QAAQ,MAAM,CAAC,KAAK,QAAQ,OAAO;AAAA,UACvE;AAAA,QACF,CAAC;AAGD,QAAAG,OAAM,OAAOA,OAAM,UAAU;AAAA;AAAA;AAAA;AAAA,UAI3B,KAAK,SAAS,IAAI,GAAG,KAAK,KAAK;AAC7B,gBAAI,GAAG,GAAG;AAEV,gBAAI,IAAI;AACN,qBAAO;AAET,gBAAI,OAAO,GAAG;AACZ,kBAAI,MAAM,KAAK,MAAM,GAAG;AACtB,uBAAO;AAAA,cACT;AACA,kBAAI,MAAM,KAAK,QAAQ,GAAG;AACxB,uBAAO;AAAA,cACT;AACA,qBAAQ,IAAIA,OAAM,OAAO,MAAM,GAAG,MAAM,CAAC,IACjCH,MAAK,IAAI,MAAM,KAAK,MAAM,CAAC,IAC3BA,MAAK,IAAI,GAAI,MAAI,IAAK,CAAC,IACvBA,MAAK,IAAK,IAAK,MAAM,MAAO,GAAI,EAAE,MAAM,OAAO,CAAC;AAAA,YAC1D;AAEA,gBAAK,MAAM,KAAM,MAAM,IAAI;AAC3B,gBAAI,OAAO,MAAM,IAAI;AACrB,gBAAI,MAAM,IAAI;AACd,mBAAO,IAAIG,OAAM,SAAS,KAAK,MAAM,KAAK,IAAI,MAAM,MAAM,KAAK,GAAG,CAAC;AAAA,UACrE;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,KAAK,KAAK;AAC7B,gBAAI,IAAI;AACN,qBAAO;AACT,mBAAOA,OAAM,MAAO,MAAM,KAAM,MAAM,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC;AAAA,UAClE;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,KAAK,KAAK;AAC7B,mBAAO,OAAO,OAAO,IAAIA,OAAM,SAAS,GAAG,MAAM,GAAG,MAAM,CAAC,IAAI;AAAA,UACjE;AAAA,UAEA,MAAM,SAAS,KAAK,KAAK,KAAK;AAC5B,mBAAQ,MAAM,IAAK,OAAO,MAAM,KAAK;AAAA,UACvC;AAAA,UAEA,MAAM,SAAS,KAAK,KAAK,KAAK;AAC5B,mBAAQ,MAAM,IAAM,OAAO,MAAM,MAAO,OAAO,MAAM,MAAM;AAAA,UAC7D;AAAA;AAAA,UAGA,QAAQ,SAAS,OAAO,KAAK,KAAK;AAChC,gBAAI,KAAKA,OAAM,MAAM,MAAM,CAAC,IAAI;AAChC,gBAAI,KAAKA,OAAM,MAAM,MAAM,CAAC,IAAI;AAChC,mBAAQ,KAAK,OAAQ,KAAK;AAAA,UAC5B;AAAA,UAEA,UAAU,SAAS,SAAS,KAAK,KAAK;AACpC,gBAAI,OAAO;AACT,qBAAO;AACT,mBAAO,IAAI,MAAM,OAAO,MAAM,MAAM,MAC/B,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM;AAAA,UAC5C;AAAA,QACF,CAAC;AAID,QAAAA,OAAM,OAAOA,OAAM,QAAQ;AAAA,UACzB,KAAK,SAAS,IAAI,GAAG,OAAO,OAAO;AACjC,gBAAI,QAAQ,GAAG;AAAE,qBAAO;AAAA,YAAG;AAE3B,mBAAQ,SAASH,MAAK,IAAI,IAAI,OAAO,CAAC,IAAIA,MAAK,IAAI,OAAO,CAAC,KAAMA,MAAK;AAAA,UACxE;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,OAAO,OAAO;AACjC,mBAAOA,MAAK,MAAM,IAAI,SAAS,KAAK,IAAIA,MAAK,KAAK;AAAA,UACpD;AAAA,UAEA,KAAK,SAAS,GAAG,OAAO,OAAO;AAC7B,mBAAO,QAAQ,QAAQA,MAAK,IAAIA,MAAK,MAAM,IAAI,IAAI;AAAA,UACrD;AAAA,UAEA,QAAQ,SAAS,OAAO,OAAkB;AACxC,mBAAO;AAAA,UACT;AAAA,UAEA,MAAM,SAAS,KAAK,OAAkB;AACpC,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,OAAO,OAAO;AACpC,mBAAOG,OAAM,MAAM,IACfH,MAAK,KAAK,KAAK,IAAIG,OAAM,MAAM,GAAG,EAAE,IAAI,QAAQ;AAAA,UACtD;AAAA,QACF,CAAC;AAKD,QAAAA,OAAM,OAAOA,OAAM,WAAW;AAAA,UAC5B,KAAK,SAAS,IAAI,GAAG,KAAK;AACxB,gBAAI,IAAI;AACN,qBAAO;AACT,mBAAQ,MAAM,KAAK,QAAQ,IAAK,MAC5BH,MAAK,KAAK,MAAM,IAAI,KAAKA,MAAK,IAAI,CAAC,IAAI,IAAI,IAAK,MAAM,IAC7CA,MAAK,IAAI,CAAC,IAAIG,OAAM,QAAQ,MAAM,CAAC,CAAC;AAAA,UACnD;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,KAAK;AACxB,gBAAI,IAAI;AACN,qBAAO;AACT,mBAAOA,OAAM,YAAY,MAAM,GAAG,IAAI,CAAC;AAAA,UACzC;AAAA,UAEA,KAAK,SAAS,GAAG,KAAK;AACpB,mBAAO,IAAIA,OAAM,UAAU,GAAG,MAAM,GAAG;AAAA,UACzC;AAAA,UAEA,MAAO,SAAS,KAAK;AACnB,mBAAO;AAAA,UACT;AAAA;AAAA,UAGA,QAAQ,SAAS,OAAO,KAAK;AAC3B,mBAAO,MAAMH,MAAK,IAAI,IAAK,KAAK,IAAI,MAAO,CAAC;AAAA,UAC9C;AAAA,UAEA,MAAM,SAAS,KAAK,KAAK;AACvB,mBAAQ,MAAM,IAAI,IAAK,MAAM,IAAI;AAAA,UACnC;AAAA,UAEA,QAAQ,SAAS,OAAO,KAAK;AAC3B,mBAAOG,OAAM,MAAM,MAAM,CAAC,IAAI;AAAA,UAChC;AAAA,UAEA,UAAU,SAAS,SAAS,KAAK;AAC/B,mBAAO,IAAI;AAAA,UACb;AAAA,QACF,CAAC;AAKD,QAAAA,OAAM,OAAOA,OAAM,aAAa;AAAA,UAC9B,KAAK,SAAS,IAAI,GAAG,MAAM;AACzB,mBAAO,IAAI,IAAI,IAAI,OAAOH,MAAK,IAAI,CAAC,OAAO,CAAC;AAAA,UAC9C;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,MAAM;AACzB,mBAAO,IAAI,IAAI,IAAI,IAAIA,MAAK,IAAI,CAAC,OAAO,CAAC;AAAA,UAC3C;AAAA,UAEA,KAAK,SAAS,GAAG,MAAM;AACrB,mBAAO,CAACA,MAAK,IAAI,IAAI,CAAC,IAAI;AAAA,UAC5B;AAAA,UAEA,MAAO,SAAS,MAAM;AACpB,mBAAO,IAAI;AAAA,UACb;AAAA,UAEA,QAAQ,SAAU,MAAM;AACtB,mBAAQ,IAAI,OAAQA,MAAK,IAAI,CAAC;AAAA,UAChC;AAAA,UAEA,MAAM,SAAS,OAAe;AAC5B,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,MAAM;AAC5B,mBAAO,KAAK,OAAOA,MAAK,IAAIG,OAAM,WAAW,CAAC;AAAA,UAChD;AAAA,UAEA,UAAW,SAAS,MAAM;AACxB,mBAAOH,MAAK,IAAI,MAAM,EAAE;AAAA,UAC1B;AAAA,QACF,CAAC;AAKD,QAAAG,OAAM,OAAOA,OAAM,OAAO;AAAA,UACxB,KAAK,SAAS,IAAI,GAAG,OAAO,OAAO;AACjC,gBAAI,IAAI;AACN,qBAAO;AACT,mBAAQ,MAAM,KAAK,UAAU,IAAK,IAAI,QAC9BH,MAAK,KAAK,QAAQ,KAAKA,MAAK,IAAI,CAAC,IAAI,IAAI,QACjCG,OAAM,QAAQ,KAAK,IAAI,QAAQH,MAAK,IAAI,KAAK,CAAC;AAAA,UAChE;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,OAAO,OAAO;AACjC,gBAAI,IAAI;AACN,qBAAO;AACT,mBAAOG,OAAM,YAAY,OAAO,IAAI,KAAK;AAAA,UAC3C;AAAA,UAEA,KAAK,SAAS,GAAG,OAAO,OAAO;AAC7B,mBAAOA,OAAM,UAAU,GAAG,KAAK,IAAI;AAAA,UACrC;AAAA,UAEA,MAAO,SAAS,OAAO,OAAO;AAC5B,mBAAO,QAAQ;AAAA,UACjB;AAAA,UAEA,MAAM,SAAS,KAAK,OAAO,OAAO;AAChC,gBAAG,QAAQ;AAAG,sBAAQ,QAAQ,KAAK;AACnC,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,OAAO,OAAO;AACpC,mBAAOA,OAAM,MAAM,KAAK,IAAI;AAAA,UAC9B;AAAA,UAEA,UAAU,SAAS,SAAS,OAAO,OAAO;AACxC,mBAAO,QAAQ,QAAQ;AAAA,UACzB;AAAA,QACF,CAAC;AAGD,QAAAA,OAAM,OAAOA,OAAM,UAAU;AAAA,UAC3B,KAAK,SAAS,IAAI,GAAG,OAAO,OAAO;AACjC,gBAAI,KAAK;AACP,qBAAO;AACT,mBAAOH,MAAK,IAAI,EAAE,QAAQ,KAAKA,MAAK,IAAI,CAAC,IAAI,QAAQ,IACrCG,OAAM,QAAQ,KAAK,IAAI,QAAQH,MAAK,IAAI,KAAK,CAAC;AAAA,UAChE;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,OAAO,OAAO;AACjC,gBAAI,KAAK;AACP,qBAAO;AACT,mBAAO,IAAIG,OAAM,YAAY,OAAO,QAAQ,CAAC;AAAA,UAC/C;AAAA,UAEA,KAAK,SAAS,GAAG,OAAO,OAAO;AAC7B,mBAAO,QAAQA,OAAM,UAAU,IAAI,GAAG,KAAK;AAAA,UAC7C;AAAA,UAEA,MAAO,SAAS,OAAO,OAAO;AAC5B,mBAAQ,QAAQ,IAAK,SAAS,QAAQ,KAAK;AAAA,UAC7C;AAAA,UAEA,MAAM,SAAS,KAAK,OAAO,OAAO;AAChC,mBAAO,SAAS,QAAQ;AAAA,UAC1B;AAAA,UAEA,QAAQ,SAAS,OAAO,OAAO,OAAO;AACpC,mBAAO,QAAQA,OAAM,MAAM,KAAK;AAAA,UAClC;AAAA,UAEA,UAAU,SAAS,SAAS,OAAO,OAAO;AACxC,gBAAI,SAAS;AACX,qBAAO;AACT,mBAAO,QAAQ,UAAU,QAAQ,MAAM,QAAQ,MAAM,QAAQ;AAAA,UAC/D;AAAA,QACF,CAAC;AAID,QAAAA,OAAM,OAAOA,OAAM,aAAa;AAAA,UAC9B,KAAK,SAAS,IAAI,GAAG,OAAO,MAAM;AAChC,gBAAI,MAAM,KAAK,UAAU;AACvB,qBAAO;AAAA,qBACA,MAAM,KAAK,SAAS;AAC3B,qBAAO;AACT,mBAAOH,MAAK,IAAIA,MAAK,IAAI,KAAK,IAAIA,MAAK,IAAI,IAAI,KAAK,QAAQ,KAC5CA,MAAK,IAAI,CAAC,KAAK,OAAO,KACtBA,MAAK,IAAI,IAAIA,MAAK,IAAI,GAAG,KAAK,CAAC,CAAC;AAAA,UAClD;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,OAAO,MAAM;AAChC,gBAAI,IAAI;AACN,qBAAO;AAAA,qBACA,IAAI;AACX,qBAAO;AACT,mBAAQ,IAAIA,MAAK,IAAI,IAAIA,MAAK,IAAI,GAAG,KAAK,GAAG,IAAI;AAAA,UACnD;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,OAAO,MAAM;AAChC,mBAAOA,MAAK,IAAI,IAAIA,MAAK,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,KAAK;AAAA,UAC1D;AAAA,UAEA,MAAO,SAAS,OAAO,MAAM;AAC3B,mBAAQ,OAAOG,OAAM,QAAQ,IAAI,IAAI,KAAK,IAClCA,OAAM,QAAQ,IAAI,IAAMA,OAAM,QAAQ,IAAI,IAAI,QAAQ,IAAI;AAAA,UACpE;AAAA,UAEA,QAAQ,SAAS,OAAO,OAAO,MAAM;AACnC,mBAAOH,MAAK,IAAI,IAAIA,MAAK,IAAI,GAAG,KAAK,IAAI,GAAG,IAAI,KAAK;AAAA,UACvD;AAAA,UAEA,MAAM,SAAS,KAAK,OAAO,MAAM;AAC/B,gBAAI,EAAE,SAAS,KAAK,QAAQ,MAAM,UAAU,KAAK,SAAS;AACxD,qBAAO;AACT,mBAAOA,MAAK,KAAK,QAAQ,MAAM,QAAQ,OAAO,IAAI,IAAI,KAAK;AAAA,UAC7D;AAAA,UAEA,UAAU,SAAS,WAA0B;AAC3C,kBAAM,IAAI,MAAM,8BAA8B;AAAA,UAEhD;AAAA,QACF,CAAC;AAKD,QAAAG,OAAM,OAAOA,OAAM,WAAW;AAAA,UAC5B,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO;AAC9B,gBAAI,KAAK;AACP,qBAAO;AACT,mBAAOH,MAAK,IAAI,CAACA,MAAK,IAAI,CAAC,IAAI,MAAMA,MAAK,IAAI,IAAIA,MAAK,EAAE,IACzCA,MAAK,IAAI,KAAK,IAAIA,MAAK,IAAIA,MAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAC7C,IAAI,QAAQ,MAAM;AAAA,UACrC;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO;AAC9B,gBAAI,IAAI;AACN,qBAAO;AACT,mBAAO,MACF,MAAMG,OAAM,KAAKH,MAAK,IAAI,CAAC,IAAI,MAAMA,MAAK,KAAK,IAAI,QAAQ,KAAK,CAAC;AAAA,UACxE;AAAA,UAEA,KAAK,SAAS,GAAG,IAAI,OAAO;AAC1B,mBAAOA,MAAK,IAAI,sBAAuB,QAAQG,OAAM,QAAQ,IAAI,CAAC,IAAI,EAAE;AAAA,UAC1E;AAAA,UAEA,MAAM,SAAS,KAAK,IAAI,OAAO;AAC7B,mBAAOH,MAAK,IAAI,KAAK,QAAQ,QAAQ,CAAC;AAAA,UACxC;AAAA,UAEA,QAAQ,SAAS,OAAO,IAAe;AACrC,mBAAOA,MAAK,IAAI,EAAE;AAAA,UACpB;AAAA,UAEA,MAAM,SAAS,KAAK,IAAI,OAAO;AAC7B,mBAAOA,MAAK,IAAI,KAAK,QAAQ,KAAK;AAAA,UACpC;AAAA,UAEA,QAAQ,SAAS,OAAO,IAAI,OAAO;AACjC,mBAAOA,MAAK,IAAIG,OAAM,MAAM,IAAI,QAAQ,EAAE;AAAA,UAC5C;AAAA,UAEA,UAAU,SAAS,SAAS,IAAI,OAAO;AACrC,oBAAQH,MAAK,IAAI,QAAQ,KAAK,IAAI,KAAKA,MAAK,IAAI,IAAI,KAAK,QAAQ,KAAK;AAAA,UACxE;AAAA,QACF,CAAC;AAKD,QAAAG,OAAM,OAAOA,OAAM,aAAa;AAAA,UAC9B,KAAK,SAAS,IAAI,GAAG,KAAK,KAAK;AAC7B,gBAAI,MAAM;AACV,gBAAIH,MAAK,IAAI,GAAG,IAAI;AAClB,qBAAOG,OAAM,SAAS,IAAI,GAAG,GAAG;AAElC,gBAAIH,MAAK,IAAI,CAAC,IAAI,KAAK;AACrB,qBAAOA,MAAK,IAAIG,OAAM,SAAS,MAAM,KAAK,CAAC,IAAI,MAAM,MAAM,IAC3C,MAAMH,MAAK,IAAIA,MAAK,KAAK,GAAG,IAAIG,OAAM,QAAQ,MAAM,CAAC,CAAC;AAAA,YACxE;AAGA,mBAAO,MAAM,KACRA,OAAM,YAAY,IAAI,IAAIH,MAAK,KAAK,IAAI,IAAI,GAAG,GAAG,MAAI,GAAG,GAAG,IAC5DG,OAAM,YAAY,IAAI,GAAG,KAAK,GAAG;AAAA,UACxC;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,KAAK,KAAK;AAC7B,gBAAI,MAAM;AACV,gBAAI,iBAAiB;AAErB,gBAAIH,MAAK,IAAI,GAAG,IAAI;AAClB,qBAAOG,OAAM,SAAS,IAAI,GAAG,GAAG;AAGlC,gBAAI,OAAO;AACX,gBAAI,IAAI,GAAG;AACT,qBAAO;AACP,oBAAM,CAAC;AAAA,YACT;AAEA,gBAAI,OAAOA,OAAM,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC;AACtC,gBAAI,QAAQ,MAAM;AAElB,gBAAI,YAAY;AAChB,gBAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AACzB,gBAAI,IAAI;AACR,gBAAI,IAAIH,MAAK,IAAI,CAAC,MAAM,MAAM,CAAC;AAC/B,gBAAI,IAAIA,MAAK,IAAI,CAAC,MAAM,MAAM,IAAI,MAAMA,MAAK,IAAI,CAAC,IACjCG,OAAM,QAAQ,IAAI,CAAC,CAAC,IAAI;AACzC,mBAAO,IAAI,kBAAkB,YAAY,OAAO,QAAQ,KAAK;AAC3D,0BAAY;AACZ,kBAAI,IAAI,GAAG;AACT,qBAAM,MAAM,OAAQ,IAAI;AACxB,qBAAM,MAAM,OAAQ,KAAK,IAAI,IAAI;AAAA,cACnC;AACA,sBAAQ,IAAIA,OAAM,KAAK,IAAI,GAAG,IAAI,KAAK,MAAM,CAAC,IAC1C,IAAIA,OAAM,KAAK,IAAI,GAAG,IAAE,GAAG,MAAI,CAAC;AACpC,sBAAQ,MAAM;AACd;AAAA,YACF;AAEA,mBAAO,OAAQ,IAAI,OAAQ;AAAA,UAC7B;AAAA,QACF,CAAC;AAID,QAAAA,OAAM,OAAOA,OAAM,QAAQ;AAAA,UACzB,KAAK,SAAS,IAAI,GAAG,MAAM,KAAK;AAC9B,mBAAOH,MAAK,IAAI,OAAOA,MAAK,IAAI,IAAIA,MAAK,EAAE,IAC3BA,MAAK,IAAI,GAAG,IAAIA,MAAK,IAAI,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,IAAI;AAAA,UACzE;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,MAAM,KAAK;AAC9B,mBAAO,OAAO,IAAIG,OAAM,KAAK,IAAI,QAAQH,MAAK,KAAK,IAAI,MAAM,GAAG,CAAC;AAAA,UACnE;AAAA,UAEA,KAAK,SAAS,GAAG,MAAM,KAAK;AAC1B,mBAAO,sBAAuB,MAAMG,OAAM,QAAQ,IAAI,CAAC,IAAI;AAAA,UAC7D;AAAA,UAEA,MAAO,SAAS,MAAe;AAC7B,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,MAAe;AACrC,mBAAO;AAAA,UACT;AAAA,UAEA,MAAM,SAAU,MAAe;AAC7B,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,MAAM,KAAK;AACjC,mBAAOA,OAAM,MAAM,IAAI,MAAM;AAAA,UAC/B;AAAA,UAEA,UAAW,SAAS,MAAM,KAAK;AAC7B,mBAAO,MAAM;AAAA,UACf;AAAA,QACF,CAAC;AAKD,QAAAA,OAAM,OAAOA,OAAM,QAAQ;AAAA,UACzB,KAAK,SAAS,IAAI,GAAG,OAAO,OAAO;AACjC,gBAAI,IAAI;AACN,qBAAO;AACT,mBAAQ,QAAQH,MAAK,IAAI,OAAO,KAAK,IAAKA,MAAK,IAAI,GAAG,QAAQ,CAAC;AAAA,UACjE;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,OAAO,OAAO;AACjC,gBAAI,IAAI;AACN,qBAAO;AACT,mBAAO,IAAIA,MAAK,IAAI,QAAQ,GAAG,KAAK;AAAA,UACtC;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,OAAO,OAAO;AACjC,mBAAO,QAAQA,MAAK,IAAI,IAAI,GAAG,IAAI,KAAK;AAAA,UAC1C;AAAA,UAEA,MAAM,SAAS,KAAK,OAAO,OAAO;AAChC,gBAAI,SAAS;AACX,qBAAO;AACT,mBAAQ,QAAQA,MAAK,IAAI,OAAO,KAAK,KAAM,QAAQ;AAAA,UACrD;AAAA,UAEA,QAAQ,SAAS,OAAO,OAAO,OAAO;AACpC,mBAAO,SAAS,QAAQA,MAAK;AAAA,UAC/B;AAAA,UAEA,MAAM,SAAS,KAAK,OAAkB;AACpC,mBAAO;AAAA,UACT;AAAA,UAEA,UAAW,SAAS,OAAO,OAAO;AAChC,gBAAI,SAAS;AACX,qBAAO;AACT,mBAAQ,QAAM,QAAQ,SAAUA,MAAK,IAAI,QAAQ,GAAG,CAAC,KAAK,QAAQ;AAAA,UACpE;AAAA,QACF,CAAC;AAKD,QAAAG,OAAM,OAAOA,OAAM,UAAU;AAAA,UAC3B,KAAK,SAAS,IAAI,GAAG,KAAK;AACxB,kBAAM,MAAM,QAAQ,QAAQ;AAC5B,mBAAQ,KAAGH,MAAK,KAAK,GAAG,IAAIG,OAAM,OAAO,KAAK,MAAI,CAAC,KAC/CH,MAAK,IAAI,IAAM,IAAI,IAAK,KAAM,GAAG,MAAM,KAAK,EAAE;AAAA,UACpD;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,KAAK;AACxB,gBAAI,OAAO,MAAM;AACjB,mBAAOG,OAAM,OAAO,IAAIH,MAAK,KAAK,IAAI,IAAI,GAAG,MACzB,IAAIA,MAAK,KAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAI;AAAA,UAC7D;AAAA,UAEA,KAAK,SAAS,GAAG,KAAK;AACpB,gBAAI,IAAIG,OAAM,SAAS,IAAIH,MAAK,IAAI,GAAG,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG;AAC7D,gBAAIA,MAAK,KAAK,OAAO,IAAI,KAAK,CAAC;AAC/B,mBAAQ,IAAI,MAAO,IAAI,CAAC;AAAA,UAC1B;AAAA,UAEA,MAAM,SAAS,KAAK,KAAK;AACvB,mBAAQ,MAAM,IAAK,IAAI;AAAA,UACzB;AAAA,UAEA,QAAQ,SAAS,SAAgB;AAC/B,mBAAO;AAAA,UACT;AAAA,UAEA,MAAM,SAAS,OAAc;AAC3B,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,KAAK;AAC3B,mBAAOG,OAAM,MAAM,IAAIH,MAAK,KAAK,OAAO,IAAIG,OAAM,MAAM,MAAM,CAAC,EAAE;AAAA,UACnE;AAAA,UAEA,UAAU,SAAS,SAAS,KAAK;AAC/B,mBAAQ,MAAO,IAAK,OAAO,MAAM,KAAM,MAAM,IAAK,WAAW;AAAA,UAC/D;AAAA,QACF,CAAC;AAKD,QAAAA,OAAM,OAAOA,OAAM,SAAS;AAAA,UAC1B,KAAK,SAAS,IAAI,GAAG,OAAO,OAAO;AACjC,gBAAI,IAAI,KAAK,QAAQ,KAAK,QAAQ;AAChC,qBAAO;AACT,mBAAQ,QAAQ,QAASH,MAAK,IAAK,IAAI,OAAS,QAAQ,CAAE,IACtDA,MAAK,IAAI,CAAEA,MAAK,IAAK,IAAI,OAAQ,KAAK,CAAE;AAAA,UAC9C;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,OAAO,OAAO;AACjC,mBAAO,IAAI,IAAI,IAAI,IAAIA,MAAK,IAAI,CAACA,MAAK,IAAK,IAAI,OAAQ,KAAK,CAAC;AAAA,UAC/D;AAAA,UAEA,KAAK,SAAS,GAAG,OAAO,OAAO;AAC7B,mBAAO,QAAQA,MAAK,IAAI,CAACA,MAAK,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK;AAAA,UACrD;AAAA,UAEA,MAAO,SAAS,OAAO,OAAO;AAC5B,mBAAO,QAAQG,OAAM,QAAQ,IAAI,IAAI,KAAK;AAAA,UAC5C;AAAA,UAEA,QAAQ,SAAS,OAAO,OAAO,OAAO;AACpC,mBAAO,QAAQH,MAAK,IAAIA,MAAK,IAAI,CAAC,GAAG,IAAI,KAAK;AAAA,UAChD;AAAA,UAEA,MAAM,SAAS,KAAK,OAAO,OAAO;AAChC,gBAAI,SAAS;AACX,qBAAO;AACT,mBAAO,QAAQA,MAAK,KAAK,QAAQ,KAAK,OAAO,IAAI,KAAK;AAAA,UACxD;AAAA,UAEA,QAAQ,SAAS,OAAO,OAAO,OAAO;AACpC,mBAAO,QAAQA,MAAK,IAAI,CAACA,MAAK,IAAIG,OAAM,WAAW,CAAC,GAAG,IAAI,KAAK;AAAA,UAClE;AAAA,UAEA,UAAU,SAAS,SAAS,OAAO,OAAO;AACxC,mBAAO,QAAQ,QAAQA,OAAM,QAAQ,IAAI,IAAI,KAAK,IAC9CH,MAAK,IAAIG,OAAM,QAAQ,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,UAClD;AAAA,QACF,CAAC;AAKD,QAAAA,OAAM,OAAOA,OAAM,SAAS;AAAA,UAC1B,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG;AACzB,mBAAQ,IAAI,KAAK,IAAI,IAAK,IAAI,KAAK,IAAI;AAAA,UACzC;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG;AACzB,gBAAI,IAAI;AACN,qBAAO;AAAA,qBACA,IAAI;AACX,sBAAQ,IAAI,MAAM,IAAI;AACxB,mBAAO;AAAA,UACT;AAAA,UAEA,KAAK,SAAS,GAAG,GAAG,GAAG;AACrB,mBAAO,IAAK,KAAK,IAAI;AAAA,UACvB;AAAA,UAEA,MAAM,SAAS,KAAK,GAAG,GAAG;AACxB,mBAAO,OAAO,IAAI;AAAA,UACpB;AAAA,UAEA,QAAQ,SAAS,OAAO,GAAG,GAAG;AAC5B,mBAAOA,OAAM,KAAK,GAAG,CAAC;AAAA,UACxB;AAAA,UAEA,MAAM,SAAS,OAAe;AAC5B,kBAAM,IAAI,MAAM,6BAA6B;AAAA,UAC/C;AAAA,UAEA,QAAQ,SAAS,OAAO,GAAG,GAAG;AAC5B,mBAAQ,IAAI,IAAI,IAAI,KAAM,IAAI,IAAI,IAAI,MAAM,IAAIA,OAAM,WAAW,IAAI;AAAA,UACvE;AAAA,UAEA,UAAU,SAAS,SAAS,GAAG,GAAG;AAChC,mBAAOH,MAAK,IAAI,IAAI,GAAG,CAAC,IAAI;AAAA,UAC9B;AAAA,QACF,CAAC;AAID,iBAAS,OAAO,GAAG,GAAG,GAAG,KAAK;AAC5B,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI;AAEJ,iBAAOA,MAAK,KAAK,KAAK,MAAM,EAAE,IAAI,KAAK;AACrC,iBAAK;AACL,iBAAK,EAAE,IAAI,OAAO,IAAI,IAAI,MAAM,KAAK,IAAI,IAAI,OAAO,IAAI,IAAI,KAAK;AACjE,iBAAK,KAAK,KAAK;AACf,iBAAK,KAAK,KAAK;AACf,iBAAK,KAAK;AACV,iBAAK,MAAM,IAAI,MAAM,KAAK,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI;AACrD,iBAAK,KAAK,KAAK;AACf,iBAAK,KAAK,KAAK;AACf,iBAAK,KAAK;AACV,iBAAK,KAAK;AACV,iBAAK,KAAK;AACV,iBAAK;AAAA,UACP;AAEA,iBAAO,KAAK;AAAA,QACd;AAIA,QAAAG,OAAM,OAAOA,OAAM,UAAU;AAAA,UAC3B,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG;AACzB,mBAAQ,MAAM,KAAK,MAAM,IACrB,IAAI,MAAO,IAAI,IAAI,IACrBA,OAAM,YAAY,GAAG,CAAC,IAAIH,MAAK,IAAI,GAAG,CAAC,IAAIA,MAAK,IAAI,IAAI,GAAG,IAAI,CAAC;AAAA,UACpE;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG;AACzB,gBAAI;AACJ,gBAAI,MAAM;AAEV,gBAAI,IAAI;AACN,qBAAO;AACT,gBAAI,KAAK;AACP,qBAAO;AACT,gBAAI,IAAI,KAAK,IAAI,KAAK,KAAK;AACzB,qBAAO;AAET,gBAAIA,MAAK,MAAM,CAAC;AAChB,gBAAI,IAAI;AACR,gBAAI,IAAI,IAAI;AACZ,gBAAI,IAAI,IAAI;AACZ,gBAAI,IAAI,IAAI;AACZ,gBAAI,KAAKA,MAAK,IAAIG,OAAM,QAAQ,CAAC,IAAIA,OAAM,QAAQ,CAAC,IAClCA,OAAM,QAAQ,CAAC,IAAI,IAAIH,MAAK,IAAI,CAAC,IAAI,IAAIA,MAAK,IAAI,IAAI,CAAC,CAAC;AAE1E,gBAAI,KAAK,IAAI,MAAM,IAAI;AACrB,wBAAU,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;AAAA;AAElC,wBAAU,IAAI,KAAK,OAAO,IAAI,GAAG,GAAG,GAAG,GAAG;AAE5C,mBAAOA,MAAK,OAAO,IAAI,YAAY,IAAI,IAAI,KAAK,IAAI;AAAA,UACtD;AAAA,QACF,CAAC;AAKD,QAAAG,OAAM,OAAOA,OAAM,QAAQ;AAAA,UACzB,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG;AACzB,gBAAI,MAAM,MAAM;AACd,qBAAO;AACT,gBAAI,IAAI;AACN,qBAAO;AACT,mBAAOA,OAAM,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,IACrCH,MAAK,IAAI,IAAI,GAAG,CAAC,IAAIA,MAAK,IAAI,GAAG,CAAC;AAAA,UACxC;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG;AACzB,gBAAI,MAAM,GACV,IAAI;AACJ,gBAAI,IAAI;AAAG,qBAAO;AAClB,mBAAO,KAAK,GAAG,KAAK;AAClB,qBAAOG,OAAM,OAAO,IAAI,GAAG,GAAG,CAAC;AAAA,YACjC;AACA,mBAAO;AAAA,UACT;AAAA,QACF,CAAC;AAKD,QAAAA,OAAM,OAAOA,OAAM,SAAS;AAAA,UAC1B,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG,GAAG;AAU5B,gBAAG,MAAM,IAAI,GAAG;AACd,qBAAO;AAAA,YACT,WAAU,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI;AAElC,qBAAO;AAAA,YACT,WAAU,IAAI,KAAK,IAAI,GAAG;AAExB,qBAAO;AAAA,YACT,WAAW,IAAI,IAAI,GAAG;AAGpB,kBAAG,IAAI,IAAI,GAAG;AAGZ,uBAAOA,OAAM,QAAQ,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;AAAA,cACzD,OAAO;AAGL,uBAAOA,OAAM,QAAQ,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,cAC7C;AAAA,YAEF,WAAU,IAAI,IAAI,GAAG;AAGnB,qBAAOA,OAAM,QAAQ,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,YAE7C,WAAU,IAAI,GAAG;AAGf,qBAAOA,OAAM,QAAQ,IAAI,GAAG,GAAG,GAAG,CAAC;AAAA,YACrC,OAAO;AAYL,kBAAI,YAAY;AAGhB,kBAAI,cAAc;AAElB,uBAAQ,IAAI,GAAG,IAAI,GAAG,KAAK;AAGzB,uBAAM,YAAY,KAAK,cAAc,GAAG;AAItC,+BAAa,IAAK,KAAK,IAAI;AAG3B;AAAA,gBACF;AAIA,8BAAc,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI;AAAA,cAC/D;AAEA,qBAAM,cAAc,GAAG,eAAe;AAEpC,6BAAa,IAAK,KAAK,IAAI;AAAA,cAC7B;AAGA,qBAAOH,MAAK,IAAI,GAAGA,MAAK,IAAI,GAAG,SAAS,CAAC;AAAA,YAC3C;AAAA,UACF;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG,GAAG;AAY5B,gBAAG,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI;AAE3B,qBAAO;AAAA,YACT,WAAU,KAAK,KAAK,KAAK,GAAG;AAE1B,qBAAO;AAAA,YACT,WAAW,IAAI,IAAI,GAAG;AAGpB,kBAAG,IAAI,IAAI,GAAG;AAGZ,uBAAOG,OAAM,QAAQ,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;AAAA,cACzD,OAAO;AAGL,uBAAO,IAAIA,OAAM,QAAQ,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,cACrD;AAAA,YAEF,WAAU,IAAI,IAAI,GAAG;AAGnB,qBAAO,IAAIA,OAAM,QAAQ,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,YAErD,WAAU,IAAI,GAAG;AAGf,qBAAOA,OAAM,QAAQ,IAAI,GAAG,GAAG,GAAG,CAAC;AAAA,YACrC,OAAO;AAWL,kBAAI,YAAY;AAIhB,kBAAI,YAAY;AAGhB,kBAAI,cAAc;AAElB,uBAAQ,IAAI,GAAG,IAAI,GAAG,KAAK;AAGzB,uBAAM,YAAY,KAAK,cAAc,GAAG;AAItC,sBAAI,SAAS,IAAK,KAAK,IAAI;AAE3B,+BAAa;AACb,+BAAa;AAGb;AAAA,gBACF;AAIA,8BAAc,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI;AAG7D,6BAAa;AAAA,cACf;AAEA,qBAAM,cAAc,GAAG,eAAe;AAEpC,6BAAa,IAAK,KAAK,IAAI;AAAA,cAC7B;AAGA,qBAAOH,MAAK,IAAI,GAAGA,MAAK,IAAI,GAAG,SAAS,CAAC;AAAA,YAC3C;AAAA,UACF;AAAA,QACF,CAAC;AAKD,QAAAG,OAAM,OAAOA,OAAM,SAAS;AAAA,UAC1B,KAAK,SAAS,IAAI,GAAG,GAAG;AACtB,gBAAI,IAAI,KAAM,IAAI,MAAO,KAAK,IAAI,GAAG;AACnC,qBAAO;AAAA,YACT;AAEA,mBAAOH,MAAK,IAAI,GAAG,CAAC,IAAIA,MAAK,IAAI,CAAC,CAAC,IAAIG,OAAM,UAAU,CAAC;AAAA,UAC1D;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,GAAG;AACtB,gBAAI,SAAS,CAAC,GACd,IAAI;AACJ,gBAAI,IAAI;AAAG,qBAAO;AAClB,mBAAO,KAAK,GAAG,KAAK;AAClB,qBAAO,KAAKA,OAAM,QAAQ,IAAI,GAAG,CAAC,CAAC;AAAA,YACrC;AACA,mBAAOA,OAAM,IAAI,MAAM;AAAA,UACzB;AAAA,UAEA,MAAO,SAAS,GAAG;AACjB,mBAAO;AAAA,UACT;AAAA,UAEA,UAAW,SAAS,GAAG;AACrB,mBAAO;AAAA,UACT;AAAA,UAEA,aAAa,SAAS,YAAY,GAAG;AACnC,gBAAI,IAAI,GAAG,IAAI,GAAG,IAAIH,MAAK,IAAI,CAAC,CAAC;AACjC,eAAG;AACD;AACA,mBAAKG,OAAM,WAAW;AAAA,YACxB,SAAS,IAAI;AACb,mBAAO,IAAI;AAAA,UACb;AAAA,UAEA,aAAa,SAAS,YAAY,GAAG;AACnC,gBAAI,MAAM;AACV,gBAAI;AACJ,gBAAI,GAAG,GAAG,MAAM,QAAQ,GAAG,GAAG,UAAU,IAAI;AAE5C,mBAAOH,MAAK,KAAK,GAAG;AACpB,qBAASA,MAAK,IAAI,GAAG;AACrB,gBAAI,QAAQ,OAAO;AACnB,gBAAI,SAAS,UAAU;AACvB,uBAAW,SAAS,UAAU,IAAI;AAClC,iBAAK,SAAS,UAAU,IAAI;AAE5B,mBAAO,GAAG;AACR,kBAAIA,MAAK,OAAO,IAAI;AACpB,kBAAIA,MAAK,OAAO;AAChB,mBAAK,MAAMA,MAAK,IAAI,CAAC;AACrB,kBAAIA,MAAK,OAAO,IAAI,IAAI,KAAK,KAAK,IAAI,MAAM,IAAI;AAChD,kBAAK,MAAM,QAAU,KAAK,IAAK;AAC3B,uBAAO;AAAA,cACX;AACA,kBAAK,IAAI,KAAQ,KAAK,SAAW,IAAI,IAAM;AACvC;AAAA,cACJ;AAGA,kBAAKA,MAAK,IAAI,CAAC,IAAIA,MAAK,IAAI,QAAQ,IAAIA,MAAK,IAAI,KAAK,KAAK,MAAM,CAAC,KAAO,CAAC,MAAM,IAAI,SAASG,OAAM,OAAO,IAAI,CAAC,GAAI;AAC/G,uBAAO;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,UAEA,QAAQ,SAAS,OAAO,GAAG;AACzB,gBAAI,IAAI;AACN,qBAAO,KAAK,YAAY,CAAC;AAAA;AAEzB,qBAAO,KAAK,YAAY,CAAC;AAAA,UAC7B;AAAA,QACF,CAAC;AAGD,QAAAA,OAAM,OAAOA,OAAM,YAAY;AAAA,UAC7B,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG,GAAG;AAC5B,gBAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG;AAC5B,qBAAO;AAAA,YACT,OAAO;AACL,kBAAI,IAAI,KAAK,IAAI,GAAG;AAClB,uBAAO;AAAA,cACT,WAAW,IAAI,GAAG;AACd,uBAAQ,KAAK,IAAI,OAAQ,IAAI,MAAM,IAAI;AAAA,cAC3C,WAAW,MAAM,GAAG;AAChB,uBAAQ,KAAK,IAAI;AAAA,cACrB,OAAO;AACH,uBAAQ,KAAK,IAAI,OAAQ,IAAI,MAAM,IAAI;AAAA,cAC3C;AAAA,YACF;AAAA,UACF;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG,GAAG;AAC5B,gBAAI,KAAK,KAAK,IAAI,KAAK,IAAI;AACzB,qBAAO;AACT,gBAAI,KAAK;AACP,qBAAO;AAAA,qBACA,KAAK;AACZ,qBAAO;AACT,gBAAI,KAAK;AACP,qBAAOH,MAAK,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,MAAM,IAAI;AAAA;AAE5C,qBAAO,IAAIA,MAAK,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,MAAM,IAAI;AAAA,UACpD;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG,GAAG;AAC5B,gBAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG;AAC5B,qBAAO;AAAA,YACT,OAAO;AACL,kBAAI,MAAO,IAAI,MAAM,IAAI,IAAK;AAC5B,uBAAO,KAAK,IAAI,KAAKA,MAAK,KAAK,MAAM,IAAI,MAAM,IAAI,GAAG;AAAA,cACxD,OAAO;AACL,uBAAO,KAAK,IAAI,MAAM,IAAIA,MAAK,MAAM,IAAI,MAAM,KAAM,IAAI,MAAM,IAAI,GAAI;AAAA,cACzE;AAAA,YACF;AAAA,UACF;AAAA,UAEA,MAAM,SAAS,KAAK,GAAG,GAAG,GAAG;AAC3B,oBAAQ,IAAI,IAAI,KAAK;AAAA,UACvB;AAAA,UAEA,QAAQ,SAAS,OAAO,GAAG,GAAG,GAAG;AAC/B,gBAAI,MAAM,IAAI,KAAK,GAAG;AACpB,qBAAO,IAAIA,MAAK,MAAM,IAAI,MAAM,IAAI,EAAE,IAAIA,MAAK,KAAK,CAAC;AAAA,YACvD,WAAW,KAAK,IAAI,KAAK,GAAG;AAC1B,qBAAO,IAAIA,MAAK,MAAM,IAAI,MAAM,IAAI,EAAE,IAAIA,MAAK,KAAK,CAAC;AAAA,YACvD;AAAA,UACF;AAAA,UAEA,MAAM,SAAS,KAAK,GAAG,GAAG,GAAG;AAC3B,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,GAAG,GAAG,GAAG;AAC/B,gBAAI,IAAIG,OAAM,WAAW;AACzB,gBAAI,KAAM,IAAI,MAAM,IAAI;AACtB,qBAAO,IAAIH,MAAK,KAAK,KAAK,IAAI,MAAM,IAAI,EAAE;AAC5C,mBAAO,IAAIA,MAAK,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,EAAE;AAAA,UAClD;AAAA,UAEA,UAAU,SAAS,SAAS,GAAG,GAAG,GAAG;AACnC,oBAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAAA,UAC3D;AAAA,QACF,CAAC;AAID,QAAAG,OAAM,OAAOA,OAAM,SAAS;AAAA,UAC1B,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG;AACzB,gBAAI,KAAK;AAAG,qBAAO;AAEnB,mBAAQ,KAAK,KAAK,KAAK,IAAK,IACzB,IAAIH,MAAK,KACRA,MAAK,IAAIA,MAAK,IAAI,IAAI,GAAG,CAAC,IAChBA,MAAK,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI;AAAA,UAChD;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG;AACzB,gBAAI,IAAI;AACN,qBAAO;AAAA,qBACA,IAAI;AACX,qBAAQ,IAAIA,MAAK,KAAMA,MAAK,KAAKA,MAAK,MAAM,IAAI,MAAI,IAAI,EAAE,CAAC;AAC7D,mBAAO;AAAA,UACT;AAAA,UAEA,KAAK,SAAS,GAAG,GAAG,GAAG;AACrB,mBAAO,KAAK,MAAM,MAAMA,MAAK,IAAIA,MAAK,KAAK,CAAC,MAAM,IAAI;AAAA,UACxD;AAAA,UAEA,MAAM,SAAS,KAAK,GAAG,GAAG;AACxB,gBAAI,KAAK;AAAG,qBAAO;AACnB,oBAAQ,IAAI,KAAK;AAAA,UACnB;AAAA,UAEA,QAAQ,SAAS,OAAO,GAAG,GAAG;AAC5B,gBAAI,KAAK;AAAG,qBAAO;AACnB,oBAAQ,IAAI,KAAK;AAAA,UACnB;AAAA,UAEA,MAAM,SAAS,OAAe;AAC5B,kBAAM,IAAI,MAAM,6BAA6B;AAAA,UAC/C;AAAA,UAEA,QAAQ,SAAS,OAAO,GAAG,GAAG;AAC5B,oBAAS,IAAI,KAAK,KAAO,IAAI,KAAK,IAChCA,MAAK,IAAI,IAAIA,MAAK,KAAKG,OAAM,QAAQ,OAAO,GAAG,CAAC,CAAC;AAAA,UACrD;AAAA,UAEA,UAAU,SAAS,SAAS,GAAG,GAAG;AAChC,gBAAI,KAAK;AAAG,qBAAO;AACnB,mBAAOH,MAAK,IAAI,IAAI,GAAG,CAAC,IAAI;AAAA,UAC9B;AAAA,QACF,CAAC;AAGD,iBAAS,YAAY,GAAG;AAAE,iBAAO,IAAIA,MAAK,IAAI,CAAC;AAAA,QAAG;AAElD,QAAAG,OAAM,OAAOA,OAAM,SAAS;AAAA,UAC1B,KAAK,SAAS,IAAI,GAAG,IAAI,GAAG;AAC1B,mBAAQ,KAAK,IAAK,IAAKH,MAAK,IAAI,CAACA,MAAK,IAAI,IAAI,EAAE,IAAI,CAAC,KAAM,IAAI;AAAA,UACjE;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,IAAI,GAAG;AAC1B,gBAAI,KAAK,GAAG;AAAE,qBAAO;AAAA,YAAG;AAExB,gBAAG,IAAI,IAAI;AACT,qBAAO,MAAMA,MAAK,KAAK,IAAI,MAAM,CAAC;AAAA,YACpC,OAAO;AACL,qBAAO,IAAI,MAAMA,MAAK,IAAI,EAAG,IAAI,MAAM,CAAC;AAAA,YAC1C;AAAA,UACF;AAAA,UAEA,MAAM,SAAS,IAAW;AACxB,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,IAAW;AAC1B,mBAAO;AAAA,UACT;AAAA,UAEA,MAAM,SAAS,IAAW;AACxB,mBAAO;AAAA,UACT;AAAA,UAEA,UAAU,SAAS,IAAI,GAAG;AACxB,mBAAO,IAAI,IAAI;AAAA,UACjB;AAAA,UAEA,QAAQ,SAAS,OAAO,IAAI,GAAG;AAC7B,gBAAI,IAAIG,OAAM,WAAW,IAAI;AAE7B,mBAAO,KAAM,IAAI,YAAY,CAAC,IAAIH,MAAK,IAAI,IAAK,IAAIA,MAAK,IAAI,CAAC,CAAE;AAAA,UAClE;AAAA,QACF,CAAC;AAED,iBAAS,WAAW,GAAG,IAAI,IAAI;AAC7B,cAAI,OAAO;AACX,cAAI,QAAQ;AAEZ,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAO;AACX,cAAI,OAAO;AACX,cAAI,SAAS;AACb,cAAI,SAAS;AACb,cAAI,OAAO;AAAA,YACT;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,cAAI,OAAO;AAAA,YACT;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAEA,cAAI,OAAO,IAAI;AAKf,cAAI,QAAQ;AACV,mBAAO;AAKT,cAAI,OAAO,IAAIG,OAAM,OAAO,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI;AAEpD,cAAI,QAAQH,MAAK,IAAI,KAAK,EAAE;AAC1B,mBAAOA,MAAK,IAAI,MAAM,EAAE;AAAA;AAExB,mBAAO;AAKT,cAAI;AACJ,cAAI,IAAI;AACN,oBAAQ;AAAA;AAER,oBAAQ;AAUV,cAAI,MAAM;AACV,cAAI,QAAQ,KAAK,QAAQ;AACzB,cAAI,MAAM,MAAM;AAChB,cAAI,SAAS;AAIb,cAAI,MAAM,KAAK;AACf,mBAAS,KAAK,GAAG,MAAM,OAAO,MAAM;AAClC,gBAAI,QAAQ;AACZ,gBAAI,IAAI,OAAO,MAAM;AAIrB,gBAAI,IAAI,OAAO,MAAM;AAErB,qBAAS,KAAK,GAAG,MAAM,MAAM,MAAM;AACjC,kBAAI,GAAG;AACP,kBAAI,QAAQ,IAAI;AACd,oBAAK,OAAO,KAAM;AAClB,qBAAK,KAAK,IAAE,CAAC;AAAA,cACf,OAAO;AACL,oBAAI;AACJ,qBAAK,CAAC,KAAK,IAAE,CAAC;AAAA,cAChB;AACA,kBAAI,IAAI,IAAI;AACZ,kBAAI,KAAK,IAAI;AAKb,kBAAI,QAAQ,KAAK;AACjB,kBAAI,QAAQ;AACV;AAEF,kBAAI,QAAQ,IAAIG,OAAM,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAC/C,kBAAI,SAAQ,IAAIA,OAAM,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAK/C,kBAAI,SAAU,QAAQ,MAAQ,SAAS;AACvC,kBAAI,UAAUH,MAAK,IAAI,KAAK,GAAG,GAAG;AAChC,yBAAU,KAAK,IAAE,CAAC,IAAIA,MAAK,IAAI,EAAE,MAAM,MAAM,IAAKA,MAAK,IAAI,QAAQ,GAAG;AACtE,yBAAS;AAAA,cACX;AAAA,YACF;AACA,qBAAY,IAAM,IAAK,KAAMA,MAAK,KAAK,IAAIA,MAAK,EAAE;AAClD,sBAAU;AACV,kBAAM;AACN,mBAAO;AAAA,UACT;AAGA,kBAAQ;AACR,cAAI,QAAQA,MAAK,IAAI,KAAK,EAAE;AAC1B,mBAAO;AAET,iBAAOA,MAAK,IAAI,MAAM,EAAE;AACxB,cAAI,QAAQ;AACV,mBAAO;AACT,iBAAO;AAAA,QACT;AAEA,iBAAS,UAAU,GAAG,GAAG,GAAG;AAC1B,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,OAAO;AAEX,cAAI,KAAK,MAAM,MAAM;AACrB,cAAI,KAAKA,MAAK,KAAKA,MAAK,IAAI,KAAO,KAAK,GAAG,CAAC;AAC5C,cAAI,IAAI,SAAU,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,UACnD,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AACrD,cAAI,IAAI;AAAM,kBAAM,IAAI,IAAI,IAAI,KAAK,IAAI;AACzC,cAAI,IAAI,KAAK,KAAK;AAClB,cAAI,IAAI;AAAM,iBAAK,CAAC,KAAK,IAAI,KAAK,IAAI;AACtC,iBAAO,KAAK,IAAIA,MAAK,IAAI,IAAI,CAAG,IAAI;AAAA,QACtC;AAEA,QAAAG,OAAM,OAAOA,OAAM,OAAO;AAAA,UACxB,KAAK,SAAS,IAAI,GAAG,QAAQ,IAAI;AAE/B,gBAAI,KAAK;AACT,gBAAI,KAAK;AAET,gBAAI,QAAQ;AACZ,gBAAI,SAAS;AAEb,gBAAI,OAAO;AACX,gBAAI,OAAO;AACX,gBAAI,OAAQ;AACZ,gBAAI,QAAQ;AACZ,gBAAI,QAAQ;AACZ,gBAAI,QAAQ;AACZ,gBAAI,QAAQ;AACZ,gBAAI,QAAQ;AACZ,gBAAI,QAAQ;AACZ,gBAAI,QAAQ;AACZ,gBAAI,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AACA,gBAAI,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAEA,gBAAI,KAAK;AACP,qBAAO;AAKT,gBAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAAG,qBAAO;AAEvC,gBAAI,CAAC,OAAO,SAAS,CAAC;AACpB,qBAAO;AAET,gBAAI,KAAK;AACP,qBAAO,WAAW,GAAG,IAAI,EAAE;AAI7B,gBAAI,KAAK,KAAK;AACd,gBAAI,OAAS,KAAKH,MAAK,IAAI,EAAE,IAAM,KAAKA,MAAK,IAAI,CAAC,IAAMG,OAAM,QAAQ,EAAE;AACxE,gBAAI,MAAM,KAAK;AAMf,gBAAI,MAAM,KAAK;AACf,gBAAI;AACJ,gBAAS,MAAM;AAAO,qBAAO;AAAA,qBACpB,MAAM;AAAO,qBAAO;AAAA,qBACpB,MAAM;AAAO,qBAAO;AAAA;AACP,qBAAO;AAE7B,oBAAQH,MAAK,IAAI,IAAI;AAIrB,gBAAI,MAAM;AAEV,qBAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,kBAAI,QAAQ;AAKZ,kBAAI,QAAQ,IAAI,IAAI,KAAK;AAEzB,uBAAS,KAAK,GAAG,MAAM,OAAO,MAAM;AAClC,oBAAI,GAAG;AACP,oBAAI,SAAS,IAAI;AACf,sBAAI,KAAK,SAAS;AAClB,uBAAM,OAAQ,MAAMA,MAAK,IAAI,OAAQ,MAAM,CAAC,IAAI,IAAK,KAC5C,MAAM,CAAC,IAAI,OAAQ,QAAQ;AAAA,gBACtC,OAAO;AACL,sBAAI,KAAK;AACT,uBAAM,OAAQ,MAAMA,MAAK,IAAI,OAAQ,MAAM,CAAC,IAAI,IAAK,KAC5C,MAAM,CAAC,IAAI,OAAQ,QAAQ;AAAA,gBACtC;AAGA,oBAAI;AACJ,oBAAI,MAAM,MAAM;AACd,sBAAI,SAAS,IAAI;AACf,2BAAO,IAAIA,MAAK,MAAO,MAAM,CAAC,IAAI,OAAQ,QAAQ,GAAG;AAAA,kBACvD,OAAO;AACL,2BAAO,IAAIA,MAAK,MAAO,EAAE,MAAM,CAAC,IAAI,QAAS,QAAQ,GAAG;AAAA,kBAC1D;AAIA,sBAAI,OAAO,WAAW,MAAM,IAAI,EAAE;AAClC,sBAAI,SAAU,OAAO,MAAM,CAAC,IAAKA,MAAK,IAAI,EAAE;AAC5C,2BAAS;AAAA,gBACX;AAAA,cAGF;AAKA,kBAAI,IAAI,QAAQ,KAAO,SAAS;AAC9B;AAKF,qBAAO;AAAA,YACT;AAEA,gBAAI,QAAQ,MAAM;AAChB,oBAAM,IAAI,MAAM,8BAA8B;AAAA,YAChD;AACA,gBAAI,MAAM;AACR,oBAAM;AACR,mBAAO;AAAA,UACT;AAAA,UAEA,KAAK,SAAS,GAAG,QAAQ,IAAI;AAE3B,gBAAI,KAAK;AACT,gBAAI,KAAK;AAET,gBAAI,MAAM;AACV,gBAAI,UAAU;AAGd,gBAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAAG,qBAAO;AAEvC,gBAAI,IAAI,KAAK,IAAI;AAAG,qBAAO;AAC3B,gBAAI,MAAM;AAAG,qBAAO;AACpB,gBAAI,MAAM;AAAG,qBAAO;AAIpB,gBAAI,KAAK,UAAU,GAAG,IAAI,EAAE;AAI5B,gBAAI,QAAQG,OAAM,MAAM,IAAI,IAAI,QAAQ,EAAE,IAAI;AAO9C,gBAAI;AACJ,gBAAI,QAAQ;AACV,mBAAKH,MAAK,IAAI,GAAK,KAAK,CAAG;AAAA;AAE3B,mBAAK,KAAK;AACZ,gBAAI,QAAQG,OAAM,MAAM,IAAI,IAAI,QAAQ,EAAE,IAAI;AAI9C,gBAAI;AACJ,qBAAQ,OAAO,GAAG,OAAO,SAAS,QAAQ;AACxC,oBAAM,KAAO,SAAS,KAAK,OAAQ,QAAQ;AAC3C,sBAAQ;AAIR,mBAAK;AACL,kBAAI,MAAM,GAAK;AACb,sBAAM;AACN,wBAAQ,CAAC;AAAA,cACX;AAGA,sBAAQA,OAAM,MAAM,IAAI,KAAK,QAAQ,EAAE,IAAI;AAC3C,mBAAK;AAKL,kBAAI,OAAOH,MAAK,IAAI,KAAK,EAAE;AAC3B,kBAAI,OAAO;AACT,uBAAO;AAAA,YACX;AAEA,kBAAM,IAAI,MAAM,8BAA8B;AAAA,UAChD;AAAA,QACF,CAAC;AAAA,MAED,GAAE,OAAO,IAAI;AAIb,OAAC,SAASG,QAAOH,OAAM;AAEvB,YAAI,OAAO,MAAM,UAAU;AAC3B,YAAI,UAAUG,OAAM,MAAM;AAE1B,iBAAS,SAAS,KAAK;AACrB,iBAAO,QAAQ,GAAG,KAAK,eAAeA;AAAA,QACxC;AAEA,QAAAA,OAAM,OAAO;AAAA;AAAA,UAGX,KAAK,SAAS,IAAI,KAAK,KAAK;AAE1B,gBAAI,SAAS,GAAG,GAAG;AACjB,kBAAI,CAAC,SAAS,IAAI,CAAC,CAAC;AAAG,sBAAM,CAAE,GAAI;AACnC,qBAAOA,OAAM,IAAI,KAAK,SAAS,OAAO,KAAK,KAAK;AAC9C,uBAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAAA,cAC7B,CAAC;AAAA,YACH;AACA,mBAAOA,OAAM,IAAI,KAAK,SAAS,OAAO;AAAE,qBAAO,QAAQ;AAAA,YAAK,CAAC;AAAA,UAC/D;AAAA;AAAA,UAGA,UAAU,SAAS,SAAS,KAAK,KAAK;AAEpC,gBAAI,SAAS,GAAG,GAAG;AACjB,kBAAI,CAAC,SAAS,IAAI,CAAC,CAAC;AAAG,sBAAM,CAAE,GAAI;AACnC,qBAAOA,OAAM,IAAI,KAAK,SAAS,OAAO,KAAK,KAAK;AAC9C,uBAAO,QAAQ,IAAI,GAAG,EAAE,GAAG,KAAK;AAAA,cAClC,CAAC;AAAA,YACH;AACA,mBAAOA,OAAM,IAAI,KAAK,SAAS,OAAO;AAAE,qBAAO,QAAQ;AAAA,YAAK,CAAC;AAAA,UAC/D;AAAA;AAAA,UAGA,QAAQ,SAAS,OAAO,KAAK,KAAK;AAChC,gBAAI,SAAS,GAAG,GAAG;AACjB,kBAAI,CAAC,SAAS,IAAI,CAAC,CAAC;AAAG,sBAAM,CAAE,GAAI;AACnC,qBAAOA,OAAM,SAAS,KAAKA,OAAM,IAAI,GAAG,CAAC;AAAA,YAC3C;AACA,mBAAOA,OAAM,IAAI,KAAK,SAAS,OAAO;AAAE,qBAAO,QAAQ;AAAA,YAAK,CAAC;AAAA,UAC/D;AAAA;AAAA,UAGA,UAAU,SAAS,SAAS,KAAK,KAAK;AACpC,gBAAI,KAAK,KAAK,UAAU,KAAK,MAAM,MAAM,KAAK;AAE9C,gBAAI,IAAI,WAAW,UAAa,IAAI,WAAW,QAAW;AACxD,qBAAO,MAAM;AAAA,YACf;AACA,mBAAO,IAAI,QACX,OAAO,IAAI,CAAC,EAAE,QACd,MAAMA,OAAM,MAAM,MAAM,WAAY,SAAS,GAAG,IAAK,IAAI,CAAC,EAAE,SAAS,IAAI,GACzE,UAAU;AACV,gBAAI,SAAS,GAAG,GAAG;AACjB,qBAAO,UAAU,UAAU,WAAW;AACpC,qBAAK,MAAM,GAAG,MAAM,MAAM,OAAO;AAC/B,wBAAM;AACN,uBAAK,MAAM,GAAG,MAAM,MAAM;AAC1B,2BAAO,IAAI,GAAG,EAAE,GAAG,IAAI,IAAI,GAAG,EAAE,OAAO;AACvC,sBAAI,GAAG,EAAE,OAAO,IAAI;AAAA,gBACtB;AAAA,cACF;AACA,qBAAQ,SAAS,KAAK,YAAY,IAAK,IAAI,CAAC,EAAE,CAAC,IAAI;AAAA,YACrD;AACA,mBAAOA,OAAM,IAAI,KAAK,SAAS,OAAO;AAAE,qBAAO,QAAQ;AAAA,YAAK,CAAC;AAAA,UAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA,OAAM,SAAS,MAAM,GAAG,GAAG;AACzB,mBAAOA,OAAM,SAAS,EAAE,IAAI,SAAS,GAAE;AAAE,qBAAO,CAAC,CAAC;AAAA,YAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA,UAC7D;AAAA;AAAA,UAIA,KAAK,SAAS,IAAI,KAAK,KAAK;AAC1B,gBAAI,CAAC,SAAS,IAAI,CAAC,CAAC;AAAG,oBAAM,CAAE,GAAI;AACnC,gBAAI,CAAC,SAAS,IAAI,CAAC,CAAC;AAAG,oBAAM,CAAE,GAAI;AAEnC,gBAAI,OAAQ,IAAI,CAAC,EAAE,WAAW,KAAK,IAAI,WAAW,IAAKA,OAAM,UAAU,GAAG,IAAI,KAC9E,QAAS,IAAI,CAAC,EAAE,WAAW,KAAK,IAAI,WAAW,IAAKA,OAAM,UAAU,GAAG,IAAI,KAC3E,MAAM,CAAC,GACP,MAAM,GACN,OAAO,KAAK,QACZ,OAAO,KAAK,CAAC,EAAE,QACf,KAAK;AACL,mBAAO,MAAM,MAAM,OAAO;AACxB,kBAAI,GAAG,IAAI,CAAC;AACZ,oBAAM;AACN,mBAAK,MAAM,GAAG,MAAM,MAAM;AAC1B,uBAAO,KAAK,GAAG,EAAE,GAAG,IAAI,MAAM,GAAG,EAAE,GAAG;AACtC,kBAAI,GAAG,IAAI;AAAA,YACb;AACA,mBAAQ,IAAI,WAAW,IAAK,IAAI,CAAC,IAAI;AAAA,UACvC;AAAA;AAAA,UAGA,KAAK,SAAS,IAAI,KAAK,KAAK;AAC1B,mBAAOA,OAAM,IAAI,KAAK,SAAS,OAAO;AAAE,qBAAOH,MAAK,IAAI,OAAO,GAAG;AAAA,YAAG,CAAC;AAAA,UACxE;AAAA;AAAA,UAGA,KAAK,SAAS,IAAI,KAAK;AACrB,mBAAOG,OAAM,IAAI,KAAK,SAAS,OAAO;AAAE,qBAAOH,MAAK,IAAI,KAAK;AAAA,YAAG,CAAC;AAAA,UACnE;AAAA;AAAA,UAGA,KAAK,SAAS,IAAI,KAAK;AACrB,mBAAOG,OAAM,IAAI,KAAK,SAAS,OAAO;AAAE,qBAAOH,MAAK,IAAI,KAAK;AAAA,YAAG,CAAC;AAAA,UACnE;AAAA;AAAA,UAGA,KAAK,SAAS,IAAI,KAAK;AACrB,mBAAOG,OAAM,IAAI,KAAK,SAAS,OAAO;AAAE,qBAAOH,MAAK,IAAI,KAAK;AAAA,YAAG,CAAC;AAAA,UACnE;AAAA;AAAA;AAAA,UAIA,MAAM,SAAS,KAAK,KAAK,GAAG;AAC1B,gBAAI,QAAQ,GACZ,IAAI;AAEJ,gBAAI,MAAM,CAAC;AAAG,kBAAI;AAElB,gBAAI,SAAS,IAAI,CAAC,CAAC;AAAG,oBAAM,IAAI,CAAC;AAEjC,mBAAO,IAAI,IAAI,QAAQ,KAAK;AAC1B,uBAASA,MAAK,IAAIA,MAAK,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC;AAAA,YACvC;AACA,mBAAOA,MAAK,IAAI,OAAO,IAAI,CAAC;AAAA,UAC9B;AAAA;AAAA;AAAA,UAIA,OAAO,SAAS,MAAM,KAAK,KAAK;AAC9B,mBAAOA,MAAK,KAAKG,OAAM,IAAI,KAAK,GAAG,KAAKA,OAAM,KAAK,GAAG,IAAIA,OAAM,KAAK,GAAG,EAAE;AAAA,UAC5E;AAAA;AAAA;AAAA,UAIA,KAAK,SAAS,IAAI,GAAG,GAAG;AACtB,gBAAI,SAAS,CAAC;AACd,gBAAI;AACJ,iBAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC7B,qBAAO,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC;AAAA,YAC1B;AACA,iBAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,mBAAK,MAAM,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,YAC5B;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA,UAKA,KAAK,SAAS,IAAI,GAAG;AACnB,gBAAI,OAAO,EAAE;AACb,gBAAI,OAAO,EAAE,CAAC,EAAE;AAChB,gBAAI,IAAIA,OAAM,SAAS,MAAM,IAAI;AACjC,gBAAI,IAAIA,OAAM,aAAa,GAAG,CAAC;AAC/B,gBAAI,SAAS,CAAC;AACd,gBAAI,IAAI;AACR,gBAAI;AAGJ,mBAAO,IAAI,MAAM,KAAK;AACpB,qBAAO,CAAC,IAAI,CAAC;AACb,mBAAK,IAAI,MAAM,IAAI,EAAE,CAAC,EAAE,QAAQ;AAC9B,uBAAO,CAAC,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,YAChC;AACA,mBAAO;AAAA,UACT;AAAA;AAAA,UAGA,KAAK,SAAS,IAAI,GAAG;AACnB,gBAAI,EAAE,WAAW,GAAG;AAClB,qBAAO,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,YAC7C;AAEA,gBAAI,cAAc;AAClB,qBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAEjC,kBAAI,YAAY,CAAC;AACjB,uBAAS,MAAM,GAAG,MAAM,EAAE,QAAQ,OAAO;AACvC,0BAAU,MAAM,CAAC,IAAI,CAAC;AACtB,yBAAS,MAAM,GAAG,MAAM,EAAE,QAAQ,OAAO;AACvC,sBAAI,MAAM,GAAG;AACX,8BAAU,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG;AAAA,kBACtC,WAAW,MAAM,GAAG;AAClB,8BAAU,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG;AAAA,kBAC1C;AAAA,gBACF;AAAA,cACF;AAGA,kBAAI,OAAO,IAAI,IAAI,KAAK;AACxB,6BAAe,IAAI,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,YAC5C;AAEA,mBAAO;AAAA,UACT;AAAA,UAEA,mBAAmB,SAAS,kBAAkB,GAAG,GAAG;AAClD,gBAAI,IAAI,GACR,IAAI,GACJ,IAAI,EAAE,QACN,IAAI,EAAE,CAAC,EAAE,QACT,SAAS,GACT,MAAM,GACN,IAAI,CAAC,GACL,MAAM,OAAO,MAAM;AACnB,gBAAIA,OAAM,IAAI,GAAG,CAAC;AAClB,mBAAO,EAAE,CAAC,EAAE;AACZ,iBAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AACrB,sBAAQ,EAAE,CAAC,EAAE,CAAC;AACd,kBAAI;AACJ,mBAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,oBAAI,QAAQH,MAAK,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG;AAC7B,0BAAQ,EAAE,CAAC,EAAE,CAAC;AACd,sBAAI;AAAA,gBACN;AAAA,cACF;AACA,kBAAI,KAAK,GAAG;AACV,qBAAI,IAAI,GAAG,IAAI,MAAM,KAAK;AACxB,yBAAO,EAAE,CAAC,EAAE,CAAC;AACb,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI;AAAA,gBACZ;AAAA,cACF;AACA,mBAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,yBAAS,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACzB,qBAAI,IAAI,GAAG,IAAI,MAAM,KAAK;AACxB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,EAAE,CAAC;AAAA,gBACrC;AAAA,cACF;AAAA,YACF;AACA,iBAAK,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,oBAAM;AACN,mBAAK,IAAI,IAAI,GAAG,KAAI,IAAI,GAAG,KAAK;AAC9B,sBAAM,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3B;AACA,gBAAE,CAAC,KAAI,EAAE,CAAC,EAAE,OAAO,CAAC,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC;AAAA,YACvC;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,cAAc,SAAS,aAAa,GAAG,GAAG;AACxC,gBAAI,IAAIG,OAAM,IAAI,GAAG,CAAC;AACtB,gBAAI,IAAI,EAAE;AACV,gBAAI,IAAI,EAAE,CAAC,EAAE;AACb,gBAAI,IAAI;AACR,gBAAI,GAAG,GAAG;AAEV,iBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,kBAAI,SAAS;AACb,mBAAK,KAAK,IAAE,GAAG,KAAK,GAAG,MAAM;AAC3B,oBAAIH,MAAK,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,IAAIA,MAAK,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;AAC5C,2BAAS;AAAA,cACb;AACA,kBAAI,MAAM,EAAE,CAAC;AACb,gBAAE,CAAC,IAAI,EAAE,MAAM;AACf,gBAAE,MAAM,IAAI;AACZ,mBAAK,KAAK,IAAE,GAAG,KAAK,GAAG,MAAM;AAC3B,oBAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACrB,qBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,oBAAE,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,gBACxB;AAAA,cACF;AAAA,YACF;AAEA,iBAAK,IAAI,IAAE,GAAG,KAAK,GAAG,KAAK;AACzB,kBAAI,EAAE,CAAC,EAAE,CAAC;AACV,mBAAK,KAAK,GAAG,KAAK,GAAG,MAAM;AACzB,qBAAK,IAAI,IAAE,GAAG,IAAI,IAAE,GAAG,KAAK;AAC1B,oBAAE,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI;AAAA,gBACnC;AAAA,cACF;AACA,gBAAE,CAAC,EAAE,CAAC,KAAK;AACX,mBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,kBAAE,CAAC,EAAE,CAAC,KAAK;AAAA,cACb;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAYA,aAAa,SAAS,YAAY,GAAG,GAAG;AACtC,gBAAI,OAAO,EAAE,CAAC,EAAE;AAChB,gBAAI,IAAIG,OAAM,MAAM,GAAG,IAAI,EAAE,CAAC;AAC9B,gBAAI;AACJ,gBAAI,cAAc;AAElB,gBAAI,EAAE,CAAC,EAAE,UAAU,QAAW;AAC5B,kBAAI,EAAE,IAAI,SAAS,GAAE;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAE,CAAC;AACpC,4BAAc;AAAA,YAChB;AAEA,YAAAA,OAAM,OAAO,OAAO,GAAG,IAAI,EAAE,EAAE,QAAQ,SAAS,GAAG;AACjD,sBAAQA,OAAM,OAAO,IAAI,GAAG,IAAI,EAAE,IAAI,SAAS,GAAG;AAChD,uBAAO,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,cACtB,CAAC;AACD,gBAAE,CAAC,KAAK,EAAE,CAAC,IAAIA,OAAM,IAAI,KAAK,KAAK,EAAE,CAAC,EAAE,CAAC;AAAA,YAC3C,CAAC;AAED,gBAAI;AACF,qBAAO,EAAE,IAAI,SAAS,GAAE;AAAE,uBAAO,CAAC,CAAC;AAAA,cAAE,CAAC;AACxC,mBAAO;AAAA,UACT;AAAA,UAEA,cAAc,SAAS,aAAa,GAAG,GAAG;AAExC,gBAAI,OAAO,EAAE,CAAC,EAAE;AAChB,gBAAI,IAAIA,OAAM,MAAM,GAAG,IAAI,EAAE,CAAC;AAC9B,gBAAI;AAEJ,gBAAI,cAAY;AAChB,gBAAI,EAAE,CAAC,EAAE,UAAU,QAAW;AAC5B,kBAAI,EAAE,IAAI,SAAS,GAAE;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAE,CAAC;AACpC,4BAAc;AAAA,YAChB;AAEA,YAAAA,OAAM,OAAO,IAAI,EAAE,QAAQ,SAAS,GAAG;AACrC,sBAAQA,OAAM,OAAO,CAAC,EAAE,IAAI,SAAS,GAAG;AACtC,uBAAO,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,cACtB,CAAC;AACD,gBAAE,CAAC,KAAK,EAAE,CAAC,IAAIA,OAAM,IAAI,KAAK,KAAK,EAAE,CAAC,EAAE,CAAC;AAAA,YAC3C,CAAC;AAED,gBAAI;AACF,qBAAO,EAAE,IAAI,SAAS,GAAE;AAAE,uBAAO,CAAC,CAAC;AAAA,cAAE,CAAC;AACxC,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA,IAAI,SAAS,GAAG,GAAG;AACjB,gBAAI,OAAO,EAAE;AAEb,gBAAI,IAAIA,OAAM,SAAS,IAAI;AAC3B,gBAAI,IAAIA,OAAM,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM;AACzC,gBAAI;AACJ,YAAAA,OAAM,OAAO,IAAI,EAAE,QAAQ,SAAS,GAAG;AACrC,gBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,YAClB,CAAC;AACD,YAAAA,OAAM,OAAO,GAAG,IAAI,EAAE,QAAQ,SAAS,GAAG;AACxC,cAAAA,OAAM,OAAO,CAAC,EAAE,QAAQ,SAAS,GAAG;AAClC,wBAAQA,OAAM,OAAO,CAAC,EAAE,IAAI,SAAS,IAAI;AACvC,yBAAO,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AAAA,gBAC3B,CAAC;AACD,kBAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAIA,OAAM,IAAI,KAAK,KAAK,EAAE,CAAC,EAAE,CAAC;AAAA,cACjD,CAAC;AACD,cAAAA,OAAM,OAAO,GAAG,IAAI,EAAE,QAAQ,SAAS,GAAG;AACxC,wBAAQA,OAAM,OAAO,CAAC,EAAE,IAAI,SAAS,IAAI;AACvC,yBAAO,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AAAA,gBAC3B,CAAC;AACD,kBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,MAAM,EAAE,CAAC,IAAIA,OAAM,IAAI,KAAK;AAAA,cAChD,CAAC;AAAA,YACH,CAAC;AACD,mBAAO,CAAC,GAAG,CAAC;AAAA,UACd;AAAA;AAAA;AAAA;AAAA,UAKA,UAAU,SAAS,SAAS,GAAG;AAC7B,gBAAI,OAAO,EAAE;AACb,gBAAI,IAAIA,OAAM,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM;AACzC,gBAAI;AACJ,YAAAA,OAAM,OAAO,IAAI,EAAE,QAAQ,SAAS,GAAG;AACrC,sBAAQA,OAAM,OAAO,CAAC,EAAE,IAAI,SAAS,GAAG;AACtC,uBAAOH,MAAK,IAAI,EAAE,CAAC,EAAE,CAAC,GAAE,CAAC;AAAA,cAC3B,CAAC;AACD,gBAAE,CAAC,EAAE,CAAC,IAAIA,MAAK,KAAK,EAAE,CAAC,EAAE,CAAC,IAAIG,OAAM,IAAI,KAAK,CAAC;AAC9C,cAAAA,OAAM,OAAO,IAAI,GAAG,IAAI,EAAE,QAAQ,SAAS,GAAG;AAC5C,wBAAQA,OAAM,OAAO,CAAC,EAAE,IAAI,SAAS,GAAG;AACtC,yBAAO,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,gBACzB,CAAC;AACD,kBAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAIA,OAAM,IAAI,KAAK,KAAK,EAAE,CAAC,EAAE,CAAC;AAAA,cACjD,CAAC;AAAA,YACH,CAAC;AACD,mBAAO;AAAA,UACT;AAAA,UAGA,cAAc,SAAS,aAAa,GAAG,GAAG,GAAG,GAAG;AAC9C,gBAAI,IAAI;AACR,gBAAI,IAAI;AACR,gBAAI,IAAI,EAAE;AACV,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,GAAG,GAAG;AACd,mBAAO,IAAI,GAAG,KAAK;AACjB,gBAAE,CAAC,IAAI,CAAC;AACR,gBAAE,CAAC,IAAI,CAAC;AACR,gBAAE,CAAC,IAAI,CAAC;AACR,mBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,oBAAI,IAAI,GAAG;AACT,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,gBACtB,WAAW,IAAI,GAAG;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,gBACtB,OAAO;AACL,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,gBACtB;AAAA,cACF;AAAA,YACF;AACA,gBAAIA,OAAM,SAASA,OAAM,SAASA,OAAM,IAAI,CAAC,GAAGA,OAAM,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE;AACpE,gBAAIA,OAAM,SAASA,OAAM,IAAI,CAAC,GAAG,CAAC;AAClC,iBAAK;AACL,iBAAKA,OAAM,IAAIA,OAAM,SAAS,GAAG,CAAC,GAAG,CAAC;AACtC,gBAAI;AACJ,mBAAOH,MAAK,IAAIG,OAAM,KAAKA,OAAM,SAAS,IAAG,EAAE,CAAC,CAAC,IAAI,GAAG;AACtD,mBAAK;AACL,mBAAKA,OAAM,IAAIA,OAAM,SAAS,GAAG,EAAE,GAAG,CAAC;AACvC;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,cAAc,SAAS,aAAa,GAAG,GAAG,GAAG,GAAG;AAC9C,gBAAI,IAAI;AACR,gBAAI,IAAI,EAAE;AACV,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,GAAG,IAAI,GAAG,GAAG;AACjB,mBAAO,IAAI,GAAG,KAAK;AACjB,gBAAE,CAAC,IAAI,CAAC;AACR,gBAAE,CAAC,IAAI,CAAC;AACR,gBAAE,CAAC,IAAI,CAAC;AACR,mBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,oBAAI,IAAI,GAAG;AACT,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,gBACtB,WAAW,IAAI,GAAG;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,gBACtB,OAAO;AACL,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,gBACtB;AAAA,cACF;AAAA,YACF;AACA,gBAAIA,OAAM,SAASA,OAAM,SAASA,OAAM,IAAIA,OAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE;AACpE,gBAAIA,OAAM,SAASA,OAAM,IAAIA,OAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AAChD,iBAAK;AACL,iBAAKA,OAAM,IAAIA,OAAM,SAAS,GAAG,CAAC,GAAG,CAAC;AACtC,gBAAI;AACJ,mBAAOH,MAAK,IAAIG,OAAM,KAAKA,OAAM,SAAS,IAAI,EAAE,CAAC,CAAC,IAAI,GAAG;AACvD,mBAAK;AACL,mBAAKA,OAAM,IAAIA,OAAM,SAAS,GAAG,EAAE,GAAG,CAAC;AACvC,kBAAI,IAAI;AAAA,YACV;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;AAC/B,gBAAI,IAAI;AACR,gBAAI,IAAI,EAAE;AACV,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,GAAG,IAAI,GAAG,GAAG;AACjB,mBAAO,IAAI,GAAG,KAAK;AACjB,gBAAE,CAAC,IAAI,CAAC;AACR,gBAAE,CAAC,IAAI,CAAC;AACR,gBAAE,CAAC,IAAI,CAAC;AACR,mBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,oBAAI,IAAI,GAAG;AACT,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,gBACtB,WAAW,IAAI,GAAG;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,gBACtB,OAAO;AACL,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAChB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI;AAAA,gBACtB;AAAA,cACF;AAAA,YACF;AACA,gBAAIA,OAAM;AAAA,cAASA,OAAM,IAAIA,OAAM,IAAI,GAAGA,OAAM,SAAS,GAAG,CAAC,CAAC,CAAC;AAAA,cAC5CA,OAAM;AAAA,gBAASA,OAAM,SAAS,GAAG,IAAI,CAAC;AAAA,gBACvBA,OAAM,SAAS,GAAG,CAAC;AAAA,cAAC;AAAA,YAAC;AACvD,gBAAIA,OAAM,SAASA,OAAM,SAASA,OAAM,IAAIA,OAAM;AAAA,cAAI;AAAA,cAClDA,OAAM,SAAS,GAAG,CAAC;AAAA,YAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AACjC,iBAAK;AACL,iBAAKA,OAAM,IAAIA,OAAM,SAAS,GAAG,CAAC,GAAG,CAAC;AACtC,gBAAI;AACJ,mBAAOH,MAAK,IAAIG,OAAM,KAAKA,OAAM,SAAS,IAAI,EAAE,CAAC,CAAC,IAAI,GAAG;AACvD,mBAAK;AACL,mBAAKA,OAAM,IAAIA,OAAM,SAAS,GAAG,EAAE,GAAG,CAAC;AACvC;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,aAAa,SAAS,YAAY,GAAG;AACnC,gBAAI,IAAI,EAAE;AACV,gBAAI,IAAI,EAAE,CAAC,EAAE;AACb,gBAAI,IAAI;AACR,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,OAAO,GAAG,GAAG,GAAG;AACpB,mBAAO,IAAI,IAAI,GAAG,KAAK;AACrB,sBAAQ;AACR,mBAAK,IAAI,IAAI,GAAG,IAAI,GAAG;AACvB,yBAAU,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAC1B,uBAAU,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,IAAK,KAAK;AAClC,sBAAQ,SAASH,MAAK,KAAK,KAAK;AAChC,kBAAIA,MAAK,MAAQ,QAAQ,QAAS,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,SAAS,CAAE;AAC3D,kBAAIG,OAAM,MAAM,GAAG,CAAC;AACpB,gBAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,UAAU,IAAI;AAC3C,mBAAK,IAAI,IAAI,GAAG,IAAI,GAAG;AAAK,kBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,KAAK,IAAI;AACrD,kBAAIA,OAAM;AAAA,gBAASA,OAAM,SAAS,GAAG,CAAC;AAAA,gBAClCA,OAAM,SAASA,OAAM,SAAS,GAAGA,OAAM,UAAU,CAAC,CAAC,GAAG,CAAC;AAAA,cAAC;AAC5D,kBAAIA,OAAM,SAAS,GAAGA,OAAM,SAAS,GAAG,CAAC,CAAC;AAAA,YAC5C;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA,UAKA,IAAK,WAAW;AAOd,gBAAI,MAAQA,OAAM;AAClB,gBAAI,QAAQA,OAAM;AAElB,qBAAS,IAAI,GAAG;AAId,kBAAI,IAAI,EAAE;AACV,kBAAI,IAAI,EAAE,CAAC,EAAE;AAEb,kBAAI,IAAIA,OAAM,MAAM,GAAG,CAAC;AACxB,kBAAIA,OAAM,KAAK,CAAC;AAEhB,kBAAI,GAAE,GAAE;AACR,mBAAI,IAAI,GAAG,IAAI,GAAG,KAAI;AACpB,kBAAE,CAAC,EAAE,CAAC,IAAIH,MAAK,KAAK,IAAI,MAAM,CAAC,EAAE,IAAI,SAASI,IAAE;AAC9C,yBAAO,EAAEA,EAAC,EAAE,CAAC,IAAI,EAAEA,EAAC,EAAE,CAAC;AAAA,gBACzB,CAAC,CAAC,CAAC;AACH,qBAAI,IAAI,GAAG,IAAI,GAAG,KAAI;AACpB,oBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,gBAC5B;AACA,qBAAI,IAAI,IAAE,GAAG,IAAI,GAAG,KAAI;AACtB,oBAAE,CAAC,EAAE,CAAC,IAAI,IAAI,MAAM,CAAC,EAAE,IAAI,SAASA,IAAE;AACpC,2BAAO,EAAEA,EAAC,EAAE,CAAC,IAAI,EAAEA,EAAC,EAAE,CAAC;AAAA,kBACzB,CAAC,CAAC;AACF,uBAAI,IAAI,GAAG,IAAI,GAAG,KAAI;AACpB,sBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAE,EAAE,CAAC,EAAE,CAAC;AAAA,kBACpC;AAAA,gBACF;AAAA,cACF;AACA,qBAAO,CAAC,GAAG,CAAC;AAAA,YACd;AAEA,mBAAO;AAAA,UACT,EAAE;AAAA,UAEF,OAAQ,WAAW;AAIjB,qBAAS,IAAI,GAAG;AACd,kBAAID,OAAM,KAAK,CAAC;AAChB,kBAAI,OAAO,EAAE;AACb,kBAAI,IAAIA,OAAM,SAAS,IAAI;AAC3B,cAAAA,OAAM,OAAO,OAAO,GAAG,IAAI,EAAE,EAAE,QAAQ,SAAS,GAAG;AACjD,gBAAAA,OAAM;AAAA,kBACF;AAAA,kBAAG,EAAE,KAAK,EAAE;AAAA,kBAAGA,OAAM,OAAOA,OAAM,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,gBAAC;AACpE,gBAAAA,OAAM;AAAA,kBACF;AAAA,kBAAG,EAAE,KAAK,EAAE;AAAA,kBAAGA,OAAM,OAAOA,OAAM,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,gBAAC;AACpE,gBAAAA,OAAM,OAAO,CAAC,EAAE,QAAQ,SAAS,GAAG;AAClC,sBAAI,IAAIA,OAAM,SAAS,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE;AAClC,sBAAI,KAAKA,OAAM,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC;AAClC,sBAAI,MAAMA,OAAM,SAASA,OAAM,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;AACtD,kBAAAA,OAAM,YAAY,GAAG,EAAE,KAAK,EAAE,GAAGA,OAAM,IAAI,IAAI,GAAG,CAAC;AACnD,sBAAI,KAAKA,OAAM,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC;AAClC,sBAAI,MAAMA,OAAM,SAASA,OAAM,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;AACtD,kBAAAA,OAAM,YAAY,GAAG,EAAE,KAAK,EAAE,GAAGA,OAAM,IAAI,IAAI,GAAG,CAAC;AAAA,gBACrD,CAAC;AAAA,cACH,CAAC;AACD,qBAAO;AAAA,YACT;AAEA,qBAAS,SAAS,GAAG,GAAE;AACrB,kBAAI,aAAa;AACjB,kBAAI,EAAE,CAAC,EAAE,WAAW,QAAW;AAE7B,oBAAI,EAAE,IAAI,SAASS,IAAE;AAAE,yBAAO,CAACA,EAAC;AAAA,gBAAE,CAAC;AACnC,6BAAa;AAAA,cACf;AACA,kBAAI,KAAKT,OAAM,GAAG,CAAC;AACnB,kBAAI,IAAI,GAAG,CAAC;AACZ,kBAAI,IAAI,GAAG,CAAC;AACZ,kBAAI,QAAQ,EAAE,CAAC,EAAE;AACjB,kBAAI,KAAKA,OAAM,MAAM,GAAE,EAAC,KAAI,EAAC,KAAI,MAAK,EAAC,CAAC;AACxC,kBAAI,KAAKA,OAAM,MAAM,GAAE,EAAC,KAAI,EAAC,KAAI,MAAK,EAAC,CAAC;AACxC,kBAAI,KAAK,IAAI,EAAE;AACf,kBAAI,KAAKA,OAAM,UAAU,EAAE;AAE3B,kBAAG,GAAG,CAAC,EAAE,WAAW,QAAU;AAC5B,qBAAK,CAAC,EAAE;AAAA,cACV;AAEA,kBAAI,IAAIA,OAAM,SAASA,OAAM,SAAS,IAAI,EAAE,GAAG,CAAC;AAEhD,kBAAG,EAAE,WAAW,QAAU;AACxB,oBAAI,CAAC,CAAC,CAAC,CAAC;AAAA,cACV;AAGA,kBAAI;AACF,uBAAO,EAAE,IAAI,SAAS,GAAE;AAAE,yBAAO,EAAE,CAAC;AAAA,gBAAE,CAAC;AACzC,qBAAO;AAAA,YACT;AAEA,mBAAO;AAAA,UACT,EAAE;AAAA,UAEF,QAAQ,SAAS,OAAO,GAAG;AACzB,gBAAI,YAAY;AAChB,gBAAI,IAAI,EAAE;AACV,gBAAI,IAAIA,OAAM,SAAS,GAAG,CAAC;AAC3B,gBAAI,KAAK,CAAC;AACV,gBAAI,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,OAAO;AAEjC,mBAAO,cAAc,GAAG;AACtB,sBAAQ,EAAE,CAAC,EAAE,CAAC;AACd,kBAAI;AACJ,kBAAI;AACJ,mBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,qBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,sBAAI,KAAK,GAAG;AACV,wBAAI,QAAQH,MAAK,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG;AAC7B,8BAAQA,MAAK,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AACxB,0BAAI;AACJ,0BAAI;AAAA,oBACN;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AACA,kBAAI,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;AACpB,wBAAS,EAAE,CAAC,EAAE,CAAC,IAAI,IAAKA,MAAK,KAAK,IAAI,CAACA,MAAK,KAAK;AAAA;AAEjD,wBAAQA,MAAK,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI;AACzD,kBAAIG,OAAM,SAAS,GAAG,CAAC;AACvB,gBAAE,CAAC,EAAE,CAAC,IAAIH,MAAK,IAAI,KAAK;AACxB,gBAAE,CAAC,EAAE,CAAC,IAAI,CAACA,MAAK,IAAI,KAAK;AACzB,gBAAE,CAAC,EAAE,CAAC,IAAIA,MAAK,IAAI,KAAK;AACxB,gBAAE,CAAC,EAAE,CAAC,IAAIA,MAAK,IAAI,KAAK;AAExB,kBAAIG,OAAM,SAAS,GAAG,CAAC;AACvB,kBAAIA,OAAM,SAASA,OAAM,SAASA,OAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AACrD,kBAAI;AACJ,0BAAY;AACZ,mBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,qBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,sBAAI,KAAK,KAAKH,MAAK,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,MAAO;AACvC,gCAAY;AAAA,kBACd;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,iBAAK,IAAI,GAAG,IAAI,GAAG;AAAK,iBAAG,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAEvC,mBAAO,CAAC,GAAG,EAAE;AAAA,UACf;AAAA,UAEA,YAAY,SAAS,WAAW,GAAG,GAAG,GAAG,KAAK,KAAK,OAAO;AACxD,gBAAI,IAAI,IAAI,MAAM,IAAI;AACtB,gBAAI,UAAU,GAAG;AACf,qBAAO,OAAO,GAAG;AACf,qBAAK,IAAI,EAAE,KAAK,GAAG;AACnB,qBAAK,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;AAC5B,uBAAO,OAAO,KAAK,MAAM;AACzB,sBAAM;AACN,sBAAM,MAAM;AAAA,cACd;AAAA,YACF;AACA,gBAAI,UAAU,GAAG;AACf,qBAAO,OAAO,GAAG;AACf,qBAAK,IAAI,EAAE,KAAK,GAAG;AACnB,qBAAK,IAAI,EAAE,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC;AACpC,qBAAK,IAAI,EAAE,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC;AACpC,qBAAK,IAAI,EAAE,MAAK,GAAG,MAAM,EAAE;AAC3B,uBAAO,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM;AAC3C,sBAAM;AACN,sBAAM,MAAM;AAAA,cACd;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,SAAS,SAAS,QAAQ,GAAG,GAAG,GAAG,OAAO;AACxC,gBAAI,IAAI;AACR,gBAAI,KAAK,IAAI,KAAK;AAClB,gBAAI,IAAI,CAAC;AACT,gBAAI,KAAK,CAAC;AACV,gBAAI,IAAI,CAAC;AACT,gBAAI,GAAG,IAAI,GAAG,GAAG;AACjB,mBAAO,IAAI,QAAQ,GAAG;AACpB,kBAAI,EAAE,CAAC;AACP,mBAAK,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,GAAG;AAAK,kBAAE,CAAC,IAAI;AAClD,kBAAI,EAAE;AACN,mBAAK,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC1B,sBAAQ,IAAI,MAAO,IAAK,IAAI,KAAK,EAAE,EAAE,CAAC,CAAC;AAAA,cACzC;AACA,kBAAK,IAAI,KAAM,IAAI,EAAE,CAAC;AACtB,gBAAE,CAAC,IAAI;AACP,mBAAK;AACL;AAAA,YACF;AACA,iBAAK,EAAE;AACP,gBAAI;AACJ,mBAAO,OAAO,GAAG;AACf,mBAAK,IAAI,GAAG,IAAI,KAAK,GAAG;AACxB,mBAAG,CAAC,KAAMA,MAAK,IAAI,GAAG,CAAC,IAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,MAAMA,MAAK,IAAI,GAAG,CAAC,IAAI;AACjE,mBAAK,GAAG;AACR,kBAAI;AACJ,mBAAK,CAAC;AACN;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,YAAY,SAAS,WAAW,GAAG,GAAG,GAAG,GAAG;AAC1C,qBAAS,IAAIa,IAAGD,IAAG;AACjB,kBAAIR,KAAI;AACR,kBAAI,IAAIS,GAAE;AACV,kBAAI;AACJ,qBAAOT,KAAI,GAAGA;AACZ,oBAAIS,GAAET,EAAC,MAAMQ;AAAG,sBAAIR;AACtB,qBAAO;AAAA,YACT;AACA,gBAAI,QAAQJ,MAAK,IAAI,IAAI,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;AACzC,gBAAI,IAAI;AACR,gBAAI,IAAI,CAAC;AACT,gBAAI,KAAK,CAAC;AACV,gBAAI,IAAI,IAAI,GAAG,GAAG;AAClB,mBAAO,KAAK,OAAO;AACjB,mBAAK,IAAI,GAAG,IAAI,CAAC;AACjB,mBAAK,IAAI,GAAG,CAAC;AACb,gBAAE,CAAC,KAAK,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,KAAK,EAAE,MAAM,IAAI;AACnD,mBAAK;AACL;AAAA,YACF;AACA,gBAAI,EAAE;AACN,gBAAI;AACJ,mBAAO,KAAK,GAAG;AACb,mBAAK,IAAI,GAAG,IAAI,IAAI,GAAG;AACrB,mBAAG,CAAC,KAAMA,MAAK,IAAI,GAAG,CAAC,IAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,MAAMA,MAAK,IAAI,GAAG,CAAC,IAAI;AACnE,kBAAI,GAAG;AACP,kBAAI;AACJ,mBAAK,CAAC;AACN;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,SAAS,SAAS,QAAQ,GAAG,GAAG,GAAG,GAAG;AACpC,gBAAI,KAAK,IAAI,KAAK;AAClB,gBAAI,IAAI,EAAE,CAAC;AACX,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI;AACR,gBAAI,IAAI;AACR,gBAAI,IAAI;AACR,gBAAI;AACJ,mBAAO,KAAK,GAAG,IAAI,IAAI,GAAG;AACxB,gBAAE,CAAC,IAAI;AACT,gBAAI,EAAE;AACN,mBAAO,IAAI,IAAI,GAAG,KAAK;AACrB,oBAAO,IAAI,MAAM,IAAK,IAAI,KAAK,EAAE,EAAE,CAAC,CAAC;AAAA,YACvC;AACA,mBAAQ,IAAI,KAAM,IAAI,EAAE,CAAC;AAAA,UAC3B;AAAA,UAEA,SAAS,SAAS,QAAQ,GAAG,GAAG,IAAI,OAAO;AACzC,gBAAI,IAAI,EAAE;AACV,gBAAI,IAAI;AACR,gBAAI,IAAI;AACR,gBAAI,IAAI,CAAC;AACT,gBAAI,KAAK,CAAC;AACV,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI;AACJ,mBAAO,IAAI,GAAG,KAAK;AACjB,gBAAE,CAAC,IAAI;AACP,mBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,oBAAI,KAAK;AAAG,oBAAE,CAAC,MAAM,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,cAClD;AACA,iBAAG,CAAC,IAAI;AACR,mBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,oBAAI,KAAK;AAAG,qBAAG,CAAC,KAAK,KAAK,EAAG,CAAC,IAAI,EAAE,CAAC;AAAA,cACvC;AACA,gBAAE,CAAC,KAAK,IAAI,KAAK,QAAQ,EAAE,CAAC,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AACrD,gBAAE,CAAC,KAAK,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AACnC,mBAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC;AAAA,YACjC;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,UAAU,SAAS,SAAS,GAAG,GAAG,OAAO;AACvC,gBAAI,IAAI;AACR,gBAAI,IAAI;AACR,gBAAI,GAAG;AACP,gBAAI,IAAI,EAAE;AACV,mBAAO,IAAI,GAAG,KAAK;AACjB,kBAAI,EAAE,CAAC;AACP,mBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AAEtB,oBAAI,KAAK;AAAG,wBAAM,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,cAC/C;AAEA,mBAAK;AAAA,YACP;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,cAAc,SAAS,aAAa,GAAG,GAAG,OAAO;AAC/C,gBAAI,IAAI,EAAE;AACV,gBAAI,IAAI,GAAG;AACX,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,QAAQ,CAAC;AACb,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,mBAAO,IAAI,IAAI,GAAG;AAChB,gBAAE,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AACvB,kBAAM,CAAC,IAAI;AACX,iBAAK,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC1B,oBAAM,CAAC,IAAK,IAAI,EAAE,CAAC,KAAM,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,KAClC,IAAI,EAAE,IAAE,CAAC,KAAM,EAAE,CAAC,IAAI,EAAE,IAAE,CAAC;AAAA,YAClC;AACA,iBAAK,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC1B,gBAAE,CAAC,IAAI,CAAC;AACR,gBAAE,CAAC,IAAI,CAAC;AACR,gBAAE,CAAC,EAAE,IAAE,CAAC,IAAI,EAAE,IAAE,CAAC;AACjB,gBAAE,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AAC7B,gBAAE,CAAC,EAAE,IAAE,CAAC,IAAI,EAAE,CAAC;AACf,gBAAE,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC;AAAA,YACnB;AACA,gBAAIG,OAAM,SAASA,OAAM,IAAI,CAAC,GAAG,CAAC;AAClC,iBAAK,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC1B,gBAAE,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC,KAAK;AACvE,gBAAE,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC;AAAA,YAC3C;AACA,iBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,kBAAI,EAAE,CAAC,IAAI;AAAO;AAAA,YACpB;AACA,iBAAK;AACL,mBAAO,EAAE,CAAC,KAAK,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,IAAIA,OAAM,GAAG,QAAM,EAAE,CAAC,CAAC,IACrD,EAAE,CAAC,KAAK,QAAQ,EAAE,CAAC,KAAKA,OAAM,GAAG,QAAQ,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,UAC1D;AAAA,UAEA,kBAAkB,SAAS,mBAAmB;AAC5C,kBAAM,IAAI,MAAM,sCAAsC;AAAA,UACxD;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG;AACnB,gBAAI,IAAI,EAAE;AACV,gBAAI,IAAI,EAAE,CAAC,EAAE;AACb,gBAAI,IAAI;AACR,gBAAI,GAAG;AACP,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,SAAS,CAAC;AACd,gBAAI,QAAQ,CAAC;AACb,gBAAI,IAAI,CAAC;AACT,gBAAI,KAAK,CAAC;AACV,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,IAAI,CAAC;AACT,gBAAI,KAAK,CAAC;AACV,iBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,gBAAE,CAAC,IAAIA,OAAM,IAAI,EAAE,CAAC,CAAC,IAAI;AAAA,YAC3B;AACA,iBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,gBAAE,CAAC,IAAI,CAAC;AACR,mBAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AACrB,kBAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,cACzB;AAAA,YACF;AACA,gBAAIA,OAAM,UAAU,CAAC;AACrB,iBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,gBAAE,CAAC,IAAI,CAAC;AACR,mBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,kBAAE,CAAC,EAAE,CAAC,IAAKA,OAAM,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,KAAM,IAAI;AAAA,cAC/C;AAAA,YACF;AACA,qBAASA,OAAM,OAAO,CAAC;AACvB,gBAAI,OAAO,CAAC;AACZ,gBAAI,OAAO,CAAC;AACZ,iBAAKA,OAAM,UAAU,CAAC;AACtB,iBAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC7B,mBAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC7B,oBAAG,EAAE,CAAC,IAAI,EAAE,CAAC,GAAI;AACf,0BAAQ,EAAE,CAAC;AACX,oBAAE,CAAC,IAAI,EAAE,CAAC;AACV,oBAAE,CAAC,IAAI;AACP,0BAAQ,GAAG,CAAC;AACZ,qBAAG,CAAC,IAAI,GAAG,CAAC;AACZ,qBAAG,CAAC,IAAI;AAAA,gBACV;AAAA,cACF;AAAA,YACF;AACA,iBAAKA,OAAM,UAAU,CAAC;AACtB,iBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,gBAAE,CAAC,IAAI,CAAC;AACR,mBAAK,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAC9B,kBAAE,CAAC,EAAE,CAAC,IAAIA,OAAM,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA,cACtC;AAAA,YACF;AACA,mBAAO,CAAC,GAAG,GAAG,IAAI,CAAC;AAAA,UACrB;AAAA,QACF,CAAC;AAGD,SAAC,SAAS,OAAO;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,aAAC,SAAS,UAAU;AACzD,cAAAA,OAAM,GAAG,QAAQ,IAAI,SAAS,KAAK,MAAM;AACvC,oBAAI,UAAU;AAEd,oBAAI,MAAM;AACR,6BAAW,WAAW;AACpB,yBAAK,KAAK,SAASA,OAAM,GAAG,QAAQ,EAAE,KAAK,SAAS,GAAG,CAAC;AAAA,kBAC1D,GAAG,EAAE;AACL,yBAAO;AAAA,gBACT;AACA,oBAAI,OAAOA,OAAM,QAAQ,EAAE,MAAM,GAAG,MAAM;AACxC,yBAAOA,OAAM,QAAQ,EAAE,MAAM,GAAG;AAAA;AAEhC,yBAAOA,OAAMA,OAAM,QAAQ,EAAE,MAAM,GAAG,CAAC;AAAA,cAC3C;AAAA,YACF,GAAE,MAAM,CAAC,CAAC;AAAA,QACZ,GAAE,8DAA8D,MAAM,GAAG,CAAC;AAAA,MAE1E,GAAE,OAAO,IAAI;AACb,OAAC,SAASA,QAAOH,OAAM;AAEvB,YAAI,QAAQ,CAAC,EAAE;AACf,YAAI,WAAWG,OAAM,MAAM;AAC3B,YAAI,UAAUA,OAAM,MAAM;AAI1B,QAAAA,OAAM,OAAO;AAAA;AAAA;AAAA;AAAA,UAIX,QAAQ,SAAS,SAAS;AACxB,gBAAI,OAAO,MAAM,KAAK,SAAS;AAC/B,gBAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,sBAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,YACrC;AACA,oBAAQ,KAAK,CAAC,IAAIA,OAAM,KAAK,KAAK,CAAC,CAAC,KAAKA,OAAM,MAAM,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,UACvE;AAAA;AAAA;AAAA;AAAA;AAAA,UAMA,OAAO,SAAS,QAAQ;AACtB,gBAAI,OAAO,MAAM,KAAK,SAAS;AAC/B,gBAAI;AACJ,gBAAI,QAAQ,KAAK,CAAC,CAAC,GAAG;AAEpB,kBAAIA,OAAM,OAAO,KAAK,CAAC,GAAE,KAAK,CAAC,GAAE,KAAK,CAAC,CAAC;AACxC,qBAAQ,KAAK,CAAC,MAAM,IACjBA,OAAM,OAAO,IAAI,CAACH,MAAK,IAAI,CAAC,GAAG,GAAG,CAAC,IACnCG,OAAM,OAAO,IAAI,CAACH,MAAK,IAAI,CAAC,GAAG,GAAG,CAAC,IAAE;AAAA,YAC1C,OAAO;AACL,kBAAI,KAAK,SAAS,GAAG;AAEnB,oBAAIG,OAAM,OAAO,KAAK,CAAC,GAAE,KAAK,CAAC,GAAE,KAAK,CAAC,CAAC;AACxC,uBAAQ,KAAK,CAAC,MAAM,IACjBA,OAAM,OAAO,IAAI,CAACH,MAAK,IAAI,CAAC,GAAE,GAAE,CAAC,IACjCG,OAAM,OAAO,IAAI,CAACH,MAAK,IAAI,CAAC,GAAE,GAAE,CAAC,IAAG;AAAA,cACzC,OAAO;AAEL,oBAAI,KAAK,CAAC;AACV,uBAAQ,KAAK,CAAC,MAAM,IACjBG,OAAM,OAAO,IAAI,CAACH,MAAK,IAAI,CAAC,GAAE,GAAE,CAAC,IACjCG,OAAM,OAAO,IAAI,CAACH,MAAK,IAAI,CAAC,GAAE,GAAE,CAAC,IAAE;AAAA,cACxC;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAED,QAAAG,OAAM,OAAOA,OAAM,IAAI;AAAA,UACrB,QAAQ,SAAS,OAAO,OAAO,MAAM;AACnC,oBAAQ,QAAQ,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI;AAAA,UAChD;AAAA,UAEA,OAAO,SAAS,MAAM,OAAO,OAAO,MAAM;AACxC,gBAAI,SAASH,MAAK,IAAI,KAAK,OAAO,OAAO,IAAI,CAAC;AAC9C,mBAAQ,UAAU,IACfG,OAAM,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,IAC9BA,OAAM,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI;AAAA,UACvC;AAAA,QACF,CAAC;AAGD,QAAAA,OAAM,OAAO;AAAA;AAAA;AAAA;AAAA,UAIX,QAAQ,SAAS,SAAS;AACxB,gBAAI,OAAO,MAAM,KAAK,SAAS;AAC/B,mBAAQ,KAAK,WAAW,KACpB,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAIH,MAAK,KAAK,KAAK,CAAC,CAAC,MACjD,KAAK,CAAC,IAAIG,OAAM,KAAK,KAAK,CAAC,CAAC,MAC5BA,OAAM,MAAM,KAAK,CAAC,GAAG,IAAI,IAAIH,MAAK,KAAK,KAAK,CAAC,EAAE,MAAM;AAAA,UAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,UAMA,OAAO,SAAS,QAAQ;AACtB,gBAAI,OAAO,MAAM,KAAK,SAAS;AAC/B,gBAAI;AACJ,gBAAI,KAAK,WAAW,GAAG;AACrB,uBAASA,MAAK,IAAIG,OAAM,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAClE,qBAAQ,KAAK,CAAC,MAAM,IACjBA,OAAM,SAAS,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAE,CAAC,IACrCA,OAAM,SAAS,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAE,CAAC,IAAE;AAAA,YAC5C;AACA,gBAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,uBAASH,MAAK,IAAI,KAAK,CAAC,CAAC;AACzB,qBAAQ,KAAK,CAAC,KAAK,IAChBG,OAAM,SAAS,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAE,CAAC,IACrCA,OAAM,SAAS,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAE,CAAC,IAAI;AAAA,YAC9C;AACA,qBAASH,MAAK,IAAIG,OAAM,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAChD,mBAAQ,KAAK,CAAC,KAAK,IAChBA,OAAM,SAAS,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,SAAO,CAAC,IAC5CA,OAAM,SAAS,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,SAAO,CAAC,IAAI;AAAA,UACrD;AAAA,QACF,CAAC;AAED,QAAAA,OAAM,OAAOA,OAAM,IAAI;AAAA,UACrB,QAAQ,SAAS,OAAO,OAAO;AAC7B,oBAAQ,QAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,IAAI,IAAIH,MAAK,KAAK,KAAK,KAAK,CAAC;AAAA,UAC1E;AAAA,UAEA,OAAO,SAAS,MAAM,OAAO,OAAO;AAClC,mBAAQ,UAAU,IACf,IAAIG,OAAM,SAAS,IAAIH,MAAK,IAAI,KAAK,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK,IAAE,CAAC,IAClEG,OAAM,SAAS,IAAI,CAACH,MAAK,IAAI,KAAK,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK,IAAE,CAAC,IAAE;AAAA,UACtE;AAAA,QACF,CAAC;AAGD,QAAAG,OAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,UAKX,aAAa,SAAS,cAAc;AAClC,gBAAI,OAAO,MAAM,KAAK,SAAS,GAC/B,QAAQ,QAAQ,UAAU,cAAc,SAAS,UAAU,GAAG;AAC9D,gBAAI,KAAK,WAAW,GAAG;AACrB,wBAAU,IAAI,MAAM,KAAK,CAAC,EAAE,MAAM;AAClC,mBAAK,IAAI,GAAG,IAAI,KAAK,CAAC,EAAE,QAAQ,KAAK;AACnC,wBAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,cACxB;AACA,qBAAO;AAAA,YACT;AAEA,qBAAS,IAAI,MAAM;AACnB,iBAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,uBAAS,OAAO,OAAO,KAAK,CAAC,CAAC;AAAA,YAChC;AACA,uBAAWA,OAAM,KAAK,MAAM;AAE5B,qBAAS;AACT,iBAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,uBAAS,SAAS,KAAK,CAAC,EAAE,SAASH,MAAK,IAAIG,OAAM,KAAK,KAAK,CAAC,CAAC,IAAI,UAAU,CAAC;AAAA,YAC/E;AACA,sBAAW,KAAK,SAAS;AAEzB,uBAAW;AACX,iBAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,6BAAeA,OAAM,KAAK,KAAK,CAAC,CAAC;AACjC,mBAAK,IAAI,GAAG,IAAI,KAAK,CAAC,EAAE,QAAQ,KAAK;AACnC,4BAAYH,MAAK,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC;AAAA,cACnD;AAAA,YACF;AACA,wBAAa,OAAO,SAAS,KAAK;AAClC,mBAAO,SAAS;AAAA,UAClB;AAAA;AAAA;AAAA;AAAA,UAKA,YAAY,SAAS,aAAa;AAChC,gBAAI,OAAO,MAAM,KAAK,SAAS,GAC/B,KAAK,KAAK,GAAG;AACb,gBAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,qBAAO,IAAIG,OAAM,SAAS,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,YACzD;AACA,gBAAI,cAAcA,OAAM,YAAY,IAAI;AACxC,kBAAM,KAAK,SAAS;AACpB,gBAAI;AACJ,iBAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,kBAAI,IAAI,KAAK,CAAC,EAAE;AAAA,YAClB;AACA,kBAAM,IAAI,MAAM;AAChB,mBAAO,IAAIA,OAAM,SAAS,IAAI,aAAa,KAAK,GAAG;AAAA,UACrD;AAAA,UAEA,OAAO,SAAS,MAAM,QAAQ,KAAK,KAAK;AACtC,mBAAO,IAAIA,OAAM,SAAS,IAAI,QAAQ,KAAK,GAAG;AAAA,UAChD;AAAA,QACF,CAAC;AAED,QAAAA,OAAM,OAAOA,OAAM,IAAI;AAAA,UACrB,aAAa,SAAS,cAAc;AAClC,mBAAOA,OAAM,YAAY,KAAK,QAAQ,CAAC;AAAA,UACzC;AAAA,UAEA,WAAW,SAAS,YAAY;AAC9B,gBAAI,IAAI;AACR,gBAAI;AACJ,iBAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,kBAAI,IAAI,KAAK,CAAC,EAAE;AAAA,YAClB;AACA,mBAAOA,OAAM,MAAM,KAAK,YAAY,GAAG,KAAK,SAAS,GAAG,IAAI,KAAK,MAAM;AAAA,UACzE;AAAA,QACF,CAAC;AAGD,QAAAA,OAAM,OAAO;AAAA;AAAA;AAAA;AAAA,UAIX,QAAQ,SAAS,SAAS;AACxB,gBAAI,OAAO,MAAM,KAAK,SAAS;AAC/B,gBAAI,OAAO,OAAO,IAAI,IAAI;AAC1B,gBAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACnB,sBAAQ,KAAK,CAAC;AACd,sBAAQ,KAAK,CAAC;AACd,mBAAK,KAAK,CAAC;AACX,mBAAK,KAAK,CAAC;AACX,mBAAK,KAAK,CAAC;AAAA,YACf,OAAO;AACH,sBAAQA,OAAM,KAAK,KAAK,CAAC,CAAC;AAC1B,sBAAQA,OAAM,KAAK,KAAK,CAAC,CAAC;AAC1B,mBAAK,KAAK,CAAC,EAAE;AACb,mBAAK,KAAK,CAAC,EAAE;AACb,mBAAK,KAAK,CAAC;AAAA,YACf;AACA,mBAAOH,MAAK,IAAI,QAAQ,KAAK,KAAK,KAAKA,MAAK,MAAM,IAAI,KAAK,IAAI,MAAM,CAAC;AAAA,UACxE;AAAA;AAAA;AAAA;AAAA;AAAA,UAMA,OAAO,SAAS,QAAQ;AACtB,gBAAI,OAAO,MAAM,KAAK,SAAS;AAE/B,gBAAI;AACJ,gBAAI,KAAK,WAAW,GAAG;AACrB,uBAAS,KAAK,CAAC;AACf,qBAAO,KAAK,MAAM,CAAC;AAAA,YACrB,WAAW,KAAK,WAAW,GAAG;AAC5B,uBAASG,OAAM,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AACjE,qBAAO,KAAK,MAAM,CAAC;AAAA,YACrB,OAAO;AACL,uBAASA,OAAM,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC/C,qBAAO,KAAK,MAAM,CAAC;AAAA,YACrB;AAEA,gBAAI,IAAI,KAAK,CAAC;AACd,gBAAI,IAAI,KAAK,CAAC;AAEd,mBAAO,IAAIA,OAAM,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC;AAAA,UAC7C;AAAA,UAEA,UAAU,SAAS,SAAS,QAAQ;AAClC,gBAAI,KAAKA,OAAM,YAAY,MAAM;AACjC,gBAAI,QAAQ,OAAO,IAAI,SAAU,KAAK;AAAC,qBAAOA,OAAM,KAAK,GAAG;AAAA,YAAE,CAAC;AAC/D,gBAAI,IAAI,OAAO,OAAO,SAAUI,IAAG,KAAK;AAAC,qBAAOA,KAAI,IAAI;AAAA,YAAO,GAAG,CAAC;AAEnE,gBAAI,UAAU,CAAC;AACf,qBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACpC,uBAAS,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACxC,oBAAI,IAAIJ,OAAM,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,QAAQ,IAAI,GAAG,OAAO,MAAM;AAChG,wBAAQ,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA,cAC5B;AAAA,YACJ;AAEA,mBAAO;AAAA,UACT;AAAA,QACF,CAAC;AAGD,QAAAA,OAAM,OAAO;AAAA;AAAA;AAAA;AAAA,UAIX,UAAU,SAAS,WAAW;AAC5B,gBAAI,OAAO,MAAM,KAAK,SAAS,GAC/B,MAAM,IAAI,MAAM,CAAC,GACjB;AACA,gBAAI,KAAK,WAAW,GAAG;AACrB,uBAASH,MAAK,IAAIG,OAAM,OAAO,IAAI,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,IAClC,KAAK,CAAC,IAAIH,MAAK,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,YAChD,OAAO;AACL,uBAASA,MAAK,IAAIG,OAAM,OAAO,IAAI,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,IAClCA,OAAM,MAAM,KAAK,CAAC,CAAC,IAAIH,MAAK,KAAK,KAAK,CAAC,EAAE,MAAM,CAAC;AAAA,YACpE;AACA,gBAAI,CAAC,IAAI,KAAK,CAAC,IAAI;AACnB,gBAAI,CAAC,IAAI,KAAK,CAAC,IAAI;AACnB,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA,UAKA,KAAK,SAAS,MAAM;AAClB,gBAAI,OAAO,MAAM,KAAK,SAAS,GAC/B,MAAM,IAAI,MAAM,CAAC,GACjB;AACA,gBAAI,KAAK,WAAW,GAAG;AACrB,uBAASA,MAAK,IAAIG,OAAM,SAAS,IAAI,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAC3C,KAAK,CAAC,IAAIH,MAAK,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,YAChD,OAAO;AACL,uBAASA,MAAK,IAAIG,OAAM,SAAS,IAAI,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC,IAClDA,OAAM,MAAM,KAAK,CAAC,GAAG,IAAI,IAAIH,MAAK,KAAK,KAAK,CAAC,EAAE,MAAM,CAAC;AAAA,YAC1E;AACA,gBAAI,CAAC,IAAI,KAAK,CAAC,IAAI;AACnB,gBAAI,CAAC,IAAI,KAAK,CAAC,IAAI;AACnB,mBAAO;AAAA,UACT;AAAA,UAEA,aAAa,SAAS,YAAY,QAAQ,OAAO;AAC/C,mBAAO,SAAS;AAAA,UAClB;AAAA,QACF,CAAC;AAED,QAAAG,OAAM,OAAOA,OAAM,IAAI;AAAA,UACrB,UAAU,SAAS,SAAS,OAAO,OAAO;AACxC,mBAAOA,OAAM,SAAS,OAAO,OAAO,KAAK,QAAQ,CAAC;AAAA,UACpD;AAAA,UAEA,KAAK,SAAS,IAAI,OAAO,OAAO;AAC9B,mBAAOA,OAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,CAAC;AAAA,UAC/C;AAAA,QACF,CAAC;AAGD,iBAAS,wBAAwB,IAAI,IAAI,IAAI,IAAI;AAC/C,cAAI,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM,GAAG;AAC1C,kBAAM,IAAI,MAAM,sDAAsD;AAAA,UACxE;AACA,cAAI,UAAU,KAAK,KAAK,KAAK,OAAO,KAAK;AACzC,cAAI,KAAKH,MAAK,KAAK,UAAU,IAAI,WAAY,IAAE,KAAO,IAAE,GAAI;AAC5D,kBAAQ,KAAK,MAAM;AAAA,QACrB;AAGA,QAAAG,OAAM,OAAOA,OAAM,IAAI;AAAA,UACrB,iCAAiC,SAAS,gCAAgC,IAAI,IAAI,IAAI,IAAI;AACxF,gBAAI,IAAI,wBAAwB,IAAI,IAAI,IAAI,EAAE;AAC9C,mBAAOA,OAAM,MAAM,GAAG,CAAC;AAAA,UACzB;AAAA,UAEA,iCAAiC,SAAS,gCAAgC,IAAI,IAAI,IAAI,IAAI;AACxF,gBAAI,IAAI,wBAAwB,IAAI,IAAI,IAAI,EAAE;AAC9C,mBAAOA,OAAM,MAAM,GAAG,CAAC;AAAA,UACzB;AAAA,QACF,CAAC;AAAA,MAED,GAAE,OAAO,IAAI;AACb,YAAM,SAAU,WAAU;AACxB,iBAAS,YAAY,MAAM;AACzB,cAAI,YAAY,KAAK,CAAC,EAAE;AACxB,cAAI,YAAY,MAAM,OAAO,SAAS,EAAE,IAAI,SAAS,aAAa;AAChE,gBAAI,aACA,MAAM,OAAO,SAAS,EAAE,OAAO,SAAS,GAAE;AAAC,qBAAO,MAAI;AAAA,YAAW,CAAC;AACtE,mBAAO;AAAA,cAAI,MAAM,IAAI,MAAM,WAAW,EAAE,IAAI,SAAS,GAAE;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAE,CAAC;AAAA,cAC3D,MAAM,IAAI,MAAM,UAAU;AAAA,YAAC;AAAA,UACxC,CAAC;AACD,iBAAO;AAAA,QACT;AAMA,iBAAS,IAAI,OAAO,MAAM;AACxB,cAAI,OAAO,MAAM;AACjB,cAAI,WAAW,KAAK,CAAC,EAAE,SAAS;AAChC,cAAI,WAAW,OAAK,WAAW;AAC/B,cAAI,OAAO,MAAM,MAAM,MAAM,KAAK;AAClC,cAAI,UACA,MAAM,SAAS,MAAM,KAAK,IAAI,SAAS,GAAG;AAAE,mBAAO,CAAC,CAAC;AAAA,UAAE,CAAC,CAAC,EACpD,IAAI,SAAS,GAAG;AAAE,mBAAO,EAAE,CAAC;AAAA,UAAE,CAAC;AACxC,cAAI,QAAQ,MAAM,SAAS,OAAO,OAAO;AACzC,cAAI,OAAO,MAAM,KAAK,KAAK;AAK3B,cAAI,MAAM,MAAM,IAAI,QAAQ,IAAI,SAAS,GAAG;AAC1C,mBAAO,KAAK,IAAI,IAAI,MAAM,CAAC;AAAA,UAC7B,CAAC,CAAC;AACF,cAAI,MAAM,MAAM,IAAI,MAAM,IAAI,SAAS,GAAG,GAAG;AAC3C,mBAAO,KAAK,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC;AAAA,UACnC,CAAC,CAAC;AACF,cAAI,MAAM,MAAM;AAChB,cAAI,KAAM,MAAM;AAChB,iBAAO;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACJ;AAAA,QACF;AAIA,iBAAS,OAAO,OAAO;AACrB,cAAI,eAAe,YAAY,MAAM,IAAI;AAEzC,cAAI,WAAW,KAAK,KAAK,MAAM,MAAO,MAAM,QAAS;AACrD,cAAI,YAAY,aAAa,IAAI,SAAS,KAAK;AAC7C,gBAAI,MAAM,IAAI;AACd,gBAAI,KAAK,IAAI;AACb,mBAAO,WAAW,KAAK,KAAK,OAAO,IAAI,GAAG;AAAA,UAC5C,CAAC;AACD,cAAI,aAAa,MAAM,KAAK,IAAI,SAAS,MAAM,GAAG;AAChD,oBAAQ,OAAO,KAAK,UAAU,CAAC;AAAA,UACjC,CAAC;AACD,cAAI,SAAS,WAAW,IAAI,SAAS,GAAG;AACtC,gBAAI,UAAU,MAAM,SAAS,IAAI,GAAG,MAAM,QAAQ;AAClD,oBAAQ,UAAU,MAAM,IAAI,UAAU,WAAW;AAAA,UACnD,CAAC;AACD,cAAI,IAAI,MAAM,SAAS,IAAI,OAAO,MAAM,QAAQ;AAChD,cAAI,aAAa,MAAM,KAAK,IAAI,SAAS,MAAM,GAAG;AAChD,gBAAI,IAAI,IAAI,UAAU,CAAC;AACvB,mBAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAAA,UAC5B,CAAC;AACD,iBAAO;AAAA,YACH,IAAI;AAAA,YACJ,GAAG;AAAA,YACH,GAAG;AAAA,YACH;AAAA,YACA;AAAA,UACJ;AAAA,QACF;AAEA,iBAAS,OAAO,OAAO;AACrB,cAAI,cACC,MAAM,KAAK,MAAM,aAAc,IAAI,MAAM,MAAM,MAAM;AAC1D,cAAI,OAAO,SAAS,GAAG,IAAI,IAAI;AAC7B,mBAAO,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG,KAAK,CAAC;AAAA,UACzD;AACA,cAAI,SAAS,IAAI,KAAK,aAAa,MAAM,UAAU,MAAM,QAAQ;AACjE,iBAAO,EAAE,aAA0B,OAAe;AAAA,QACpD;AAEA,iBAAS,SAAS,OAAO,MAAM;AAC7B,cAAI,QAAQ,IAAI,OAAM,IAAI;AAC1B,cAAI,QAAQ,OAAO,KAAK;AACxB,cAAI,QAAQ,OAAO,KAAK;AAGxB,cAAI,YACA,KAAK,IAAI,MAAM,QAAQ,MAAM,OAAO,KAAM,MAAM;AACpD,gBAAM,IAAI;AACV,gBAAM,IAAI;AACV,gBAAM,YAAY;AAClB,iBAAO;AAAA,QACT;AAEA,eAAO,EAAE,KAAK,SAAS;AAAA,MACzB,EAAG;AAYH,YAAM,OAAO;AAAA,QACX,cAAc,SAAS,eAAc;AAKnC,cAAI,aAAa,IAAI,MAAM,UAAU,MAAM;AAC3C,mBAAQ,IAAE,GAAE,IAAE,UAAU,QAAO,KAAI;AACjC,gBAAI,QAAQ,CAAC,CAAC;AACd,uBAAW,CAAC,IAAG,MAAM,OAAO,UAAU,CAAC,CAAC;AAAA,UAC1C;AACA,iBAAO,MAAM,UAAU;AAAA,QAEzB;AAAA,QAEA,eAAe,SAAS,gBAAgB;AAGtC,cAAI,aAAa,IAAI,MAAM,UAAU,CAAC,EAAE,MAAM;AAC9C,mBAAQ,IAAE,GAAE,IAAE,UAAU,CAAC,EAAE,QAAO,KAAI;AACpC,gBAAI,QAAQ,CAAC,CAAC;AACd,uBAAW,CAAC,IAAG,MAAM,OAAO,UAAU,CAAC,EAAE,CAAC,CAAC;AAAA,UAC7C;AACA,iBAAO,MAAM,UAAU;AAAA,QAEzB;AAAA,QAEA,eAAe,SAAS,cAAc,MAAM;AAE1C,cAAI,OAAO,IAAI,MAAM,KAAK,MAAM;AAChC,mBAAQ,IAAE,GAAE,IAAE,KAAK,QAAO,KAAI;AAC5B,iBAAK,CAAC,IAAI,KAAK,CAAC;AAAA,UAClB;AACA,iBAAO,MAAM,cAAc,IAAI;AAAA,QAEjC;AAAA,QAEA,cAAc,SAAS,aAAa,OAAM;AACxC,iBAAO,MAAM,KAAK,EAAE,UAAU;AAAA,QAChC;AAAA,QAEA,eAAe,SAAS,cAAc,MAAK;AACzC,iBAAO,KAAK,UAAU;AAAA,QACxB;AAAA,QAEA,YAAY,SAAS,WAAW,GAAE,GAAE;AAClC,cAAI,GAAG,GAAG,GAAG,QAAQ;AACrB,cAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;AACxB,gBAAG,EAAE,KAAK,IAAE,GAAE;AACZ,uBAAS,CAAC;AACV,mBAAK,IAAI,GAAG,IAAI,EAAE,KAAK,GAAG,KAAK;AAC7B,uBAAO,CAAC,IAAI,CAAC;AACb,qBAAK,IAAI,GAAG,IAAI,EAAE,KAAK,GAAG,KAAK;AAC7B,wBAAM;AACN,uBAAK,IAAI,GAAG,IAAI,EAAE,KAAK,GAAG,KAAK;AAC7B,2BAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AAAA,kBAC7C;AACA,yBAAO,CAAC,EAAE,CAAC,IAAI;AAAA,gBACjB;AAAA,cACF;AACA,qBAAO,MAAM,MAAM;AAAA,YACrB;AACA,qBAAS,CAAC;AACV,iBAAK,IAAI,GAAG,IAAI,EAAE,KAAK,GAAG,KAAK;AAC7B,qBAAO,CAAC,IAAI,CAAC;AACb,mBAAK,IAAI,GAAG,IAAI,EAAE,KAAK,GAAG,KAAK;AAC7B,sBAAM;AACN,qBAAK,IAAI,GAAG,IAAI,EAAE,KAAK,GAAG,KAAK;AAC7B,yBAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;AAAA,gBAC1C;AACA,uBAAO,CAAC,EAAE,CAAC,IAAI;AAAA,cACjB;AAAA,YACF;AACA,mBAAO,MAAM,MAAM;AAAA,UACrB;AAAA,QACF;AAAA;AAAA,QAIA,SAAS,SAAS,QAAQ,OAAM,OAAM;AAGpC,cAAI,WAAW,MAAM,YAAY,KAAK;AAEtC,cAAI,UAAU,MAAM,UAAU;AAC9B,cAAI,OAAO,MAAM,WAAW,MAAM,QAAQ,GAAE,OAAO;AACnD,iBAAO,MAAM,WAAW,MAAK,KAAK;AAAA,QAEpC;AAAA,QAEA,UAAU,SAAS,SAAS,OAAM,OAAM,OAAM;AAC5C,cAAI,OAAO,MAAM,QAAQ,OAAM,KAAK;AAEpC,cAAI,UAAU,CAAC;AACf,kBAAQ,QAAQ,CAAC;AACjB,cAAI,WAAW,MAAM,SAAS,OAAO,IAAI;AACzC,kBAAQ,OAAO;AACf,cAAI,WAAW,MAAM,KAAK;AAC1B,kBAAQ,MAAM,YAAY,MAAM,UAAU,OAAO,QAAQ;AAEzD,kBAAQ,MAAM,MAAM,MAAM,IAAI,UAAU,QAAQ;AAChD,kBAAQ,MAAM,MAAM,QAAQ,MAAM,OAAO,MAAM,CAAC,EAAE,SAAS;AAE3D,kBAAQ,MAAM,MAAM,MAAM,IAAI,OAAO,QAAQ;AAC7C,kBAAQ,MAAM,MACV,QAAQ,MAAM,OAAO,MAAM,UAAU,MAAM,CAAC,EAAE,SAAS,KAAK;AAEhE,kBAAQ,MAAM,MAAM,MAAM,IAAI,OAAO,QAAQ;AAC7C,kBAAQ,MAAM,MAAM,QAAQ,MAAM,OAAO,MAAM,SAAS;AAExD,kBAAQ,MAAM,KAAK,IAAK,QAAQ,MAAM,MAAM,QAAQ,MAAM;AAC1D,cAAI,QAAQ,MAAM,KAAK;AAAG,oBAAQ,MAAM,KAAK;AAE7C,kBAAQ,MAAM,SAAS,QAAQ,MAAM,MAAM,QAAQ,MAAM;AACzD,kBAAQ,MAAM,SACV,MAAM;AAAA,YAAW,QAAQ,MAAM;AAAA,YACd,MAAM,CAAC,EAAE,SAAS;AAAA,YAClB,MAAM,UAAU,MAAM,CAAC,EAAE,SAAS,KAAK;AAAA,UAAC;AAE7D,kBAAQ,MAAM,OAAO,KAAK,KAAK,QAAQ,MAAM,GAAG;AAEhD,kBAAQ,MAAM,QAAQ,IAAK,QAAQ,MAAM,MAAM,QAAQ,MAAM;AAC7D,cAAI,QAAQ,MAAM,QAAQ;AAAG,oBAAQ,MAAM,QAAQ;AAEnD,kBAAQ,QAAQ,IAAI,MAAM,MAAM,CAAC,EAAE,MAAM;AACzC,cAAI,QAAQ,MAAM,YAAY,KAAK;AACnC,cAAI,KAAK,IAAI;AAEb,mBAAQ,IAAE,GAAG,IAAE,KAAK,QAAO,KAAI;AAC7B,kBAAI,KAAK,KAAK,QAAQ,MAAM,MAAM,KAAK,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AACvD,iBAAI,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG;AAC1B,iBAAI,MAAM,MAAM,IAAI,MAAM,SAAS,MAAM,CAAC,EAAE,SAAS,GAAG,KAAK;AAE7D,oBAAQ,MAAM,CAAC,IAAE,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,EAAE;AAAA,UACxC;AAEA,kBAAQ,UAAU;AAClB,iBAAO;AAAA,QACT;AAAA,QAEA,UAAU,SAAS,SAAS,OAAM;AAChC,iBAAO,MAAM,WAAW,MAAM,UAAU,GAAE,KAAK;AAAA,QACjD;AAAA,QAGA,aAAa,SAAS,YAAY,OAAM;AACtC,cAAI,QAAQ,MAAM,WAAW,MAAM,UAAU,GAAE,KAAK;AACpD,cAAI,WAAW,MAAM,IAAI,KAAK;AAC9B,iBAAO;AAAA,QACT;AAAA,QAEA,UAAU,SAAS,SAAS,OAAO,MAAM;AACvC,cAAI,OAAO,MAAM,WAAW,OAAO,IAAI;AACvC,iBAAO,IAAI,MAAM,IAAI;AAAA,QACvB;AAAA,QAEA,WAAW,SAAS,UAAU,OAAO,UAAU;AAC7C,iBAAO,MAAM,eAAe,OAAO,QAAQ;AAAA,QAC7C;AAAA,QAEA,KAAK,SAAS,IAAI,UAAU,UAAU;AACpC,cAAIW,OAAM;AACV,mBAAQ,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACvC,YAAAA,QAAO,KAAK,IAAI,SAAS,CAAC,IAAI,UAAU,CAAC;AAAA,UAC3C;AACA,iBAAOA;AAAA,QACT;AAAA,QAEA,KAAK,SAAS,IAAI,OAAO,UAAU;AACjC,cAAIC,OAAM;AACV,mBAAQ,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACpC,YAAAA,QAAO,KAAK,IAAI,MAAM,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC;AAAA,UAC3C;AACA,iBAAOA;AAAA,QACT;AAAA,QAEA,KAAK,SAAS,IAAI,OAAO,UAAU;AACjC,cAAIC,OAAM;AACV,mBAAQ,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACpC,YAAAA,QAAO,KAAK,IAAI,MAAM,CAAC,IAAI,UAAU,CAAC;AAAA,UACxC;AACA,iBAAOA;AAAA,QACT;AAAA,QAEA,gBAAgB,SAAS,eAAe,GAAE,GAAE;AAC1C,cAAI,MAAM,IAAI,MAAM,EAAE,MAAM;AAC5B,mBAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AACzB,gBAAI,CAAC,IAAI,IAAI,MAAM,EAAE,CAAC,EAAE,MAAM;AAC9B,qBAAQ,IAAE,GAAE,IAAE,EAAE,CAAC,EAAE,QAAO,KAAI;AAC5B,kBAAI,CAAC,EAAE,CAAC,IAAE,EAAE,CAAC,EAAE,CAAC,IAAE,EAAE,CAAC,EAAE,CAAC;AAAA,YAC1B;AAAA,UACF;AACA,iBAAO,MAAM,GAAG;AAAA,QAClB;AAAA,MACF,CAAC;AAEC,YAAM,QAAQ;AAEd,aAAO;AAAA,IACT,CAAC;AAAA;AAAA;;;ACt4JD;AAAA;AAGA,QAAI;AACJ,KAAC,SAAU,SAAS;AAElB,UAAG,OAAO,yBAAyB,aAAa;AAC9C,YAAG,aAAa,OAAO,SAAS;AAC9B,kBAAQ,OAAO;AAAA,QACjB,WAAW,eAAe,OAAO,UAAU,OAAO,KAAK;AACrD,iBAAO,WAAY;AACjB,gBAAIC,UAAS,CAAC;AACd,oBAAQA,OAAM;AACd,mBAAOA;AAAA,UACT,CAAC;AAAA,QACH,OAAO;AACL,kBAAQ,SAAS,CAAC,CAAC;AAAA,QACrB;AAAA,MACF,OAAO;AACL,gBAAQ,SAAS,CAAC,CAAC;AAAA,MACrB;AAAA,IAEF,GAAE,SAASC,SAAQ;AACnB,MAAAA,QAAO,UAAU;AACjB,UAAI,IAAI;AAER,eAAS,QAAQ,KAAK,GAAG;AAAE,iBAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE;AAAG,cAAI,IAAI,IAAI,IAAI,CAAC;AAAG,eAAO;AAAA,MAAG;AACpG,eAAS,aAAa,GAAG,GAAG,IAAI,IAAI,MAAM;AACxC,YAAG,MAAM;AAAG,iBAAO;AACnB,YAAG,MAAM;AAAG,iBAAO;AACnB,YAAI,MAAM,IAAI,GAAG,KAAK;AACtB,iBAAQ,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACzB,eAAK,KAAK,IAAI,MAAM,OAAO;AAC3B,eAAK;AAAI,eAAK;AAAA,QAChB;AACA,eAAO;AAAA,MACT;AACA,eAAS,aAAa,SAAS,SAAS,MAAM,SAAS,MAAM;AAC3D,eAAO,SAAS,OAAO,GAAE,GAAG;AAC1B,cAAG,SAAS;AACV,gBAAG,MAAM;AAAG,qBAAQ,WAAW,IAAI,YAAY;AAAA,qBACvC,IAAI;AAAG,qBAAO;AAAA,UACxB;AACA,cAAG,MAAM;AAAG,mBAAO,QAAQ,CAAC;AAC5B,cAAG,MAAM;AAAG,mBAAO,QAAQ,CAAC;AAC5B,cAAG,IAAI;AAAG,mBAAO;AACjB,eAAG;AACH,cAAI,KAAK,QAAQ,CAAC,GAAG,KAAK,QAAQ,CAAC;AACnC,iBAAO,aAAa,GAAG,GAAG,IAAI,IAAI,IAAI;AAAA,QACxC;AAAA,MACF;AACA,UAAI,UAAW,WAAW;AACxB,YAAI,IAAI;AAER,YAAI,SAAS,CAAC,aAAe,cAAgB,eAAa,gBAAc,aAAa,YAAY,EAAE,QAAQ;AAC3G,YAAI,SAAS,CAAC,aAAe,YAAc,eAAa,aAAa,aAAa,CAAG,EAAE,QAAQ;AAC/F,YAAI,SAAS,CAAC,GAAK,iBAAkB,gBAAiB,iBAAkB,cAAe,EAAE,QAAQ;AACjG,YAAI,SAAS,CAAC,gBAAkB,gBAAiB,iBAAkB,gBAAiB,cAAe,EAAE,QAAQ;AAE7G,iBAAS,QAAQ,GAAG;AAClB,cAAI,IAAE,GAAG,KAAG,GAAG,KAAG,GAAG,IAAI,IAAI;AAC7B,cAAG,IAAI,GAAG;AACR,iBAAK,QAAQ,QAAQ,CAAC;AACtB,iBAAK,QAAQ,QAAQ,CAAC;AACtB,gBAAI,KAAK;AAAA,UACX,OAAO;AACL,gBAAI,KAAK,IAAI;AACb,gBAAI,KAAK;AACT,iBAAK,QAAQ,QAAQ,CAAC;AACtB,iBAAK,QAAQ,QAAQ,CAAC;AACtB,gBAAI,EAAE,KAAK,IAAE,CAAC,KAAG,EAAE,IAAI,EAAE,IAAE,KAAG,EAAE,IAAI,EAAE,IAAE,KAAG,IAAE;AAAA,UAC/C;AACA,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,CAAC,aAAe,aAAe,eAAa,gBAAc,YAAa,YAAY,EAAE,QAAQ;AAC1G,YAAI,SAAS,CAAC,cAAgB,YAAc,eAAa,aAAa,aAAa,CAAG,EAAE,QAAQ;AAChG,YAAI,SAAS,CAAC,GAAK,WAAa,iBAAkB,gBAAiB,cAAe,EAAE,QAAQ;AAC5F,YAAI,SAAS,CAAC,eAAe,iBAAkB,gBAAiB,eAAgB,aAAc,EAAE,QAAQ;AAExG,iBAAS,QAAQ,GAAG;AAClB,cAAI,IAAE,GAAG,KAAG,GAAG,KAAG,GAAG,IAAI,IAAE,GAAG,KAAK,EAAE,IAAI,CAAC,IAAI;AAC9C,cAAG,KAAK,IAAI,CAAC,IAAG,GAAG;AACjB,iBAAK,IAAE,QAAQ,QAAQ,CAAC;AACxB,iBAAK,QAAQ,QAAQ,CAAC;AACtB,gBAAI,KAAK;AAAA,UACX,OAAO;AACL,gBAAI,KAAK;AACT,iBAAG,QAAQ,QAAQ,CAAC;AACpB,iBAAG,QAAQ,QAAQ,CAAC;AACpB,gBAAE,EAAE,KAAK,IAAE,EAAE,IAAI,CAAC,CAAC,KAAG,EAAE,IAAI,EAAE,IAAE,KAAG,EAAE,IAAI,EAAE,IAAE,KAAG,IAAE,EAAE,IAAI,CAAC;AACzD,gBAAG,IAAI;AAAG,kBAAI,CAAC;AAAA,UACjB;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,SAASC,SAAQ,GAAG,GAAG;AAC5B,cAAI,KAAK,MAAM,CAAC;AAChB,cAAG,CAAC,SAAS,CAAC;AAAG,mBAAO,MAAM,CAAC,IAAI,IAAI;AACvC,cAAG,IAAI;AAAG,oBAAS,IAAE,IAAG,KAAG,KAAGA,SAAQ,GAAG,CAAC,CAAC;AAC3C,cAAG,IAAI;AAAG,oBAAS,IAAE,IAAG,KAAG,KAAGA,SAAQ,CAAC,GAAG,CAAC;AAC3C,cAAG,MAAM;AAAG,mBAAO,QAAQ,CAAC;AAC5B,cAAG,MAAM;AAAG,mBAAO,QAAQ,CAAC;AAC5B,cAAG,MAAM;AAAG,mBAAO;AAEnB,cAAI,MAAI;AACR,cAAG,IAAI,GAAG;AACR,kBAAM,aAAa,GAAG,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAE,EAAE;AAAA,UACpD,OAAO;AACL,gBAAI,IAAE,IAAE,EAAE,OAAO,IAAE,EAAE,MAAM,EAAE,KAAK,KAAG,CAAC,CAAC,KAAG,CAAC;AAC3C,gBAAI,OAAK;AACT,gBAAI,MAAI,GAAK,MAAI;AACjB,gBAAI,KAAG,GAAK,MAAM;AAClB,gBAAI,MAAM,IAAI;AACd,qBAAS,IAAE,GAAE,IAAE,GAAE,KAAK;AACpB,oBAAI,IAAE,MAAI,KAAG;AACb,oBAAI;AACJ,mBAAG;AACH,kBAAI,EAAE,IAAI,EAAE,IAAI,MAAM;AACpB,sBAAM;AACN,uBAAO;AACP,uBAAO;AACP,uBAAO;AAAA,cACT;AACA,kBAAI;AAAM,uBAAO;AACjB,qBAAK,CAAC;AACN,kBAAI,KAAK;AAAG,sBAAI;AAAA,YAClB;AACA,kBAAI,IAAI,MAAI;AACZ,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,MACF,EAAG;AACH,UAAI,UAAW,WAAW;AACxB,YAAI,IAAI;AAER,YAAI,SAAS,CAAC,aAAe,YAAc,gBAAc,eAAa,cAAc,WAAW,EAAE,QAAQ;AACzG,YAAI,SAAS,CAAC,aAAe,eAAa,eAAa,YAAa,aAAa,CAAG,EAAE,QAAQ;AAC9F,YAAI,SAAS,CAAC,GAAK,iBAAkB,gBAAiB,iBAAkB,cAAe,EAAE,QAAQ;AACjG,YAAI,SAAS,CAAC,gBAAkB,gBAAiB,iBAAkB,gBAAiB,cAAe,EAAE,QAAQ;AAE7G,iBAAS,QAAQ,GAAG;AAClB,cAAI,IAAE,GAAG,KAAG,GAAG,KAAG,GAAG,IAAI,IAAI,GAAG,KAAK,IAAI;AACzC,cAAG,IAAI,GAAG;AACR,iBAAK,QAAQ,QAAQ,CAAC;AACtB,iBAAK,QAAQ,QAAQ,CAAC;AACtB,gBAAI,KAAG,KAAK,IAAI,QAAQ,GAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAAA,UACxC,OAAO;AACL,gBAAI,KAAK;AACT,iBAAK,QAAQ,QAAQ,CAAC;AACtB,iBAAK,QAAQ,QAAQ,CAAC;AACtB,gBAAI,EAAE,KAAK,IAAE,CAAC,KAAG,EAAE,IAAI,EAAE,IAAE,KAAG,EAAE,IAAI,EAAE,IAAE,KAAG,IAAE;AAAA,UAC/C;AACA,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,CAAC,eAAkB,aAAiB,cAAkB,eAAgB,gBAAiB,WAAc,EAAE,QAAQ;AAC5H,YAAI,SAAS,CAAC,aAAiB,cAAiB,YAAiB,eAAgB,YAAgB,aAAgB,CAAC,EAAE,QAAQ;AAC5H,YAAI,SAAS,CAAC,GAAK,WAAa,iBAAkB,gBAAiB,cAAe,EAAE,QAAQ;AAC5F,YAAI,SAAS,CAAC,eAAe,iBAAkB,gBAAiB,eAAgB,aAAc,EAAE,QAAQ;AAExG,iBAAS,QAAQ,GAAG;AAClB,cAAI,IAAE,GAAG,KAAG,GAAG,KAAG,GAAG,IAAI,IAAE,GAAG,KAAK,IAAI;AACvC,cAAG,IAAI,GAAG;AACR,iBAAK,IAAE,QAAQ,QAAQ,CAAC;AACxB,iBAAK,QAAQ,QAAQ,CAAC;AACtB,gBAAI,KAAG,KAAK,KAAK,QAAQ,GAAE,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI;AAAA,UACjD,OAAO;AACL,gBAAI,KAAK;AACT,iBAAG,QAAQ,QAAQ,CAAC;AACpB,iBAAG,QAAQ,QAAQ,CAAC;AACpB,gBAAE,EAAE,KAAK,IAAE,CAAC,KAAG,EAAE,IAAI,EAAE,IAAE,KAAG,EAAE,IAAI,EAAE,IAAE,KAAG,IAAE;AAAA,UAC7C;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,aAAa,SAAS,SAAS,WAAW,GAAG,EAAE;AAAA,MACxD,EAAG;AACH,UAAI,UAAW,WAAW;AACxB,YAAI,OAAO,CAAC,GAAK,WAAW,WAAW,WAAW,WAAW,WAAa,QAAU,EAAE,QAAQ;AAC9F,YAAI,OAAO,CAAC,YAAY,YAAc,WAAa,YAAc,WAAa,aAAe,YAAc,aAAe,SAAW,EAAE,QAAQ;AAE/I,iBAAS,QAAQ,GAAG;AAClB,cAAG,KAAK;AAAM,mBAAO,QAAQ,MAAM,IAAE,KAAG,OAAK,KAAK;AAClD,iBAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,IAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,IAAE,QAAQ,MAAM,OAAK,EAAE,IAAI,CAAC,CAAC;AAAA,QACrE;AAEA,YAAI,OAAO,CAAC,KAAK,YAAY,YAAY,YAAY,YAAc,WAAa,QAAU,EAAE,QAAQ;AACpG,YAAI,OAAO,CAAC,YAAY,aAAe,YAAc,WAAa,aAAe,YAAc,aAAe,YAAc,UAAY,EAAE,QAAQ;AAElJ,iBAAS,QAAQ,GAAG;AAClB,cAAG,IAAI;AAAM,mBAAO,IAAI,QAAQ,MAAM,IAAE,KAAG,OAAK,KAAK;AACrD,kBAAQ,IAAI,IAAI,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,IAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,IAAE,QAAQ,MAAM,OAAK,EAAE,IAAI,CAAC,CAAC;AAAA,QACxF;AAEA,eAAO,SAASC,SAAQ,GAAG,GAAG;AAC5B,cAAI,KAAK,MAAM,CAAC;AAChB,cAAG,MAAM;AAAG,mBAAO,QAAQ,CAAC;AAC5B,cAAG,MAAM;AAAG,mBAAO,QAAQ,CAAC;AAC5B,cAAG,IAAI;AAAG,mBAAO;AACjB,cAAG,EAAE,IAAI,CAAC,MAAM;AAAG,mBAAO;AAC1B,cAAG,KAAK;AAAU,mBAAO;AAEzB,cAAI,MAAM,GAAK,GAAG,MAAM,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,GAAK,KAAG,GAAK,MAAI;AAC7D,cAAI,IAAE,IAAE,EAAE,OAAO,IAAE,EAAE,MAAM,EAAE,KAAK,KAAG,CAAC,CAAC,KAAG,CAAC;AAC3C,eAAK,IAAE,GAAE,IAAE,GAAE,KAAK;AAChB,kBAAI,IAAE,MAAI,KAAK;AACf,kBAAI;AAAI,iBAAG;AACX,gBAAI,EAAE,IAAI,EAAE,IAAI,MAAM;AACpB,oBAAM;AACN,qBAAO;AACP,qBAAO;AAAA,YACT;AACA,gBAAG,KAAK;AAAG,oBAAM;AAAA,UACnB;AACA,iBAAOA,SAAQ,GAAG,CAAC,IAAI;AACvB,iBAAO,IAAI,KAAM,IAAE,IAAK,CAAC,MAAM;AAAA,QACjC;AAAA,MAEF,EAAG;AAEH,UAAI,UAAW,WAAW;AACxB,YAAI,OAAO,CAAC,aAAa,WAAY,YAAY,WAAc,WAAa,SAAY,KAAO,EAAE,QAAQ;AACzG,YAAI,OAAO,CAAC,YAAY,aAAe,YAAc,aAAe,WAAa,WAAc,QAAU,EAAE,QAAQ;AAEnH,iBAAS,QAAQ,GAAG;AAClB,cAAG,KAAK;AAAG,mBAAO,CAAC,EAAE,IAAI,IAAE,CAAC,IAAI,QAAQ,GAAE,CAAC,IAAI,QAAQ,MAAM,IAAE,IAAE,CAAC;AAClE,iBAAO,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,QAAQ,MAAM,IAAE,CAAC;AAAA,QAClD;AAEA,YAAI,OAAO,CAAC,GAAK,YAAY,aAAa,aAAa,aAAe,YAAc,QAAU,EAAE,QAAQ;AACxG,YAAI,OAAO,CAAC,YAAY,YAAY,YAAe,YAAc,YAAc,WAAa,SAAW,EAAE,QAAQ;AAEjH,iBAAS,QAAQ,GAAG;AAClB,cAAG,KAAK;AAAG,mBAAO,EAAE,IAAI,IAAE,CAAC,IAAI,QAAQ,GAAE,CAAC,IAAK,IAAE,IAAK,QAAQ,MAAM,IAAE,IAAE,CAAC;AACzE,iBAAO,EAAE,IAAI,CAAC,CAAC,IAAE,EAAE,KAAK,CAAC,IAAE,QAAQ,MAAM,IAAE,CAAC;AAAA,QAC9C;AAEA,eAAO,aAAa,SAAS,SAAS,WAAW,GAAG,CAAC;AAAA,MACvD,EAAG;AACH,MAAAF,QAAO,UAAU;AACjB,MAAAA,QAAO,UAAU;AACjB,MAAAA,QAAO,UAAU;AACjB,MAAAA,QAAO,UAAU;AAAA,IACjB,CAAC;AAAA;AAAA;", "names": ["Math", "undefined", "isArray", "jStat", "i", "slice", "row", "n", "m", "sum", "number", "curriedFunction", "x", "X", "ssr", "sse", "sst", "module", "BESSEL", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}