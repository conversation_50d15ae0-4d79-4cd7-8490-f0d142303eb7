{"hash": "80cd7166", "browserHash": "3a0927fa", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "d72dfa55", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "9be6a795", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "719fdadb", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "6e9fa28c", "needsInterop": true}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "94788066", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "2034eda7", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "67755a1b", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "59fc5b20", "needsInterop": false}, "react-spreadsheet": {"src": "../../react-spreadsheet/dist/es/index.js", "file": "react-spreadsheet.js", "fileHash": "f0489dc3", "needsInterop": false}, "@fortune-sheet/react": {"src": "../../@fortune-sheet/react/dist/index.esm.js", "file": "@fortune-sheet_react.js", "fileHash": "7925855d", "needsInterop": false}}, "chunks": {"chunk-DAN7SEKX": {"file": "chunk-DAN7SEKX.js"}, "chunk-CURPYWJH": {"file": "chunk-CURPYWJH.js"}, "chunk-3JF7OS7Z": {"file": "chunk-3JF7OS7Z.js"}, "chunk-5RYML6EB": {"file": "chunk-5RYML6EB.js"}, "chunk-ROME4SDB": {"file": "chunk-ROME4SDB.js"}}}