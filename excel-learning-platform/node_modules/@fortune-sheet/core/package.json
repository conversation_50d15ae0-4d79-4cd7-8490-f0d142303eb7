{"name": "@fortune-sheet/core", "version": "1.0.2", "main": "dist/index.js", "module": "dist/index.esm.js", "typings": "dist/index.d.ts", "files": ["dist"], "repository": "https://github.com/ruilisi/fortune-sheet", "license": "MIT", "dependencies": {"@fortune-sheet/formula-parser": "^0.2.13", "dayjs": "^1.11.0", "immer": "^9.0.12", "lodash": "^4.17.21", "numeral": "^2.0.6", "uuid": "^8.3.2"}, "devDependencies": {"@types/lodash": "^4.14.179", "@types/numeral": "^2.0.2", "@types/uuid": "^8.3.4"}, "keywords": ["spreadsheet", "sheet", "excel", "data table", "table", "data grid", "react", "reactjs", "react component", "collabration", "collabrative"], "publishConfig": {"access": "public"}}