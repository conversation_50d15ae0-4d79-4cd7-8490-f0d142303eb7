import { Context } from "../context";
import { Cell, CellMatrix, GlobalCache } from "../types";
declare type Toolbar<PERSON>temClickHandler = (ctx: Context, cellInput: HTMLDivElement, cache?: GlobalCache) => void;
declare type ToolbarItemSelectedFunc = (cell: Cell | null | undefined) => boolean;
export declare function updateFormatCell(ctx: Context, d: CellMatrix, attr: keyof Cell, foucsStatus: any, row_st: number, row_ed: number, col_st: number, col_ed: number, canvas?: CanvasRenderingContext2D): void;
export declare function updateFormat(ctx: Context, $input: HTMLDivElement, d: CellMatrix, attr: keyof Cell, foucsStatus: any, canvas?: CanvasRenderingContext2D): void;
export declare function autoSelectionFormula(ctx: Context, cellInput: HTMLDivElement, fxInput: HTMLDivElement | null | undefined, formula: string, cache: GlobalCache): void;
export declare function cancelPaintModel(ctx: Context): void;
export declare function handleCurrencyFormat(ctx: Context, cellInput: HTMLDivElement): void;
export declare function handlePercentageFormat(ctx: Context, cellInput: HTMLDivElement): void;
export declare function handleNumberDecrease(ctx: Context, cellInput: HTMLDivElement): void;
export declare function handleNumberIncrease(ctx: Context, cellInput: HTMLDivElement): void;
export declare function handleBold(ctx: Context, cellInput: HTMLDivElement): void;
export declare function handleItalic(ctx: Context, cellInput: HTMLDivElement): void;
export declare function handleStrikeThrough(ctx: Context, cellInput: HTMLDivElement): void;
export declare function handleUnderline(ctx: Context, cellInput: HTMLDivElement): void;
export declare function handleHorizontalAlign(ctx: Context, cellInput: HTMLDivElement, value: string): void;
export declare function handleVerticalAlign(ctx: Context, cellInput: HTMLDivElement, value: string): void;
export declare function handleFormatPainter(ctx: Context): void;
export declare function handleClearFormat(ctx: Context): void;
export declare function handleTextColor(ctx: Context, cellInput: HTMLDivElement, color: string): void;
export declare function handleTextBackground(ctx: Context, cellInput: HTMLDivElement, color: string): void;
export declare function handleBorder(ctx: Context, type: string, borderColor?: string, borderStyle?: string): void;
export declare function handleMerge(ctx: Context, type: string): void;
export declare function handleSort(ctx: Context, isAsc: boolean): void;
export declare function handleFreeze(ctx: Context, type: string): void;
export declare function handleTextSize(ctx: Context, cellInput: HTMLDivElement, size: number, canvas?: CanvasRenderingContext2D): void;
export declare function handleSum(ctx: Context, cellInput: HTMLDivElement, fxInput: HTMLDivElement | null | undefined, cache?: GlobalCache): void;
export declare function handleLink(ctx: Context): void;
export declare function toolbarItemClickHandler(name: string): ToolbarItemClickHandler;
export declare function toolbarItemSelectedFunc(name: string): ToolbarItemSelectedFunc;
export {};
