import type { Cell, CellMatrix, FormulaDependency, FormulaCell, FormulaCellInfoMap, History, Rect, Selection } from "../types";
import { Context } from "../context";
export declare function isFormula(value: any): boolean;
export declare class FormulaCache {
    parser: any;
    func_selectedrange?: Selection;
    data_parm_index: number;
    cellTextToIndexList: any;
    rangechangeindex?: number;
    selectingRangeIndex: number;
    rangeResizeObj?: any;
    rangeResize?: any;
    rangeResizeIndex?: number;
    rangeResizexy?: any;
    rangeResizeWinH?: any;
    rangeResizeWinW?: any;
    rangeResizeTo?: any;
    rangeSetValueTo?: any;
    rangeIndex?: number;
    rangestart?: boolean;
    rangetosheet?: string;
    rangedrag_column_start?: boolean;
    rangedrag_row_start?: boolean;
    functionRangeIndex?: number[];
    functionlistMap: any;
    execFunctionExist?: any[];
    execFunctionGlobalData: any;
    formulaCellInfoMap: FormulaCellInfoMap | null;
    constructor();
    tryGetCellAsNumber(cell: Cell): string | number | boolean | undefined;
    updateFormulaCache(ctx: Context, history: History, type: "undo" | "redo", data?: CellMatrix): void;
}
export declare function iscelldata(txt: string): boolean;
export declare function getcellrange(ctx: Context, txt: string, formulaId?: string, data?: CellMatrix): FormulaDependency | null;
export declare function isFunctionRange(ctx: Context, txt: string, r: number | null, c: number | null, id: string, dynamicArray_compute: any, cellRangeFunction: any): string;
export declare function getAllFunctionGroup(ctx: Context): FormulaCell[];
export declare function delFunctionGroup(ctx: Context, r: number, c: number, id?: string): void;
export declare function insertUpdateFunctionGroup(ctx: Context, r: number, c: number, id?: string, calcChainSet?: Set<string>): void;
export declare function execfunction(ctx: Context, txt: string, r: number, c: number, id?: string, calcChainSet?: Set<string>, isrefresh?: boolean, notInsertFunc?: boolean): any[];
export declare function groupValuesRefresh(ctx: Context): void;
export declare function setFormulaCellInfoMap(ctx: Context, calcChains?: any[], data?: CellMatrix): void;
export declare function execFunctionGroup(ctx: Context, origin_r: number, origin_c: number, value: any, id?: string, data?: any, isForce?: boolean): void;
export declare function createFormulaRangeSelect(ctx: Context, select: {
    rangeIndex: number;
} & Rect): void;
export declare function createRangeHightlight(ctx: Context, inputInnerHtmlStr: string, ignoreRangeIndex?: number): void;
export declare function setCaretPosition(ctx: Context, textDom: HTMLElement, children: number, pos: number): void;
export declare function getrangeseleciton(): ParentNode | ChildNode | null | undefined;
export declare function rangeHightlightselected(ctx: Context, $editor: HTMLDivElement): void;
export declare function functionHTMLGenerate(txt: string): string;
export declare function handleFormulaInput(ctx: Context, $copyTo: HTMLDivElement | null | undefined, $editor: HTMLDivElement, kcode: number, preText?: string, refreshRangeSelect?: boolean): void;
export declare function israngeseleciton(ctx: Context, istooltip?: boolean): boolean;
export declare function functionStrChange(txt: string, type: string, rc: "row" | "col", orient: string | null, stindex: number, step: number): string;
export declare function rangeSetValue(ctx: Context, cellInput: HTMLDivElement, selected: any, fxInput?: HTMLDivElement | null): void;
export declare function onFormulaRangeDragEnd(ctx: Context): void;
export declare function rangeDrag(ctx: Context, e: MouseEvent, cellInput: HTMLDivElement, scrollLeft: number, scrollTop: number, container: HTMLDivElement, fxInput?: HTMLDivElement | null): void;
export declare function rangeDragColumn(ctx: Context, e: MouseEvent, cellInput: HTMLDivElement, scrollLeft: number, scrollTop: number, container: HTMLDivElement, fxInput?: HTMLDivElement | null): void;
export declare function rangeDragRow(ctx: Context, e: MouseEvent, cellInput: HTMLDivElement, scrollLeft: number, scrollTop: number, container: HTMLDivElement, fxInput?: HTMLDivElement | null): void;
export declare function functionCopy(ctx: Context, txt: string, mode: string, step: number): string;
