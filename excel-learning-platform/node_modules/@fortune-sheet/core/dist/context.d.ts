import { SheetConfig } from ".";
import { FormulaCache } from "./modules";
import { Hooks } from "./settings";
import { Sheet, Selection, Cell, CommentBox, Rect, Image, Presence, LinkCardProps, FilterOptions, RangeDialogProps, DataRegulationProps, ConditionRulesProps, GlobalCache } from "./types";
interface MutableRefObject<T> {
    current: T;
}
declare type RefValues = {
    globalCache: GlobalCache;
    cellInput: MutableRefObject<HTMLDivElement | null>;
    fxInput: MutableRefObject<HTMLDivElement | null>;
    canvas: MutableRefObject<HTMLCanvasElement | null>;
    cellArea: MutableRefObject<HTMLDivElement | null>;
    workbookContainer: MutableRefObject<HTMLDivElement | null>;
};
export declare type Context = {
    luckysheetfile: Sheet[];
    defaultcolumnNum: number;
    defaultrowNum: number;
    addDefaultRows: number;
    fullscreenmode: boolean;
    devicePixelRatio: number;
    commentBoxes?: CommentBox[];
    editingCommentBox?: CommentBox;
    hoveredCommentBox?: CommentBox;
    insertedImgs?: Image[];
    editingInsertedImgs?: Image;
    activeImg?: string;
    presences?: Presence[];
    showSearch?: boolean;
    showReplace?: boolean;
    linkCard?: LinkCardProps;
    rangeDialog?: RangeDialogProps;
    warnDialog?: string;
    currency?: string;
    dataVerification?: {
        selectStatus: boolean;
        selectRange: [];
        optionLabel_en: any;
        optionLabel_zh: any;
        optionLabel_zh_tw: any;
        optionLabel_es: any;
        optionLabel_hi: any;
        dataRegulation?: DataRegulationProps;
    };
    dataVerificationDropDownList?: boolean;
    conditionRules: ConditionRulesProps;
    contextMenu: {
        x?: number;
        y?: number;
        headerMenu?: boolean;
        pageX?: number;
        pageY?: number;
    };
    sheetTabContextMenu: {
        x?: number;
        y?: number;
        sheet?: Sheet;
        onRename?: () => void;
    };
    filterContextMenu?: {
        x: number;
        y: number;
        col: number;
        startRow: number;
        endRow: number;
        startCol: number;
        endCol: number;
        hiddenRows: number[];
        listBoxMaxHeight: number;
    };
    currentSheetId: string;
    calculateSheetId: string;
    config: SheetConfig;
    visibledatarow: number[];
    visibledatacolumn: number[];
    ch_width: number;
    rh_height: number;
    cellmainWidth: number;
    cellmainHeight: number;
    toolbarHeight: number;
    infobarHeight: number;
    calculatebarHeight: number;
    rowHeaderWidth: number;
    columnHeaderHeight: number;
    cellMainSrollBarSize: number;
    sheetBarHeight: number;
    statisticBarHeight: number;
    luckysheetTableContentHW: number[];
    defaultcollen: number;
    defaultrowlen: number;
    scrollLeft: number;
    scrollTop: number;
    sheetScrollRecord: Record<string, any>;
    luckysheet_select_status: boolean;
    luckysheet_select_save: Sheet["luckysheet_select_save"];
    luckysheet_selection_range: Sheet["luckysheet_selection_range"];
    formulaRangeHighlight: ({
        rangeIndex: number;
        backgroundColor: string;
    } & Rect)[];
    formulaRangeSelect: ({
        rangeIndex: number;
    } & Rect) | undefined;
    functionCandidates: any[];
    functionHint: string | null | undefined;
    luckysheet_copy_save?: {
        dataSheetId: string;
        copyRange: {
            row: number[];
            column: number[];
        }[];
        RowlChange: boolean;
        HasMC: boolean;
    };
    luckysheet_paste_iscut: boolean;
    filterchage: boolean;
    filterOptions?: FilterOptions;
    luckysheet_filter_save?: {
        row: number[];
        column: number[];
    } | undefined;
    filter: Record<string, {
        caljs: any;
        rowhidden: Record<string, number>;
        optionstate: boolean;
        str: number;
        edr: number;
        cindex: number;
        stc: number;
        edc: number;
    }>;
    luckysheet_sheet_move_status: boolean;
    luckysheet_sheet_move_data: any[];
    luckysheet_scroll_status: boolean;
    luckysheetcurrentisPivotTable: boolean;
    luckysheet_rows_selected_status: boolean;
    luckysheet_cols_selected_status: boolean;
    luckysheet_rows_change_size: boolean;
    luckysheet_rows_change_size_start: any[];
    luckysheet_cols_change_size: boolean;
    luckysheet_cols_change_size_start: any[];
    luckysheet_cols_freeze_drag: boolean;
    luckysheet_rows_freeze_drag: boolean;
    luckysheetCellUpdate: any[];
    luckysheet_shiftkeydown: boolean;
    luckysheet_shiftpositon: Selection | undefined;
    iscopyself: boolean;
    orderbyindex: number;
    luckysheet_model_move_state: boolean;
    luckysheet_model_xy: number[];
    luckysheet_model_move_obj: any;
    luckysheet_cell_selected_move: boolean;
    luckysheet_cell_selected_move_index: any[];
    luckysheet_cell_selected_extend: boolean;
    luckysheet_cell_selected_extend_index: any[];
    lang: string | null;
    chart_selection: any;
    zoomRatio: number;
    showGridLines: boolean;
    allowEdit: boolean;
    fontList: any[];
    defaultFontSize: number;
    luckysheetPaintModelOn: boolean;
    luckysheetPaintSingle: boolean;
    defaultCell: Cell;
    groupValuesRefreshData: any[];
    formulaCache: FormulaCache;
    hooks: Hooks;
    showSheetList?: Boolean;
    forceFormulaRef?: Boolean;
    sheetFocused: boolean;
    getRefs: () => RefValues;
};
export declare function defaultContext(refs: RefValues): Context;
export declare function getFlowdata(ctx?: Context, id?: string | null): import("./types").CellMatrix | null | undefined;
export declare function ensureSheetIndex(data: Sheet[], generateSheetId: () => string): void;
export declare function initSheetIndex(ctx: Context): void;
export declare function updateContextWithSheetData(ctx: Context, data: any[][]): void;
export declare function updateContextWithCanvas(ctx: Context, canvas: HTMLCanvasElement, placeholder: HTMLDivElement): void;
export {};
