import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Auth API
export const authAPI = {
  login: (email, password) => api.post('/auth/login', { email, password }),
  register: (username, email, password) => api.post('/auth/register', { username, email, password }),
  getCurrentUser: () => api.get('/auth/me'),
};

// Progress API
export const progressAPI = {
  getUserProgress: () => api.get('/progress'),
  updateChallengeProgress: (progressData) => api.post('/progress/challenge', progressData),
  getLeaderboard: () => api.get('/progress/leaderboard'),
};

// Challenges API
export const challengesAPI = {
  getLevels: () => api.get('/challenges/levels'),
  getChallengesByLevel: (levelId) => api.get(`/challenges/levels/${levelId}/challenges`),
  getChallenge: (challengeId) => api.get(`/challenges/challenges/${challengeId}`),
  validateSolution: (challengeId, userSolution) => 
    api.post(`/challenges/challenges/${challengeId}/validate`, { userSolution }),
  getLevelProgress: (levelId) => api.get(`/challenges/levels/${levelId}/progress`),
};

export default api;
