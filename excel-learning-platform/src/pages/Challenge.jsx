import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { challengesAPI } from '../services/api';
import ExcelSimulator from '../components/ExcelSimulator';
import { CheckCircle, XCircle, ArrowLeft, Lightbulb } from 'lucide-react';

const Challenge = () => {
  const { challengeId } = useParams();
  const { isAuthenticated } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  const [challenge, setChallenge] = useState(null);
  const [userSolution, setUserSolution] = useState({});
  const [feedback, setFeedback] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [allChallenges, setAllChallenges] = useState([]);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const fetchData = async () => {
      try {
        const [challengeResponse, allChallengesResponse] = await Promise.all([
          challengesAPI.getChallenge(challengeId),
          challengesAPI.getChallenges()
        ]);

        setChallenge(challengeResponse.data);
        setAllChallenges(allChallengesResponse.data);
      } catch (error) {
        console.error('Error fetching data:', error);
        navigate('/dashboard');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [challengeId, isAuthenticated, navigate]);

  const handleCellChange = (cellData) => {
    setUserSolution({
      ...userSolution,
      ...cellData,
      currentCell: Object.keys(cellData).pop() // Track last edited cell
    });
  };

  const handleCellSelect = (cellRef) => {
    // For navigation challenges, track the current selected cell
    setUserSolution({
      ...userSolution,
      currentCell: cellRef
    });
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    setFeedback(null);

    try {
      const response = await challengesAPI.validateSolution(challengeId, userSolution);
      setFeedback(response.data);
    } catch (error) {
      console.error('Error validating solution:', error);
      setFeedback({
        correct: false,
        message: 'Error validating solution. Please try again.',
        score: 0
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleReset = () => {
    setUserSolution({});
    setFeedback(null);
  };

  const getNextChallenge = () => {
    if (!allChallenges.length) return null;

    const currentIndex = allChallenges.findIndex(c => c.id === challengeId);
    if (currentIndex === -1 || currentIndex === allChallenges.length - 1) {
      return null; // No next challenge
    }

    return allChallenges[currentIndex + 1];
  };

  const handleContinueLearning = () => {
    const nextChallenge = getNextChallenge();
    if (nextChallenge) {
      navigate(`/challenge/${nextChallenge.id}`);
    } else {
      // If no next challenge, go back to the level page
      if (challenge && challenge.levelId) {
        navigate(`/level/${challenge.levelId}`);
      } else {
        navigate('/dashboard');
      }
    }
  };

  const handleBackToLevel = () => {
    if (challenge && challenge.levelId) {
      navigate(`/level/${challenge.levelId}`);
    } else {
      navigate('/dashboard');
    }
  };

  // Helper function to get translated content
  const getTranslatedContent = (item, field) => {
    const key = `challenge_${item.id}_${field}`;
    const translated = t(key);
    // If translation exists and is different from the key, use it; otherwise use original
    return translated !== key ? translated : item[field];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-excel-green"></div>
      </div>
    );
  }

  if (!challenge) {
    return (
      <div className="text-center py-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">{t('challengeNotFound')}</h2>
        <button
          onClick={handleBackToLevel}
          className="bg-excel-green text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          {t('backToDashboard')}
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <button
          onClick={handleBackToLevel}
          className="flex items-center space-x-2 text-excel-green hover:text-green-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{challenge && challenge.levelId ? t('backToLevel') : t('backToDashboard')}</span>
        </button>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{getTranslatedContent(challenge, 'title')}</h1>
              <p className="text-gray-600 mb-4">{getTranslatedContent(challenge, 'description')}</p>
            </div>
            <div className="text-right">
              <div className="bg-excel-green text-white px-3 py-1 rounded-full text-sm font-medium">
                {challenge.points} points
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex items-start space-x-2">
              <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">{t('instructions')}:</h3>
                <p className="text-blue-800 mb-3">{getTranslatedContent(challenge, 'instructions')}</p>
                {challenge.id === '1-1' && (
                  <div className="text-sm text-blue-700 bg-blue-100 rounded p-2">
                    💡 <strong>{t('tip')}:</strong> {t('navigationTip')}
                  </div>
                )}
                {challenge.id === '1-3' && (
                  <div className="text-sm text-blue-700 bg-blue-100 rounded p-2">
                    💡 <strong>{t('tip')}:</strong> {t('formattingTip')}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Feedback */}
          {feedback && (
            <div className={`border rounded-lg p-4 mb-4 ${
              feedback.correct
                ? 'bg-green-50 border-green-200'
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-start space-x-2">
                {feedback.correct ? (
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                )}
                <div>
                  <p className={`font-semibold ${
                    feedback.correct ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {feedback.message}
                  </p>
                  {feedback.correct && (
                    <p className="text-green-800 mt-1">
                      {t('youEarned')} {feedback.score} {t('pointsEarned')}!
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Excel Simulator */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('excelWorkspace')}</h2>
        <ExcelSimulator
          onCellChange={handleCellChange}
          onCellSelect={handleCellSelect}
          initialData={userSolution}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        <button
          onClick={handleReset}
          className="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors"
        >
          {t('reset')}
        </button>

        <div className="space-x-4">
          {feedback?.correct && (
            <button
              onClick={handleContinueLearning}
              className="bg-excel-green text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              {getNextChallenge() ? t('nextChallenge') : t('backToDashboard')}
            </button>
          )}

          <button
            onClick={handleSubmit}
            disabled={submitting || feedback?.correct}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? t('checkingSolution') : t('submitSolution')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Challenge;
