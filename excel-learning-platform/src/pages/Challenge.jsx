import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { challengesAPI } from '../services/api';
import ExcelSimulator from '../components/ExcelSimulator';
import { CheckCircle, XCircle, ArrowLeft, Lightbulb } from 'lucide-react';

const Challenge = () => {
  const { challengeId } = useParams();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  
  const [challenge, setChallenge] = useState(null);
  const [userSolution, setUserSolution] = useState({});
  const [feedback, setFeedback] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const fetchChallenge = async () => {
      try {
        const response = await challengesAPI.getChallenge(challengeId);
        setChallenge(response.data);
      } catch (error) {
        console.error('Error fetching challenge:', error);
        navigate('/dashboard');
      } finally {
        setLoading(false);
      }
    };

    fetchChallenge();
  }, [challengeId, isAuthenticated, navigate]);

  const handleCellChange = (cellData) => {
    setUserSolution({
      ...userSolution,
      ...cellData,
      currentCell: Object.keys(cellData).pop() // Track last edited cell
    });
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    setFeedback(null);

    try {
      const response = await challengesAPI.validateSolution(challengeId, userSolution);
      setFeedback(response.data);
    } catch (error) {
      console.error('Error validating solution:', error);
      setFeedback({
        correct: false,
        message: 'Error validating solution. Please try again.',
        score: 0
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleReset = () => {
    setUserSolution({});
    setFeedback(null);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-excel-green"></div>
      </div>
    );
  }

  if (!challenge) {
    return (
      <div className="text-center py-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Challenge Not Found</h2>
        <button
          onClick={() => navigate('/dashboard')}
          className="bg-excel-green text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <button
          onClick={() => navigate('/dashboard')}
          className="flex items-center space-x-2 text-excel-green hover:text-green-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Dashboard</span>
        </button>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{challenge.title}</h1>
              <p className="text-gray-600 mb-4">{challenge.description}</p>
            </div>
            <div className="text-right">
              <div className="bg-excel-green text-white px-3 py-1 rounded-full text-sm font-medium">
                {challenge.points} points
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex items-start space-x-2">
              <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">Instructions:</h3>
                <p className="text-blue-800">{challenge.instructions}</p>
              </div>
            </div>
          </div>

          {/* Feedback */}
          {feedback && (
            <div className={`border rounded-lg p-4 mb-4 ${
              feedback.correct 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-start space-x-2">
                {feedback.correct ? (
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                )}
                <div>
                  <p className={`font-semibold ${
                    feedback.correct ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {feedback.message}
                  </p>
                  {feedback.correct && (
                    <p className="text-green-800 mt-1">
                      You earned {feedback.score} points!
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Excel Simulator */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Excel Workspace</h2>
        <ExcelSimulator
          onCellChange={handleCellChange}
          initialData={userSolution}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        <button
          onClick={handleReset}
          className="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors"
        >
          Reset
        </button>

        <div className="space-x-4">
          {feedback?.correct && (
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-excel-green text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              Continue Learning
            </button>
          )}
          
          <button
            onClick={handleSubmit}
            disabled={submitting || feedback?.correct}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? 'Checking...' : 'Submit Solution'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Challenge;
