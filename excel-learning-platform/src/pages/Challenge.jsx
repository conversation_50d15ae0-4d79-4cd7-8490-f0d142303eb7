import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { challengesAPI } from '../services/api';
import ReactSpreadsheetWrapper from '../components/ReactSpreadsheetWrapper';
import { CheckCircle, XCircle, ArrowLeft, Lightbulb } from 'lucide-react';

const Challenge = () => {
  const { challengeId } = useParams();
  const { isAuthenticated } = useAuth();
  const { t, language } = useLanguage();
  const navigate = useNavigate();

  const [challenge, setChallenge] = useState(null);
  const [userSolution, setUserSolution] = useState({});
  const [feedback, setFeedback] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [allChallenges, setAllChallenges] = useState([]);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const fetchData = async () => {
      try {
        const [challengeResponse, allChallengesResponse] = await Promise.all([
          challengesAPI.getChallenge(challengeId),
          challengesAPI.getChallenges()
        ]);

        setChallenge(challengeResponse.data);
        setAllChallenges(allChallengesResponse.data);
      } catch (error) {
        console.error('Error fetching data:', error);
        navigate('/dashboard');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [challengeId, isAuthenticated, navigate]);

  const handleCellChange = useCallback((cellData) => {
    if (!cellData || typeof cellData !== 'object') return;
    if (Object.keys(cellData).length === 0) return;

    setUserSolution(prevSolution => {
      const newSolution = {...prevSolution, ...cellData};

      const changedCells = Object.keys(cellData).filter(key =>
        key !== '_ref' && cellData[key] !== prevSolution[key]
      );

      if (changedCells.length > 0) {
        newSolution.currentCell = changedCells[0];
      }

      return newSolution;
    });
  }, []);

  const handleCellSelect = useCallback((cellRef) => {
    setUserSolution(prevSolution => ({
      ...prevSolution,
      currentCell: cellRef
    }));
  }, []);

  // Local validation function
    const validateSolution = (challenge, userSolution) => {
      const normalizeValue = (value) => {
        if (value === null || value === undefined) return '';
        return String(value).trim();
      };

      const expected = challenge.solutionData;

      switch (challenge.id) {
        case '1-1': // Navigate interface - check selected cell
          return userSolution.currentCell === expected.targetCell || 
                 userSolution[expected.targetCell] !== undefined;

        case '1-2': // Enter data - check final result
          return normalizeValue(userSolution.A1) === normalizeValue(expected.cellA1);

        case '1-3': // Format cell - check for bold and yellow background
          const formatData = userSolution['A1_format'];
          return formatData && 
                 formatData.bold === true && 
                 formatData.backgroundColor.toLowerCase() === 'yellow';

        case '1-4': // Simple calculation
          const userA1 = normalizeValue(userSolution.A1);
          const userB1 = normalizeValue(userSolution.B1);
          const userC1 = normalizeValue(userSolution.C1);

          // Validate numeric values
          const valuesValid = 
            Number(userA1) === Number(expected.cellA1) && 
            Number(userB1) === Number(expected.cellB1);

          // Validate formula or result
          const formulaRegex = /^\s*=\s*[aA]1\s*\+\s*[bB]1\s*$/;
          const resultValid = 
            userC1 === expected.cellC1 || 
            formulaRegex.test(userC1) || 
            Number(userC1) === 30; // 10+20 result

          return valuesValid && resultValid;

        // Level 2 challenges
        case '2-1': // SUM function
          const a6Formula = normalizeValue(userSolution.A6);
          const sumRegex = /^\s*=\s*SUM\s*\(\s*[aA]1\s*:\s*[aA]5\s*\)\s*$/i;
          return sumRegex.test(a6Formula) || Number(a6Formula) === 15;

        case '2-2': // AVERAGE function
          const b6Formula = normalizeValue(userSolution.B6);
          const avgRegex = /^\s*=\s*AVERAGE\s*\(\s*[aA]1\s*:\s*[aA]5\s*\)\s*$/i;
          return avgRegex.test(b6Formula) || Number(b6Formula) === 3;

        case '2-3': // IF function
          const b1Formula = normalizeValue(userSolution.B1);
          const ifRegex = /^\s*=\s*IF\s*\(\s*[aA]1\s*>\s*10\s*,\s*["']?High["']?\s*,\s*["']?Low["']?\s*\)\s*$/i;
          return ifRegex.test(b1Formula);

        case '2-4': // COUNT function
          const b10Formula = normalizeValue(userSolution.B10);
          const countRegex = /^\s*=\s*COUNT\s*\(\s*[aA]1\s*:\s*[aA]10\s*\)\s*$/i;
          return countRegex.test(b10Formula);

        default:
          return false;
      }
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    setFeedback(null);

    try {
      // Try backend validation first, fall back to local validation
      let result;
      try {
        const response = await challengesAPI.validateSolution(challengeId, userSolution);
        result = response.data;
      } catch (backendError) {
        console.log('Backend not available, using local validation');
        // Use local validation
        const isCorrect = validateSolution(challenge, userSolution);
        const score = isCorrect ? challenge.points : 0;
        result = {
          correct: isCorrect,
          message: isCorrect ? t('correctSolution') : t('notCorrect'),
          score: score
        };
      }

      setFeedback(result);
    } catch (error) {
      console.error('Error validating solution:', error);
      setFeedback({
        correct: false,
        message: t('submitError'),
        score: 0
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleReset = () => {
    setUserSolution({});
    setFeedback(null);
  };

  // Memoize the next challenge calculation to prevent unnecessary re-renders
  const nextChallenge = useMemo(() => {
    if (!allChallenges.length) return null;

    const currentIndex = allChallenges.findIndex(c => c.id === challengeId);
    if (currentIndex === -1 || currentIndex === allChallenges.length - 1) {
      return null; // No next challenge
    }

    return allChallenges[currentIndex + 1];
  }, [allChallenges, challengeId]);

  const getNextChallenge = () => nextChallenge;

  // Memoize button texts to prevent re-renders
  const continueButtonText = useMemo(() => {
    return nextChallenge ? t('nextChallenge') : t('backToDashboard');
  }, [nextChallenge, t]);

  const submitButtonText = useMemo(() => {
    return submitting ? t('checkingSolution') : t('submitSolution');
  }, [submitting, t]);

  const handleContinueLearning = () => {
    const nextChallenge = getNextChallenge();
    if (nextChallenge) {
      navigate(`/challenge/${nextChallenge.id}`);
    } else {
      // If no next challenge, go back to the level page
      if (challenge && challenge.levelId) {
        navigate(`/level/${challenge.levelId}`);
      } else {
        navigate('/dashboard');
      }
    }
  };

  const handleBackToLevel = () => {
    if (challenge && challenge.levelId) {
      navigate(`/level/${challenge.levelId}`);
    } else {
      navigate('/dashboard');
    }
  };

  // Helper function to get translated content
  const getTranslatedContent = (item, field) => {
    // For instructions, check if there's a language-specific field
    if (field === 'instructions') {
      if (language === 'en' && item.instructionsEn) {
        return item.instructionsEn;
      } else if (language === 'zh' && item.instructions) {
        return item.instructions;
      }
    }

    const key = `challenge_${item.id}_${field}`;
    const translated = t(key);
    // If translation exists and is different from the key, use it; otherwise use original
    return translated !== key ? translated : item[field];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-excel-green"></div>
      </div>
    );
  }

  if (!challenge) {
    return (
      <div className="text-center py-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">{t('challengeNotFound')}</h2>
        <button
          onClick={handleBackToLevel}
          className="bg-excel-green text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          {t('backToDashboard')}
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <button
          onClick={handleBackToLevel}
          className="flex items-center space-x-2 text-excel-green hover:text-green-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{challenge && challenge.levelId ? t('backToLevel') : t('backToDashboard')}</span>
        </button>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{getTranslatedContent(challenge, 'title')}</h1>
              <p className="text-gray-600 mb-4">{getTranslatedContent(challenge, 'description')}</p>
            </div>
            <div className="text-right">
              <div className="bg-excel-green text-white px-3 py-1 rounded-full text-sm font-medium">
                {challenge.points} {t('points')}
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex items-start space-x-2">
              <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">{t('instructions')}:</h3>
                <div className="text-blue-800 mb-3 whitespace-pre-line">{getTranslatedContent(challenge, 'instructions')}</div>
                {challenge.id === '1-1' && (
                  <div className="text-sm text-blue-700 bg-blue-100 rounded p-2">
                    💡 <strong>{t('tip')}:</strong> {t('navigationTip')}
                  </div>
                )}
                {challenge.id === '1-3' && (
                  <div className="text-sm text-blue-700 bg-blue-100 rounded p-2">
                    💡 <strong>{t('tip')}:</strong> {t('formattingTip')}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Feedback */}
          {feedback && (
            <div className={`border rounded-lg p-4 mb-4 ${
              feedback.correct
                ? 'bg-green-50 border-green-200'
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-start space-x-2">
                {feedback.correct ? (
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                )}
                <div>
                  <p className={`font-semibold ${
                    feedback.correct ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {feedback.message}
                  </p>
                  {feedback.correct && (
                    <p className="text-green-800 mt-1">
                      {t('youEarnedPoints', [feedback.score])}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Excel Simulator */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('excelWorkspace')}</h2>
        <div className="excel-simulator-container" style={{width: '100%', minWidth: '800px'}}>
          <ReactSpreadsheetWrapper
            onCellChange={handleCellChange}
            onCellSelect={handleCellSelect}
            initialData={userSolution}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        <button
          onClick={handleReset}
          className="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors"
        >
          {t('reset')}
        </button>

        <div className="space-x-4">
          {feedback?.correct && (
            <button
              onClick={handleContinueLearning}
              className="bg-excel-green text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              {continueButtonText}
            </button>
          )}

          <button
            onClick={handleSubmit}
            disabled={submitting || feedback?.correct}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitButtonText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Challenge;
