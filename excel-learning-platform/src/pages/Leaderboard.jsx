import React, { useState, useEffect } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { progressAPI } from '../services/api';
import { Trophy, Medal, Award, Users } from 'lucide-react';

const Leaderboard = () => {
  const { t } = useLanguage();
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        const response = await progressAPI.getLeaderboard();
        setLeaderboard(response.data);
      } catch (error) {
        console.error('Error fetching leaderboard:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboard();
  }, []);

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-6 w-6 text-yellow-500" />;
      case 2:
        return <Medal className="h-6 w-6 text-gray-400" />;
      case 3:
        return <Award className="h-6 w-6 text-amber-600" />;
      default:
        return <div className="w-6 h-6 flex items-center justify-center bg-gray-200 rounded-full text-sm font-bold text-gray-600">{rank}</div>;
    }
  };

  const getRankBadge = (rank) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white';
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600 text-white';
      default:
        return 'bg-white border border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-excel-green"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <Trophy className="h-16 w-16 text-excel-green" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('leaderboard')}</h1>
        <p className="text-gray-600">{t('seeHowYouRank')}</p>
      </div>

      {/* Stats */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex items-center justify-center space-x-8">
          <div className="text-center">
            <Users className="h-8 w-8 text-excel-green mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">{leaderboard.length}</p>
            <p className="text-sm text-gray-600">{t('totalLearners')}</p>
          </div>
          <div className="text-center">
            <Trophy className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">
              {leaderboard[0]?.totalPoints || 0}
            </p>
            <p className="text-sm text-gray-600">{t('topScore')}</p>
          </div>
          <div className="text-center">
            <Award className="h-8 w-8 text-blue-500 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">
              {leaderboard[0]?.completedChallenges || 0}
            </p>
            <p className="text-sm text-gray-600">{t('mostChallenges')}</p>
          </div>
        </div>
      </div>

      {/* Leaderboard */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="bg-excel-green text-white p-4">
          <h2 className="text-xl font-semibold">{t('topPerformers')}</h2>
        </div>

        {leaderboard.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>{t('noLearnersYet')}</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {leaderboard.map((user, index) => {
              const rank = index + 1;
              const isTopThree = rank <= 3;

              return (
                <div
                  key={index}
                  className={`p-4 flex items-center justify-between hover:bg-gray-50 transition-colors ${
                    isTopThree ? getRankBadge(rank) : ''
                  }`}
                >
                  <div className="flex items-center space-x-4">
                    {getRankIcon(rank)}

                    <div>
                      <h3 className={`font-semibold ${
                        isTopThree ? 'text-white' : 'text-gray-900'
                      }`}>
                        {user.username}
                      </h3>
                      <p className={`text-sm ${
                        isTopThree ? 'text-white opacity-90' : 'text-gray-600'
                      }`}>
                        {t('joined')} {new Date(user.joinDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className={`text-lg font-bold ${
                      isTopThree ? 'text-white' : 'text-excel-green'
                    }`}>
                      {user.totalPoints} {t('pts')}
                    </div>
                    <div className={`text-sm ${
                      isTopThree ? 'text-white opacity-90' : 'text-gray-600'
                    }`}>
                      {user.completedChallenges} {t('challenges')}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Call to Action */}
      {leaderboard.length > 0 && (
        <div className="mt-8 text-center">
          <p className="text-gray-600 mb-4">
            {t('wantToClimb')}
          </p>
          <a
            href="/dashboard"
            className="inline-flex items-center space-x-2 bg-excel-green text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
          >
            <Trophy className="h-4 w-4" />
            <span>{t('continueLearning')}</span>
          </a>
        </div>
      )}
    </div>
  );
};

export default Leaderboard;
