import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const SafeHome = () => {
  try {
    const { t, language, toggleLanguage } = useLanguage();

    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <div style={{ marginBottom: '20px' }}>
          <button 
            onClick={toggleLanguage}
            style={{ padding: '10px', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '5px' }}
          >
            {language === 'en' ? '中文' : 'English'}
          </button>
        </div>
        <h1 style={{ color: 'green' }}>{t('homeTitle') || 'Excel Learning Platform'}</h1>
        <p>{t('homeSubtitle') || 'Master Excel skills through interactive challenges'}</p>
        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0', borderRadius: '5px' }}>
          <h2>{t('whyChoose') || 'Why Choose Excel Master?'}</h2>
          <ul>
            <li>{t('interactiveLearningTitle') || 'Interactive Learning'}</li>
            <li>{t('gamifiedChallengesTitle') || 'Gamified Challenges'}</li>
            <li>{t('trackProgressTitle') || 'Track Progress'}</li>
          </ul>
        </div>
      </div>
    );
  } catch (error) {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1 style={{ color: 'red' }}>Error: {error.message}</h1>
        <p>There was an error with the language context.</p>
        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#ffe6e6', borderRadius: '5px' }}>
          <h2>Fallback Content:</h2>
          <ul>
            <li>Excel Learning Platform</li>
            <li>Interactive Learning</li>
            <li>Gamified Challenges</li>
          </ul>
        </div>
      </div>
    );
  }
};

export default SafeHome;
