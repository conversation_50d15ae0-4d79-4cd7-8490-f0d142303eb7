import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const TestChallenge4 = () => {
  const { t } = useLanguage();
  const [testResults, setTestResults] = useState([]);

  const validateSolution = (challenge, userSolution) => {
    const normalizeValue = (value) => {
      if (value === null || value === undefined) return '';
      return String(value).trim();
    };

    const expected = challenge.solutionData;
    
    console.log('=== 1-4 Validation Test ===');
    console.log('User solution:', JSON.stringify(userSolution, null, 2));
    console.log('Expected solution:', JSON.stringify(expected, null, 2));
    
    // Check A1
    const userA1 = normalizeValue(userSolution.A1);
    const expectedA1 = expected.cellA1;
    const a1Valid = Number(userA1) === Number(expectedA1);
    console.log(`A1: "${userA1}" (${typeof userA1}) vs "${expectedA1}" (${typeof expectedA1}) = ${a1Valid}`);
    
    // Check B1
    const userB1 = normalizeValue(userSolution.B1);
    const expectedB1 = expected.cellB1;
    const b1Valid = Number(userB1) === Number(expectedB1);
    console.log(`B1: "${userB1}" (${typeof userB1}) vs "${expectedB1}" (${typeof expectedB1}) = ${b1Valid}`);
    
    // Check C1 - more flexible validation
    const userC1 = normalizeValue(userSolution.C1);
    const expectedC1 = expected.cellC1;
    const c1Valid = userC1 === expectedC1 ||
                    userC1 === '=A1+B1' ||
                    userC1.toLowerCase() === '=a1+b1' ||
                    userC1 === '= A1+B1' ||
                    userC1 === '= A1 + B1' ||
                    userC1 === '=A1 + B1' ||
                    Number(userC1) === 30; // Result of 10+20
    console.log(`C1: "${userC1}" (${typeof userC1}) vs "${expectedC1}" (${typeof expectedC1}) = ${c1Valid}`);
    
    const result = a1Valid && b1Valid && c1Valid;
    console.log(`Final result: ${result}`);
    console.log('=== End Test ===');

    return result;
  };

  const runTests = () => {
    const challenge = {
      id: '1-4',
      solutionData: {
        cellA1: 10,
        cellB1: 20,
        cellC1: '=A1+B1'
      }
    };

    const testCases = [
      {
        name: 'Correct solution',
        userSolution: { A1: 10, B1: 20, C1: '=A1+B1' },
        expected: true
      },
      {
        name: 'String numbers',
        userSolution: { A1: '10', B1: '20', C1: '=A1+B1' },
        expected: true
      },
      {
        name: 'Lowercase formula',
        userSolution: { A1: 10, B1: 20, C1: '=a1+b1' },
        expected: true
      },
      {
        name: 'Formula with spaces',
        userSolution: { A1: 10, B1: 20, C1: '=A1 + B1' },
        expected: true
      },
      {
        name: 'Result as number',
        userSolution: { A1: 10, B1: 20, C1: 30 },
        expected: true
      },
      {
        name: 'Result as string',
        userSolution: { A1: 10, B1: 20, C1: '30' },
        expected: true
      },
      {
        name: 'Wrong A1',
        userSolution: { A1: 5, B1: 20, C1: '=A1+B1' },
        expected: false
      },
      {
        name: 'Wrong B1',
        userSolution: { A1: 10, B1: 15, C1: '=A1+B1' },
        expected: false
      },
      {
        name: 'Wrong C1',
        userSolution: { A1: 10, B1: 20, C1: '=A1*B1' },
        expected: false
      }
    ];

    const results = testCases.map(testCase => {
      const actual = validateSolution(challenge, testCase.userSolution);
      return {
        ...testCase,
        actual,
        passed: actual === testCase.expected
      };
    });

    setTestResults(results);
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Challenge 1-4 Validation Test</h1>
      
      <button
        onClick={runTests}
        className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 mb-6"
      >
        Run Tests
      </button>

      {testResults.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold">Test Results</h2>
          {testResults.map((result, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border ${
                result.passed
                  ? 'bg-green-50 border-green-200'
                  : 'bg-red-50 border-red-200'
              }`}
            >
              <div className="flex justify-between items-center">
                <h3 className="font-semibold">{result.name}</h3>
                <span className={`px-2 py-1 rounded text-sm ${
                  result.passed
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {result.passed ? 'PASS' : 'FAIL'}
                </span>
              </div>
              <div className="mt-2 text-sm">
                <div>Input: {JSON.stringify(result.userSolution)}</div>
                <div>Expected: {result.expected.toString()}</div>
                <div>Actual: {result.actual.toString()}</div>
              </div>
            </div>
          ))}
          
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-900">Summary</h3>
            <p className="text-blue-800">
              {testResults.filter(r => r.passed).length} / {testResults.length} tests passed
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TestChallenge4;
