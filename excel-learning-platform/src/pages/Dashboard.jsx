import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { challengesAPI, progressAPI } from '../services/api';
import { Trophy, Star, Clock, Target, Lock, CheckCircle } from 'lucide-react';

const Dashboard = () => {
  const { user, isAuthenticated } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [levels, setLevels] = useState([]);
  const [userProgress, setUserProgress] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const fetchData = async () => {
      try {
        const [levelsResponse, progressResponse] = await Promise.all([
          challengesAPI.getLevels(),
          progressAPI.getUserProgress()
        ]);

        setLevels(levelsResponse.data);
        setUserProgress(progressResponse.data.progress);
        setStats(progressResponse.data.stats);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [isAuthenticated, navigate]);

  const getLevelProgress = (levelId) => {
    return userProgress.filter(p => p.levelId === levelId && p.completed).length;
  };

  const isLevelUnlocked = (level) => {
    if (level.orderIndex === 1) return true;

    const previousLevel = levels.find(l => l.orderIndex === level.orderIndex - 1);
    if (!previousLevel) return false;

    // Check if previous level is completed (simplified logic)
    const previousLevelProgress = getLevelProgress(previousLevel.id);
    return previousLevelProgress >= 4; // Assuming 4 challenges per level
  };

  // Helper function to get translated content
  const getTranslatedContent = (item, field) => {
    const key = `level_${item.id}_${field}`;
    const translated = t(key);
    // If translation exists and is different from the key, use it; otherwise use original
    return translated !== key ? translated : item[field];
  };

  // Handle practice mode
  const handlePracticeMode = () => {
    // Navigate to the first available challenge for practice
    const firstLevel = levels.find(level => isLevelUnlocked(level));
    if (firstLevel) {
      navigate(`/level/${firstLevel.id}`);
    } else {
      // If no levels are unlocked, go to level 1
      navigate('/level/1');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-excel-green"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Welcome Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {t('welcomeBackDashboard')}, {user?.username}! 👋
        </h1>
        <p className="text-gray-600">{t('continueYourExcelLearningJourney')}</p>
      </div>

      {/* Stats Cards */}
      <div className="grid md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">{t('totalPoints')}</p>
              <p className="text-2xl font-bold text-excel-green">{stats.totalPoints || 0}</p>
            </div>
            <Trophy className="h-8 w-8 text-excel-green" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">{t('completedChallenges')}</p>
              <p className="text-2xl font-bold text-blue-600">{stats.completedChallenges || 0}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">{t('currentLevel')}</p>
              <p className="text-2xl font-bold text-purple-600">{stats.currentLevel || 1}</p>
            </div>
            <Star className="h-8 w-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">{t('progress')}</p>
              <p className="text-2xl font-bold text-orange-600">{stats.completionPercentage || 0}%</p>
            </div>
            <Target className="h-8 w-8 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('overallProgress')}</h2>
        <div className="progress-bar h-4">
          <div
            className="progress-fill"
            style={{ width: `${stats.completionPercentage || 0}%` }}
          ></div>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          {stats.completedChallenges || 0} {t('of')} {stats.totalChallenges || 0} {t('challengesCompleted')}
        </p>
      </div>

      {/* Levels Grid */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">{t('learningLevels')}</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {levels.map((level) => {
            const isUnlocked = isLevelUnlocked(level);
            const levelProgress = getLevelProgress(level.id);
            const totalChallenges = 4; // Simplified - would be dynamic in real app
            const progressPercentage = (levelProgress / totalChallenges) * 100;

            return (
              <div
                key={level.id}
                className={`level-card p-6 ${
                  isUnlocked
                    ? 'cursor-pointer hover:shadow-xl transform hover:-translate-y-1 transition-all'
                    : 'opacity-50 cursor-not-allowed'
                }`}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-white">Level {level.orderIndex}</h3>
                  {isUnlocked ? (
                    <CheckCircle className="h-6 w-6 text-white" />
                  ) : (
                    <Lock className="h-6 w-6 text-white" />
                  )}
                </div>

                <h4 className="text-lg font-semibold text-white mb-2">{getTranslatedContent(level, 'title')}</h4>
                <p className="text-green-100 text-sm mb-4">{getTranslatedContent(level, 'description')}</p>

                <div className="mb-4">
                  <div className="flex justify-between text-white text-sm mb-1">
                    <span>{t('progress')}</span>
                    <span>{levelProgress}/{totalChallenges}</span>
                  </div>
                  <div className="bg-green-800 rounded-full h-2">
                    <div
                      className="bg-white h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progressPercentage}%` }}
                    ></div>
                  </div>
                </div>

                {isUnlocked ? (
                  <Link
                    to={`/level/${level.id}`}
                    className="block w-full bg-white text-excel-green text-center py-2 rounded-md font-semibold hover:bg-green-50 transition-colors"
                  >
                    {levelProgress > 0 ? t('continueLevel') : t('startLevel')}
                  </Link>
                ) : (
                  <div className="w-full bg-green-800 text-white text-center py-2 rounded-md font-semibold">
                    {t('locked')}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('quickActions')}</h2>
        <div className="flex flex-wrap gap-4">
          <Link
            to="/leaderboard"
            className="flex items-center space-x-2 bg-excel-green text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
          >
            <Trophy className="h-4 w-4" />
            <span>{t('viewLeaderboard')}</span>
          </Link>

          <button
            onClick={handlePracticeMode}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            <Clock className="h-4 w-4" />
            <span>{t('practiceMode')}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
