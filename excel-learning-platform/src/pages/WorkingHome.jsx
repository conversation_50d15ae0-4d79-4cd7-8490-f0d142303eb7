import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';

const WorkingHome = () => {
  const { isAuthenticated } = useAuth();
  const { t } = useLanguage();

  return (
    <div className="max-w-6xl mx-auto">
      {/* Hero Section */}
      <div className="text-center py-16">
        <h1 className="text-5xl font-bold text-gray-900 mb-6">
          {t('heroTitle')}
          <span className="text-excel-green"> {t('heroTitleHighlight')}</span>
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          {t('heroDescription')}
        </p>

        <div className="flex justify-center space-x-4">
          {isAuthenticated ? (
            <Link
              to="/dashboard"
              className="bg-excel-green text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
            >
              <span>{t('continueLearning')}</span>
            </Link>
          ) : (
            <>
              <Link
                to="/register"
                className="bg-excel-green text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
              >
                <span>{t('startLearning')}</span>
              </Link>
              <Link
                to="/login"
                className="border-2 border-excel-green text-excel-green px-8 py-3 rounded-lg font-semibold hover:bg-excel-green hover:text-white transition-colors"
              >
                {t('signIn')}
              </Link>
            </>
          )}
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
          {t('whyChoose')}
        </h2>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              {t('interactiveLearningTitle')}
            </h3>
            <p className="text-gray-600">
              {t('interactiveLearningDesc')}
            </p>
          </div>
          <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              {t('gamifiedChallengesTitle')}
            </h3>
            <p className="text-gray-600">
              {t('gamifiedChallengesDesc')}
            </p>
          </div>
          <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              {t('trackProgressTitle')}
            </h3>
            <p className="text-gray-600">
              {t('trackProgressDesc')}
            </p>
          </div>
          <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              {t('communityLearningTitle')}
            </h3>
            <p className="text-gray-600">
              {t('communityLearningDesc')}
            </p>
          </div>
        </div>
      </div>

      {/* Learning Path Preview */}
      <div className="py-16 bg-gray-50 rounded-lg">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {t('learningJourney')}
          </h2>
          <p className="text-lg text-gray-600">
            {t('learningJourneyDesc')}
          </p>
        </div>

        <div className="grid md:grid-cols-5 gap-4">
          {[
            { level: 1, title: t('level1Title'), description: t('interfaceNavigation') },
            { level: 2, title: t('level2Title'), description: t('sumAverageIf') },
            { level: 3, title: t('level3Title'), description: t('sortingFiltering') },
            { level: 4, title: t('level4Title'), description: t('vlookupIndexMatch') },
            { level: 5, title: t('level5Title'), description: t('pivotTablesCharts') }
          ].map((level, index) => (
            <div key={index} className="text-center p-4 bg-white rounded-lg shadow-sm">
              <div className="w-12 h-12 bg-excel-green text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">
                {level.level}
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">{level.title}</h4>
              <p className="text-sm text-gray-600">{level.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WorkingHome;
