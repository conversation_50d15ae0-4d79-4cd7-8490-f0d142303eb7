import React, { useState } from 'react';

const TestValidation = () => {
  const [testData, setTestData] = useState({
    A1: 10,
    B1: 20,
    C1: '=A1+B1'
  });
  const [result, setResult] = useState(null);

  const testValidation = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/challenges/1-4/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        },
        body: JSON.stringify({
          userSolution: testData
        })
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: error.message });
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Validation Test</h1>
      
      <div className="mb-4">
        <h2 className="text-lg font-semibold mb-2">Test Data:</h2>
        <pre className="bg-gray-100 p-4 rounded">
          {JSON.stringify(testData, null, 2)}
        </pre>
      </div>

      <button
        onClick={testValidation}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        Test Validation
      </button>

      {result && (
        <div className="mt-4">
          <h2 className="text-lg font-semibold mb-2">Result:</h2>
          <pre className="bg-gray-100 p-4 rounded">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-8">
        <h2 className="text-lg font-semibold mb-2">Edit Test Data:</h2>
        <div className="space-y-2">
          <div>
            <label className="block text-sm font-medium">A1:</label>
            <input
              type="text"
              value={testData.A1}
              onChange={(e) => setTestData({...testData, A1: e.target.value})}
              className="border border-gray-300 rounded px-2 py-1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium">B1:</label>
            <input
              type="text"
              value={testData.B1}
              onChange={(e) => setTestData({...testData, B1: e.target.value})}
              className="border border-gray-300 rounded px-2 py-1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium">C1:</label>
            <input
              type="text"
              value={testData.C1}
              onChange={(e) => setTestData({...testData, C1: e.target.value})}
              className="border border-gray-300 rounded px-2 py-1"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestValidation;
