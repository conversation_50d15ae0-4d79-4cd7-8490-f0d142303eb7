import React, { useState, useEffect, useCallback } from 'react';
import Spreadsheet from 'react-spreadsheet';
import { useLanguage } from '../contexts/LanguageContext';
import {
  Bold, Italic, Underline, Palette, AlignLeft, AlignCenter, AlignRight,
  Type, Square, Hash, Percent, DollarSign, Undo, Redo
} from 'lucide-react';

const ReactSpreadsheetWrapper = ({ onCellChange, onCellSelect, initialData = {}, readOnly = false }) => {
  const { t } = useLanguage();

  // Convert our data format to react-spreadsheet format
  const convertToSpreadsheetData = (data) => {
    const spreadsheetData = [];
    const maxRow = 15;
    const maxCol = 10;

    // Initialize empty grid
    for (let row = 0; row < maxRow; row++) {
      spreadsheetData[row] = [];
      for (let col = 0; col < maxCol; col++) {
        spreadsheetData[row][col] = { value: '' };
      }
    }

    // Fill with actual data
    Object.keys(data).forEach(cellRef => {
      if (cellRef.includes('_format')) return; // Skip format data

      const match = cellRef.match(/^([A-Z]+)(\d+)$/);
      if (match) {
        const colStr = match[1];
        const rowNum = parseInt(match[2]) - 1; // Convert to 0-based

        // Convert column letter to number (A=0, B=1, etc.)
        let col = 0;
        for (let i = 0; i < colStr.length; i++) {
          col = col * 26 + (colStr.charCodeAt(i) - 64);
        }
        col -= 1; // Convert to 0-based

        if (rowNum >= 0 && rowNum < maxRow && col >= 0 && col < maxCol) {
          spreadsheetData[rowNum][col] = { value: data[cellRef] };
        }
      }
    });

    return spreadsheetData;
  };

  // Convert spreadsheet data back to our format
  const convertFromSpreadsheetData = (spreadsheetData) => {
    const data = {};

    spreadsheetData.forEach((row, rowIndex) => {
      row.forEach((cell, colIndex) => {
        if (cell && cell.value !== undefined && cell.value !== '') {
          const colLetter = String.fromCharCode(65 + colIndex);
          const cellRef = `${colLetter}${rowIndex + 1}`;
          data[cellRef] = cell.value;
        }
      });
    });

    return data;
  };

  const [data, setData] = useState(() => convertToSpreadsheetData(initialData));
  const [selectedCell, setSelectedCell] = useState({ row: 0, column: 0 });
  const [cellFormats, setCellFormats] = useState({});
  const [history, setHistory] = useState([{ data: convertToSpreadsheetData(initialData), formats: {} }]);
  const [historyIndex, setHistoryIndex] = useState(0);

  // Update data when initialData changes
  useEffect(() => {
    setData(convertToSpreadsheetData(initialData));
  }, [initialData]);

  const handleDataChange = useCallback((newData) => {
    setData(newData);

    // Convert back to our format and notify parent
    const convertedData = convertFromSpreadsheetData(newData);
    if (onCellChange) {
      onCellChange(convertedData);
    }
  }, [onCellChange]);

  const handleSelect = useCallback((selection) => {
    if (selection && selection.length > 0) {
      const { row, column } = selection[0];
      setSelectedCell({ row, column });

      // Convert to cell reference and notify parent
      const colLetter = String.fromCharCode(65 + column);
      const cellRef = `${colLetter}${row + 1}`;
      if (onCellSelect) {
        onCellSelect(cellRef);
      }
    }
  }, [onCellSelect]);

  // Get current cell reference
  const getCurrentCellRef = () => {
    const colLetter = String.fromCharCode(65 + selectedCell.column);
    return `${colLetter}${selectedCell.row + 1}`;
  };

  // Format functions
  const toggleBold = () => {
    const cellRef = getCurrentCellRef();
    const currentFormat = cellFormats[cellRef] || {};
    const newFormats = {
      ...cellFormats,
      [cellRef]: { ...currentFormat, bold: !currentFormat.bold }
    };
    setCellFormats(newFormats);
  };

  const toggleItalic = () => {
    const cellRef = getCurrentCellRef();
    const currentFormat = cellFormats[cellRef] || {};
    const newFormats = {
      ...cellFormats,
      [cellRef]: { ...currentFormat, italic: !currentFormat.italic }
    };
    setCellFormats(newFormats);
  };

  const toggleUnderline = () => {
    const cellRef = getCurrentCellRef();
    const currentFormat = cellFormats[cellRef] || {};
    const newFormats = {
      ...cellFormats,
      [cellRef]: { ...currentFormat, underline: !currentFormat.underline }
    };
    setCellFormats(newFormats);
  };

  const setBackgroundColor = (color) => {
    const cellRef = getCurrentCellRef();
    const currentFormat = cellFormats[cellRef] || {};
    const newFormats = {
      ...cellFormats,
      [cellRef]: { ...currentFormat, backgroundColor: color }
    };
    setCellFormats(newFormats);
  };

  // Get current cell format
  const getCurrentCellFormat = () => {
    const cellRef = getCurrentCellRef();
    return cellFormats[cellRef] || {};
  };

  // Custom cell renderer with formatting
  const Cell = ({ row, column, cell, DataViewer, ...props }) => {
    const cellRef = `${String.fromCharCode(65 + column)}${row + 1}`;
    const format = cellFormats[cellRef] || {};
    const isSelected = selectedCell.row === row && selectedCell.column === column;

    const cellStyle = {
      border: '1px solid #ddd',
      padding: '4px 8px',
      minWidth: '80px',
      height: '32px',
      backgroundColor: isSelected ? '#e3f2fd' : (format.backgroundColor || 'white'),
      fontWeight: format.bold ? 'bold' : 'normal',
      fontStyle: format.italic ? 'italic' : 'normal',
      textDecoration: format.underline ? 'underline' : 'none',
      ...props.style
    };

    return (
      <td
        {...props}
        className={`react-spreadsheet__cell ${isSelected ? 'react-spreadsheet__cell--selected' : ''}`}
        style={cellStyle}
      >
        <DataViewer cell={cell} />
      </td>
    );
  };

  return (
    <div className="react-spreadsheet-wrapper bg-white border border-gray-300 rounded-lg overflow-hidden">
      {/* Formatting Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 p-2">
        <div className="flex items-center space-x-2">
          {/* Font formatting */}
          <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
            <button
              onClick={toggleBold}
              className={`p-1 rounded hover:bg-gray-200 ${getCurrentCellFormat().bold ? 'bg-blue-200' : ''}`}
              title={t('bold')}
              disabled={readOnly}
            >
              <Bold className="h-4 w-4" />
            </button>
            <button
              onClick={toggleItalic}
              className={`p-1 rounded hover:bg-gray-200 ${getCurrentCellFormat().italic ? 'bg-blue-200' : ''}`}
              title={t('italic')}
              disabled={readOnly}
            >
              <Italic className="h-4 w-4" />
            </button>
            <button
              onClick={toggleUnderline}
              className={`p-1 rounded hover:bg-gray-200 ${getCurrentCellFormat().underline ? 'bg-blue-200' : ''}`}
              title={t('underline')}
              disabled={readOnly}
            >
              <Underline className="h-4 w-4" />
            </button>
          </div>

          {/* Background color */}
          <div className="flex items-center space-x-1">
            <span className="text-sm text-gray-600">{t('backgroundColor')}:</span>
            <button
              onClick={() => setBackgroundColor('')}
              className="w-6 h-6 border border-gray-300 rounded bg-white"
              title={t('noColor')}
              disabled={readOnly}
            />
            <button
              onClick={() => setBackgroundColor('yellow')}
              className="w-6 h-6 border border-gray-300 rounded bg-yellow-200"
              title={t('yellow')}
              disabled={readOnly}
            />
            <button
              onClick={() => setBackgroundColor('lightblue')}
              className="w-6 h-6 border border-gray-300 rounded bg-blue-200"
              title={t('lightBlue')}
              disabled={readOnly}
            />
            <button
              onClick={() => setBackgroundColor('lightgreen')}
              className="w-6 h-6 border border-gray-300 rounded bg-green-200"
              title={t('lightGreen')}
              disabled={readOnly}
            />
            <button
              onClick={() => setBackgroundColor('pink')}
              className="w-6 h-6 border border-gray-300 rounded bg-pink-200"
              title={t('pink')}
              disabled={readOnly}
            />
          </div>
        </div>
      </div>

      {/* Formula Bar */}
      <div className="bg-gray-50 border-b border-gray-300 p-2 flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">{t('cell')}:</span>
          <span className="bg-white border border-gray-300 px-2 py-1 rounded text-sm font-mono">
            {getCurrentCellRef()}
          </span>
        </div>
        <div className="flex-1">
          <input
            type="text"
            value={data[selectedCell.row]?.[selectedCell.column]?.value || ''}
            onChange={(e) => {
              const newData = [...data];
              if (!newData[selectedCell.row]) {
                newData[selectedCell.row] = [];
              }
              if (!newData[selectedCell.row][selectedCell.column]) {
                newData[selectedCell.row][selectedCell.column] = {};
              }
              newData[selectedCell.row][selectedCell.column].value = e.target.value;
              handleDataChange(newData);
            }}
            className="w-full px-3 py-1 border border-gray-300 rounded text-sm font-mono"
            placeholder={t('enterValue')}
            disabled={readOnly}
          />
        </div>
      </div>

      {/* Spreadsheet */}
      <div className="p-4">
        <Spreadsheet
          data={data}
          onChange={handleDataChange}
          onSelect={handleSelect}
          Cell={Cell}
          columnLabels={Array.from({ length: 10 }, (_, i) => String.fromCharCode(65 + i))}
          rowLabels={Array.from({ length: 15 }, (_, i) => (i + 1).toString())}
        />
      </div>

      {/* Status Bar */}
      <div className="bg-gray-50 border-t border-gray-300 p-2 text-xs text-gray-600">
        <div className="flex justify-between">
          <span>{t('selected')}: {getCurrentCellRef()}</span>
          <span>{t('ready')}</span>
        </div>
      </div>
    </div>
  );
};

export default ReactSpreadsheetWrapper;
