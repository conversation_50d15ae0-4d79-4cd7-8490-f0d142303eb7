import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Workbook } from '@fortune-sheet/react';
import '@fortune-sheet/react/dist/index.css';
import { useLanguage } from '../contexts/LanguageContext';

const ReactSpreadsheetWrapper = ({ onCellChange, onCellSelect, initialData = {}, readOnly = false }) => {
  const { t } = useLanguage();
  const containerRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(false);

  // Convert our data format to Fortune Sheet format
  const convertToFortuneSheetData = (data) => {
    const celldata = [];

    // Convert our data format to Fortune Sheet celldata format
    Object.keys(data).forEach(cellRef => {
      if (cellRef.includes('_format')) return; // Skip format data for now

      const match = cellRef.match(/^([A-Z]+)(\d+)$/);
      if (match) {
        const colStr = match[1];
        const rowNum = parseInt(match[2]) - 1; // Convert to 0-based

        // Convert column letter to number (A=0, B=1, etc.)
        let col = 0;
        for (let i = 0; i < colStr.length; i++) {
          col = col * 26 + (colStr.charCodeAt(i) - 64);
        }
        col -= 1; // Convert to 0-based

        if (rowNum >= 0 && col >= 0) {
          celldata.push({
            r: rowNum,
            c: col,
            v: {
              v: data[cellRef],
              m: String(data[cellRef]),
              ct: { fa: 'General', t: 'g' }
            }
          });
        }
      }
    });

    return celldata;
  };

  // Convert Fortune Sheet data back to our format
  const convertFromFortuneSheetData = (sheetData) => {
    const data = {};

    if (sheetData && sheetData.celldata) {
      sheetData.celldata.forEach(cell => {
        if (cell.v && cell.v.v !== undefined && cell.v.v !== '') {
          const colLetter = String.fromCharCode(65 + cell.c);
          const cellRef = `${colLetter}${cell.r + 1}`;
          data[cellRef] = cell.v.v;
        }
      });
    }

    return data;
  };

  // Initialize Fortune Sheet data
  const [workbookData, setWorkbookData] = useState(() => {
    const celldata = convertToFortuneSheetData(initialData);
    return [{
      name: "Sheet1",
      color: "",
      status: 1,
      order: 0,
      data: [],
      config: {},
      index: 0,
      celldata: celldata,
      row: 20,
      column: 26,
      defaultRowHeight: 19,
      defaultColWidth: 73
    }];
  });

  // Update data when initialData changes
  useEffect(() => {
    const celldata = convertToFortuneSheetData(initialData);
    setWorkbookData([{
      name: "Sheet1",
      color: "",
      status: 1,
      order: 0,
      data: [],
      config: {},
      index: 0,
      celldata: celldata,
      row: 20,
      column: 26,
      defaultRowHeight: 19,
      defaultColWidth: 73
    }]);
  }, [initialData]);

  // Handle data changes from Fortune Sheet
  const handleCellUpdate = useCallback((data) => {
    if (data && data.length > 0) {
      const sheetData = data[0];
      const convertedData = convertFromFortuneSheetData(sheetData);
      console.log('Fortune Sheet data update:', convertedData);
      if (onCellChange) {
        onCellChange(convertedData);
      }
    }
  }, [onCellChange]);

  // Handle cell selection
  const handleCellSelect = useCallback((selection) => {
    if (selection && selection.length > 0) {
      const { row, column } = selection[0];
      const colLetter = String.fromCharCode(65 + column);
      const cellRef = `${colLetter}${row + 1}`;
      if (onCellSelect) {
        onCellSelect(cellRef);
      }
    }
  }, [onCellSelect]);

  // Error boundary for Fortune Sheet
  useEffect(() => {
    const handleError = (event) => {
      if (event.error && event.error.message && event.error.message.includes('objectInnerText')) {
        console.warn('Fortune Sheet objectInnerText error caught and ignored');
        event.preventDefault();
        return false;
      }
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  // Initialize Fortune Sheet after component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  if (!isLoaded) {
    return (
      <div className="fortune-sheet-wrapper bg-white border border-gray-300 rounded-lg overflow-hidden">
        <div style={{ width: '100%', height: '500px' }} className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="fortune-sheet-wrapper bg-white border border-gray-300 rounded-lg overflow-hidden" ref={containerRef}>
      <div style={{ width: '100%', height: '500px' }}>
        <Workbook
          data={workbookData}
          onChange={handleCellUpdate}
          onSelect={handleCellSelect}
          options={{
            container: 'fortune-sheet-container',
            title: 'Excel Learning Platform',
            lang: 'en',
            allowCopy: !readOnly,
            allowEdit: !readOnly,
            allowUpdate: !readOnly,
            showToolbar: true,
            showInfoBar: true,
            showFormulaBar: true,
            showSheetTabs: false,
            showConfigWindowResize: true,
            enableAddRow: false,
            enableAddBackTop: false,
            userInfo: false,
            myFolderUrl: false,
            devicePixelRatio: window.devicePixelRatio || 1,
            // Simplified toolbar to avoid potential issues
            toolbarItems: [
              "undo", "redo", "|",
              "format-painter", "clear-format", "|",
              "bold", "italic", "underline", "|",
              "font-color", "background", "border", "|",
              "horizontal-align", "vertical-align", "|",
              "freeze", "sort"
            ],
            // Additional error handling options
            errorHandler: (error) => {
              console.warn('Fortune Sheet error:', error);
              return false; // Prevent default error handling
            }
          }}
        />
      </div>
    </div>
  );
};

export default ReactSpreadsheetWrapper;
