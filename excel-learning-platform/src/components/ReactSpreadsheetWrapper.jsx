import React, { useState, useEffect, useCallback } from 'react';
import Spreadsheet from 'react-spreadsheet';
import { useLanguage } from '../contexts/LanguageContext';

const ReactSpreadsheetWrapper = ({ onCellChange, onCellSelect, initialData = {}, readOnly = false }) => {
  const { t } = useLanguage();

  // Convert our data format to react-spreadsheet format
  const convertToSpreadsheetData = (data) => {
    const spreadsheetData = [];
    const maxRow = 15;
    const maxCol = 10;

    // Initialize empty grid
    for (let row = 0; row < maxRow; row++) {
      spreadsheetData[row] = [];
      for (let col = 0; col < maxCol; col++) {
        spreadsheetData[row][col] = { value: '' };
      }
    }

    // Fill with actual data
    Object.keys(data).forEach(cellRef => {
      if (cellRef.includes('_format')) return; // Skip format data

      const match = cellRef.match(/^([A-Z]+)(\d+)$/);
      if (match) {
        const colStr = match[1];
        const rowNum = parseInt(match[2]) - 1; // Convert to 0-based

        // Convert column letter to number (A=0, B=1, etc.)
        let col = 0;
        for (let i = 0; i < colStr.length; i++) {
          col = col * 26 + (colStr.charCodeAt(i) - 64);
        }
        col -= 1; // Convert to 0-based

        if (rowNum >= 0 && rowNum < maxRow && col >= 0 && col < maxCol) {
          spreadsheetData[rowNum][col] = { value: data[cellRef] };
        }
      }
    });

    return spreadsheetData;
  };

  // Convert spreadsheet data back to our format
  const convertFromSpreadsheetData = (spreadsheetData) => {
    const data = {};

    spreadsheetData.forEach((row, rowIndex) => {
      row.forEach((cell, colIndex) => {
        if (cell && cell.value !== undefined && cell.value !== '') {
          const colLetter = String.fromCharCode(65 + colIndex);
          const cellRef = `${colLetter}${rowIndex + 1}`;
          data[cellRef] = cell.value;
        }
      });
    });

    return data;
  };

  const [data, setData] = useState(() => convertToSpreadsheetData(initialData));

  // Update data when initialData changes
  useEffect(() => {
    setData(convertToSpreadsheetData(initialData));
  }, [initialData]);

  const handleDataChange = useCallback((newData) => {
    setData(newData);

    // Convert back to our format and notify parent
    const convertedData = convertFromSpreadsheetData(newData);
    if (onCellChange) {
      onCellChange(convertedData);
    }
  }, [onCellChange]);

  return (
    <div className="react-spreadsheet-wrapper bg-white border border-gray-300 rounded-lg overflow-hidden">
      {/* Spreadsheet */}
      <div className="p-4">
        <Spreadsheet
          data={data}
          onChange={handleDataChange}
        />
      </div>
    </div>
  );
};

export default ReactSpreadsheetWrapper;
