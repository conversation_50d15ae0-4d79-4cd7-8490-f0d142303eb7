import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { BookOpen, Trophy, LogOut, User, Globe } from 'lucide-react';

const Navbar = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const { language, toggleLanguage, t } = useLanguage();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <nav className="bg-excel-green shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 text-white font-bold text-xl">
            <BookOpen className="h-8 w-8" />
            <span>{t('homeTitle')}</span>
          </Link>

          {/* Navigation Links */}
          <div className="flex items-center space-x-6">
            {isAuthenticated ? (
              <>
                <Link
                  to="/dashboard"
                  className="text-white hover:text-green-200 transition-colors flex items-center space-x-1"
                >
                  <BookOpen className="h-4 w-4" />
                  <span>{t('dashboard')}</span>
                </Link>

                <Link
                  to="/leaderboard"
                  className="text-white hover:text-green-200 transition-colors flex items-center space-x-1"
                >
                  <Trophy className="h-4 w-4" />
                  <span>{t('leaderboard')}</span>
                </Link>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 text-white">
                    <User className="h-4 w-4" />
                    <span className="text-sm">{user?.username}</span>
                  </div>

                  <button
                    onClick={handleLogout}
                    className="text-white hover:text-green-200 transition-colors flex items-center space-x-1"
                  >
                    <LogOut className="h-4 w-4" />
                    <span>{t('logout')}</span>
                  </button>
                </div>

                <button
                  onClick={toggleLanguage}
                  className="text-white hover:text-green-200 transition-colors flex items-center space-x-1"
                  title={t('language')}
                >
                  <Globe className="h-4 w-4" />
                  <span className="text-sm">{language === 'en' ? '中文' : 'EN'}</span>
                </button>
              </>
            ) : (
              <>
                <Link
                  to="/login"
                  className="text-white hover:text-green-200 transition-colors"
                >
                  {t('login')}
                </Link>
                <Link
                  to="/register"
                  className="bg-white text-excel-green px-4 py-2 rounded-md hover:bg-green-50 transition-colors font-medium"
                >
                  {t('register')}
                </Link>
                
                <button
                  onClick={toggleLanguage}
                  className="text-white hover:text-green-200 transition-colors flex items-center space-x-1"
                  title={t('language')}
                >
                  <Globe className="h-4 w-4" />
                  <span className="text-sm">{language === 'en' ? '中文' : 'EN'}</span>
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
