import React, { useState, useRef, useEffect } from 'react';
import {
  Bold, Italic, Underline, Palette, AlignLeft, AlignCenter, AlignRight,
  Type, Square, Hash, Percent, DollarSign, Undo, Redo
} from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const ExcelSimulator = ({ onCellChange, onCellSelect, initialData = {}, readOnly = false }) => {
  const { t } = useLanguage();
  const [selectedCell, setSelectedCell] = useState('A1');
  const [cellData, setCellData] = useState(initialData);
  const [cellFormulas, setCellFormulas] = useState({}); // Store formulas separately
  const [editingCell, setEditingCell] = useState(null);
  const [cellFormats, setCellFormats] = useState({});
  const [history, setHistory] = useState([{ cellData: initialData, cellFormats: {}, cellFormulas: {} }]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const inputRef = useRef(null);

  const rows = 15;
  const cols = 10;

  // Generate column letters (A, B, C, ...)
  const getColumnLetter = (index) => {
    return String.fromCharCode(65 + index);
  };

  // Generate cell reference (A1, B2, etc.)
  const getCellRef = (row, col) => {
    return `${getColumnLetter(col)}${row + 1}`;
  };

  // Parse cell reference to get row and column
  const parseCellRef = (cellRef) => {
    const match = cellRef.match(/^([A-Z]+)(\d+)$/);
    if (!match) return null;

    const colStr = match[1];
    const rowNum = parseInt(match[2]);

    let col = 0;
    for (let i = 0; i < colStr.length; i++) {
      col = col * 26 + (colStr.charCodeAt(i) - 64);
    }
    col -= 1; // Convert to 0-based index

    return { row: rowNum - 1, col };
  };

  // Evaluate formula
  const evaluateFormula = (formula, currentCellRef = null) => {
    try {
      // Remove the = sign
      let expr = formula.slice(1);

      // Replace cell references with their values
      expr = expr.replace(/[A-Z]+\d+/g, (cellRef) => {
        // Prevent circular reference
        if (cellRef === currentCellRef) return '0';

        const value = getCellDisplayValue(cellRef);
        const numValue = parseFloat(value);
        return isNaN(numValue) ? '0' : numValue.toString();
      });

      // Handle Excel functions
      expr = expr.replace(/SUM\(([^)]+)\)/gi, (match, range) => {
        return evaluateSumFunction(range);
      });

      expr = expr.replace(/AVERAGE\(([^)]+)\)/gi, (match, range) => {
        return evaluateAverageFunction(range);
      });

      expr = expr.replace(/COUNT\(([^)]+)\)/gi, (match, range) => {
        return evaluateCountFunction(range);
      });

      expr = expr.replace(/IF\(([^)]+)\)/gi, (match, condition) => {
        return evaluateIfFunction(condition);
      });

      // Evaluate the expression safely
      const result = Function('"use strict"; return (' + expr + ')')();
      return isNaN(result) ? '#ERROR!' : result;
    } catch (error) {
      return '#ERROR!';
    }
  };

  // Evaluate SUM function
  const evaluateSumFunction = (range) => {
    const values = getRangeValues(range);
    return values.reduce((sum, val) => sum + (parseFloat(val) || 0), 0);
  };

  // Evaluate AVERAGE function
  const evaluateAverageFunction = (range) => {
    const values = getRangeValues(range);
    const numericValues = values.filter(val => !isNaN(parseFloat(val)));
    if (numericValues.length === 0) return 0;
    const sum = numericValues.reduce((sum, val) => sum + parseFloat(val), 0);
    return sum / numericValues.length;
  };

  // Evaluate COUNT function
  const evaluateCountFunction = (range) => {
    const values = getRangeValues(range);
    return values.filter(val => !isNaN(parseFloat(val))).length;
  };

  // Evaluate IF function
  const evaluateIfFunction = (condition) => {
    const parts = condition.split(',');
    if (parts.length !== 3) return '#ERROR!';

    const conditionExpr = parts[0].trim();
    const trueValue = parts[1].trim().replace(/"/g, '');
    const falseValue = parts[2].trim().replace(/"/g, '');

    try {
      // Replace cell references in condition
      let expr = conditionExpr.replace(/[A-Z]+\d+/g, (cellRef) => {
        const value = getCellDisplayValue(cellRef);
        const numValue = parseFloat(value);
        return isNaN(numValue) ? '0' : numValue.toString();
      });

      const result = Function('"use strict"; return (' + expr + ')')();
      return result ? trueValue : falseValue;
    } catch (error) {
      return '#ERROR!';
    }
  };

  // Get values from a range (e.g., A1:A5)
  const getRangeValues = (range) => {
    const values = [];

    if (range.includes(':')) {
      // Range like A1:A5
      const [start, end] = range.split(':');
      const startPos = parseCellRef(start);
      const endPos = parseCellRef(end);

      if (!startPos || !endPos) return values;

      for (let row = startPos.row; row <= endPos.row; row++) {
        for (let col = startPos.col; col <= endPos.col; col++) {
          const cellRef = getCellRef(row, col);
          const value = getCellDisplayValue(cellRef);
          if (value !== '') values.push(value);
        }
      }
    } else {
      // Single cell
      const value = getCellDisplayValue(range);
      if (value !== '') values.push(value);
    }

    return values;
  };

  // Get the display value of a cell (calculated if formula)
  const getCellDisplayValue = (cellRef) => {
    const formula = cellFormulas[cellRef];
    if (formula && formula.startsWith('=')) {
      return evaluateFormula(formula, cellRef);
    }
    return cellData[cellRef] || '';
  };

  // Save state to history
  const saveToHistory = (newCellData, newCellFormats, newCellFormulas) => {
    const newState = { cellData: newCellData, cellFormats: newCellFormats, cellFormulas: newCellFormulas };
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newState);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // Undo function
  const undo = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      const state = history[newIndex];
      setCellData(state.cellData);
      setCellFormats(state.cellFormats);
      setCellFormulas(state.cellFormulas || {});
      setHistoryIndex(newIndex);
      if (onCellChange) {
        onCellChange(state.cellData);
      }
    }
  };

  // Redo function
  const redo = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      const state = history[newIndex];
      setCellData(state.cellData);
      setCellFormats(state.cellFormats);
      setCellFormulas(state.cellFormulas || {});
      setHistoryIndex(newIndex);
      if (onCellChange) {
        onCellChange(state.cellData);
      }
    }
  };

  // Handle cell click
  const handleCellClick = (row, col) => {
    const cellRef = getCellRef(row, col);
    setSelectedCell(cellRef);

    // Notify parent component about cell selection
    if (onCellSelect) {
      onCellSelect(cellRef);
    }

    if (!readOnly) {
      setEditingCell(cellRef);
    }
  };

  // Handle cell value change
  const handleCellChange = (cellRef, value) => {
    const newCellData = { ...cellData };
    const newCellFormulas = { ...cellFormulas };

    if (value.startsWith('=')) {
      // It's a formula
      newCellFormulas[cellRef] = value;
      newCellData[cellRef] = evaluateFormula(value, cellRef);
    } else {
      // It's a regular value
      newCellData[cellRef] = value;
      delete newCellFormulas[cellRef]; // Remove any existing formula
    }

    setCellData(newCellData);
    setCellFormulas(newCellFormulas);
    saveToHistory(newCellData, cellFormats, newCellFormulas);

    if (onCellChange) {
      onCellChange(newCellData);
    }
  };

  // Format functions
  const toggleBold = () => {
    const currentFormat = cellFormats[selectedCell] || {};
    const newFormat = { ...currentFormat, bold: !currentFormat.bold };
    const newFormats = { ...cellFormats, [selectedCell]: newFormat };
    setCellFormats(newFormats);

    // Update cell data to include formatting info
    const newData = { ...cellData, [`${selectedCell}_format`]: newFormat };
    setCellData(newData);
    if (onCellChange) {
      onCellChange(newData);
    }
  };

  const toggleItalic = () => {
    const currentFormat = cellFormats[selectedCell] || {};
    const newFormat = { ...currentFormat, italic: !currentFormat.italic };
    const newFormats = { ...cellFormats, [selectedCell]: newFormat };
    setCellFormats(newFormats);

    const newData = { ...cellData, [`${selectedCell}_format`]: newFormat };
    setCellData(newData);
    if (onCellChange) {
      onCellChange(newData);
    }
  };

  const setBackgroundColor = (color) => {
    const currentFormat = cellFormats[selectedCell] || {};
    const newFormat = { ...currentFormat, backgroundColor: color };
    const newFormats = { ...cellFormats, [selectedCell]: newFormat };
    setCellFormats(newFormats);

    const newData = { ...cellData, [`${selectedCell}_format`]: newFormat };
    setCellData(newData);
    if (onCellChange) {
      onCellChange(newData);
    }
  };

  const toggleUnderline = () => {
    const currentFormat = cellFormats[selectedCell] || {};
    const newFormat = { ...currentFormat, underline: !currentFormat.underline };
    const newFormats = { ...cellFormats, [selectedCell]: newFormat };
    setCellFormats(newFormats);

    const newData = { ...cellData, [`${selectedCell}_format`]: newFormat };
    setCellData(newData);
    if (onCellChange) {
      onCellChange(newData);
    }
  };

  const setTextAlign = (align) => {
    const currentFormat = cellFormats[selectedCell] || {};
    const newFormat = { ...currentFormat, textAlign: align };
    const newFormats = { ...cellFormats, [selectedCell]: newFormat };
    setCellFormats(newFormats);

    const newData = { ...cellData, [`${selectedCell}_format`]: newFormat };
    setCellData(newData);
    if (onCellChange) {
      onCellChange(newData);
    }
  };

  const setFontSize = (size) => {
    const currentFormat = cellFormats[selectedCell] || {};
    const newFormat = { ...currentFormat, fontSize: size };
    const newFormats = { ...cellFormats, [selectedCell]: newFormat };
    setCellFormats(newFormats);

    const newData = { ...cellData, [`${selectedCell}_format`]: newFormat };
    setCellData(newData);
    if (onCellChange) {
      onCellChange(newData);
    }
  };

  const setTextColor = (color) => {
    const currentFormat = cellFormats[selectedCell] || {};
    const newFormat = { ...currentFormat, color: color };
    const newFormats = { ...cellFormats, [selectedCell]: newFormat };
    setCellFormats(newFormats);

    const newData = { ...cellData, [`${selectedCell}_format`]: newFormat };
    setCellData(newData);
    if (onCellChange) {
      onCellChange(newData);
    }
  };

  const setBorder = (borderStyle) => {
    const currentFormat = cellFormats[selectedCell] || {};
    const newFormat = { ...currentFormat, border: borderStyle };
    const newFormats = { ...cellFormats, [selectedCell]: newFormat };
    setCellFormats(newFormats);

    const newData = { ...cellData, [`${selectedCell}_format`]: newFormat };
    setCellData(newData);
    if (onCellChange) {
      onCellChange(newData);
    }
  };

  const setNumberFormat = (format) => {
    const currentFormat = cellFormats[selectedCell] || {};
    const newFormat = { ...currentFormat, numberFormat: format };
    const newFormats = { ...cellFormats, [selectedCell]: newFormat };
    setCellFormats(newFormats);

    const newData = { ...cellData, [`${selectedCell}_format`]: newFormat };
    setCellData(newData);
    if (onCellChange) {
      onCellChange(newData);
    }
  };



  // Handle key press in cell
  const handleKeyPress = (e, cellRef) => {
    if (e.key === 'Enter') {
      setEditingCell(null);
      // Move to next row
      const { col, row } = parseCellRef(cellRef);
      if (row < rows - 1) {
        const nextCell = getCellRef(row + 1, col);
        setSelectedCell(nextCell);
      }
    } else if (e.key === 'Escape') {
      setEditingCell(null);
    }
  };

  // Handle arrow key navigation
  const handleArrowKeys = (e) => {
    // Only handle arrow keys when not editing a cell
    if (editingCell) return;

    const { row, col } = parseCellRef(selectedCell);
    let newRow = row;
    let newCol = col;

    switch (e.key) {
      case 'ArrowUp':
        e.preventDefault();
        newRow = Math.max(0, row - 1);
        break;
      case 'ArrowDown':
        e.preventDefault();
        newRow = Math.min(rows - 1, row + 1);
        break;
      case 'ArrowLeft':
        e.preventDefault();
        newCol = Math.max(0, col - 1);
        break;
      case 'ArrowRight':
        e.preventDefault();
        newCol = Math.min(cols - 1, col + 1);
        break;
      case 'Enter':
        e.preventDefault();
        setEditingCell(selectedCell);
        break;
      default:
        return;
    }

    const newCellRef = getCellRef(newRow, newCol);
    setSelectedCell(newCellRef);

    // Notify parent component about cell selection
    if (onCellSelect) {
      onCellSelect(newCellRef);
    }
  };

  // Auto-focus input when editing
  useEffect(() => {
    if (editingCell && inputRef.current) {
      inputRef.current.focus();
    }
  }, [editingCell]);

  // Add keyboard event listener for arrow keys
  useEffect(() => {
    const handleKeyDown = (e) => {
      handleArrowKeys(e);
    };

    // Add event listener to the document
    document.addEventListener('keydown', handleKeyDown);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedCell, editingCell]);

  return (
    <div className="excel-simulator bg-white border border-gray-300 rounded-lg overflow-hidden">
      {/* Formatting Toolbar */}
      {!readOnly && (
        <div className="bg-gray-100 border-b border-gray-300 p-2">
          {/* Font Formatting Row */}
          <div className="flex items-center space-x-2 mb-2">
            {/* Undo/Redo */}
            <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
              <button
                onClick={undo}
                disabled={historyIndex <= 0}
                className={`p-2 rounded hover:bg-gray-200 ${
                  historyIndex <= 0 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                title={t('undo')}
              >
                <Undo className="h-4 w-4" />
              </button>
              <button
                onClick={redo}
                disabled={historyIndex >= history.length - 1}
                className={`p-2 rounded hover:bg-gray-200 ${
                  historyIndex >= history.length - 1 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                title={t('redo')}
              >
                <Redo className="h-4 w-4" />
              </button>
            </div>

            {/* Font Style */}
            <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
              <button
                onClick={toggleBold}
                className={`p-2 rounded hover:bg-gray-200 ${
                  cellFormats[selectedCell]?.bold ? 'bg-blue-200' : ''
                }`}
                title={t('bold')}
              >
                <Bold className="h-4 w-4" />
              </button>
              <button
                onClick={toggleItalic}
                className={`p-2 rounded hover:bg-gray-200 ${
                  cellFormats[selectedCell]?.italic ? 'bg-blue-200' : ''
                }`}
                title={t('italic')}
              >
                <Italic className="h-4 w-4" />
              </button>
              <button
                onClick={toggleUnderline}
                className={`p-2 rounded hover:bg-gray-200 ${
                  cellFormats[selectedCell]?.underline ? 'bg-blue-200' : ''
                }`}
                title={t('underline')}
              >
                <Underline className="h-4 w-4" />
              </button>
            </div>

            {/* Font Size */}
            <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
              <Type className="h-4 w-4 text-gray-600" />
              <select
                onChange={(e) => setFontSize(e.target.value)}
                value={cellFormats[selectedCell]?.fontSize || '12'}
                className="text-sm border border-gray-300 rounded px-2 py-1"
              >
                <option value="8">8</option>
                <option value="9">9</option>
                <option value="10">10</option>
                <option value="11">11</option>
                <option value="12">12</option>
                <option value="14">14</option>
                <option value="16">16</option>
                <option value="18">18</option>
                <option value="20">20</option>
                <option value="24">24</option>
              </select>
            </div>

            {/* Text Color */}
            <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
              <span className="text-sm text-gray-600">A</span>
              <select
                onChange={(e) => setTextColor(e.target.value)}
                value={cellFormats[selectedCell]?.color || ''}
                className="text-sm border border-gray-300 rounded px-2 py-1"
              >
                <option value="">Black</option>
                <option value="red">Red</option>
                <option value="blue">Blue</option>
                <option value="green">Green</option>
                <option value="purple">Purple</option>
                <option value="orange">Orange</option>
              </select>
            </div>

            {/* Background Color */}
            <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
              <Palette className="h-4 w-4 text-gray-600" />
              <select
                onChange={(e) => setBackgroundColor(e.target.value)}
                value={cellFormats[selectedCell]?.backgroundColor || ''}
                className="text-sm border border-gray-300 rounded px-2 py-1"
              >
                <option value="">{t('noColor')}</option>
                <option value="yellow">{t('yellow')}</option>
                <option value="lightblue">{t('lightBlue')}</option>
                <option value="lightgreen">{t('lightGreen')}</option>
                <option value="pink">{t('pink')}</option>
                <option value="orange">{t('orange')}</option>
              </select>
            </div>
          </div>

          {/* Alignment and Format Row */}
          <div className="flex items-center space-x-2">
            {/* Text Alignment */}
            <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
              <button
                onClick={() => setTextAlign('left')}
                className={`p-2 rounded hover:bg-gray-200 ${
                  cellFormats[selectedCell]?.textAlign === 'left' ? 'bg-blue-200' : ''
                }`}
                title="Align Left"
              >
                <AlignLeft className="h-4 w-4" />
              </button>
              <button
                onClick={() => setTextAlign('center')}
                className={`p-2 rounded hover:bg-gray-200 ${
                  cellFormats[selectedCell]?.textAlign === 'center' ? 'bg-blue-200' : ''
                }`}
                title="Align Center"
              >
                <AlignCenter className="h-4 w-4" />
              </button>
              <button
                onClick={() => setTextAlign('right')}
                className={`p-2 rounded hover:bg-gray-200 ${
                  cellFormats[selectedCell]?.textAlign === 'right' ? 'bg-blue-200' : ''
                }`}
                title="Align Right"
              >
                <AlignRight className="h-4 w-4" />
              </button>
            </div>

            {/* Number Format */}
            <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
              <button
                onClick={() => setNumberFormat('general')}
                className="p-2 rounded hover:bg-gray-200"
                title="General"
              >
                <Hash className="h-4 w-4" />
              </button>
              <button
                onClick={() => setNumberFormat('percent')}
                className="p-2 rounded hover:bg-gray-200"
                title="Percentage"
              >
                <Percent className="h-4 w-4" />
              </button>
              <button
                onClick={() => setNumberFormat('currency')}
                className="p-2 rounded hover:bg-gray-200"
                title="Currency"
              >
                <DollarSign className="h-4 w-4" />
              </button>
            </div>

            {/* Borders */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setBorder('1px solid black')}
                className="p-2 rounded hover:bg-gray-200"
                title="Add Border"
              >
                <Square className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Formula Bar */}
      <div className="bg-gray-50 border-b border-gray-300 p-2 flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">{t('cell')}:</span>
          <span className="bg-white border border-gray-300 px-2 py-1 rounded text-sm font-mono">
            {selectedCell}
          </span>
        </div>
        <div className="flex-1">
          <input
            type="text"
            value={cellFormulas[selectedCell] || cellData[selectedCell] || ''}
            onChange={(e) => handleCellChange(selectedCell, e.target.value)}
            className="w-full px-3 py-1 border border-gray-300 rounded text-sm font-mono"
            placeholder={t('enterValue')}
            disabled={readOnly}
          />
        </div>
      </div>

      {/* Spreadsheet Grid */}
      <div className="overflow-auto max-h-96">
        <table className="excel-table w-full">
          {/* Header Row */}
          <thead>
            <tr>
              <th className="excel-header w-12 h-8"></th>
              {Array.from({ length: cols }, (_, col) => (
                <th key={col} className="excel-header w-20 h-8">
                  {getColumnLetter(col)}
                </th>
              ))}
            </tr>
          </thead>

          {/* Data Rows */}
          <tbody>
            {Array.from({ length: rows }, (_, row) => (
              <tr key={row}>
                {/* Row Header */}
                <td className="excel-header w-12 h-8">
                  {row + 1}
                </td>

                {/* Data Cells */}
                {Array.from({ length: cols }, (_, col) => {
                  const cellRef = getCellRef(row, col);
                  const isSelected = selectedCell === cellRef;
                  const isEditing = editingCell === cellRef;
                  const cellValue = isEditing ? (cellFormulas[cellRef] || cellData[cellRef] || '') : getCellDisplayValue(cellRef);
                  const cellFormat = cellFormats[cellRef] || {};

                  const cellStyle = {
                    backgroundColor: cellFormat.backgroundColor || 'transparent',
                    fontWeight: cellFormat.bold ? 'bold' : 'normal',
                    fontStyle: cellFormat.italic ? 'italic' : 'normal',
                    textDecoration: cellFormat.underline ? 'underline' : 'none',
                    textAlign: cellFormat.textAlign || 'left',
                    fontSize: cellFormat.fontSize ? `${cellFormat.fontSize}px` : '12px',
                    color: cellFormat.color || 'black',
                    border: cellFormat.border || 'none',
                  };

                  return (
                    <td
                      key={col}
                      className={`excel-cell w-20 h-8 p-1 cursor-pointer ${
                        isSelected ? 'selected' : ''
                      }`}
                      style={cellStyle}
                      onClick={() => handleCellClick(row, col)}
                    >
                      {isEditing ? (
                        <input
                          ref={inputRef}
                          type="text"
                          value={cellValue}
                          onChange={(e) => handleCellChange(cellRef, e.target.value)}
                          onKeyDown={(e) => handleKeyPress(e, cellRef)}
                          onBlur={() => setEditingCell(null)}
                          className="w-full h-full border-none outline-none bg-transparent text-sm font-mono"
                          style={cellStyle}
                        />
                      ) : (
                        <div
                          className="w-full h-full flex items-center text-sm font-mono"
                          style={cellStyle}
                        >
                          {cellValue}
                        </div>
                      )}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Status Bar */}
      <div className="bg-gray-50 border-t border-gray-300 p-2 text-xs text-gray-600">
        <div className="flex justify-between">
          <span>{t('selected')}: {selectedCell}</span>
          <span>{t('ready')}</span>
        </div>
      </div>
    </div>
  );
};

export default ExcelSimulator;
