@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Excel-like interface */
.excel-cell {
  @apply border border-gray-300 bg-white text-sm font-mono;
  border-width: 1px;
  border-style: solid;
  border-color: #d1d5db;
}

.excel-cell:focus {
  @apply outline-none ring-2 ring-excel-blue border-excel-blue;
}

.excel-cell.selected {
  @apply bg-blue-100 border-excel-blue;
}

.excel-header {
  @apply bg-excel-gray border border-gray-300 text-center font-semibold text-xs;
}

.challenge-card {
  @apply bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow;
}

.level-card {
  @apply bg-gradient-to-br from-excel-green to-green-600 text-white rounded-lg shadow-lg;
}

.progress-bar {
  @apply bg-gray-200 rounded-full overflow-hidden;
}

.progress-fill {
  @apply bg-excel-green h-full transition-all duration-300;
}
