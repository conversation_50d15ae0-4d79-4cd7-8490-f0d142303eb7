@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Excel-like interface */
.excel-cell {
  @apply bg-white text-sm font-mono;
  border: 1px solid #e5e7eb;
  border-right: 1px solid #d1d5db;
  border-bottom: 1px solid #d1d5db;
  min-height: 24px;
  position: relative;
}

.excel-cell:hover {
  background-color: #f8fafc;
}

.excel-cell.selected {
  background-color: #dbeafe;
  border: 2px solid #3b82f6;
  z-index: 1;
}

.excel-header {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-right: 1px solid #9ca3af;
  border-bottom: 1px solid #9ca3af;
  text-align: center;
  font-weight: 600;
  font-size: 12px;
  color: #374151;
  min-height: 24px;
  padding: 4px;
}

.excel-cell:focus {
  @apply outline-none ring-2 ring-excel-blue border-excel-blue;
}

.excel-cell.selected {
  @apply bg-blue-100 border-excel-blue;
}

.excel-header {
  @apply bg-excel-gray border border-gray-300 text-center font-semibold text-xs;
}

.challenge-card {
  @apply bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow;
}

.level-card {
  @apply bg-gradient-to-br from-excel-green to-green-600 text-white rounded-lg shadow-lg;
}

.progress-bar {
  @apply bg-gray-200 rounded-full overflow-hidden;
}

.progress-fill {
  @apply bg-excel-green h-full transition-all duration-300;
}
