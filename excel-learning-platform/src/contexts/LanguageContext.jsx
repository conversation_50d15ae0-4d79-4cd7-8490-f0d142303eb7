import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

const translations = {
  en: {
    // Navigation
    home: 'Home',
    dashboard: 'Dashboard',
    leaderboard: 'Leaderboard',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    language: 'Language',

    // Home page
    homeTitle: 'Excel Learning Platform',
    homeSubtitle: 'Master Excel skills through interactive challenges and gamified learning',
    heroTitle: 'Master Excel Through',
    heroTitleHighlight: 'Interactive Learning',
    heroDescription: 'Learn Excel skills through gamified challenges and hands-on practice. From basic formulas to advanced data analysis, become an Excel expert step by step.',
    continueLearning: 'Continue Learning',
    startLearning: 'Start Learning',
    signIn: 'Sign In',
    whyChoose: 'Why Choose Excel Master?',
    learningJourney: 'Your Learning Journey',
    learningJourneyDesc: 'Progress through 5 comprehensive levels, from Excel basics to advanced data analysis',

    // Features
    interactiveLearningTitle: 'Interactive Learning',
    interactiveLearningDesc: 'Learn Excel through hands-on practice with our interactive spreadsheet simulator.',
    gamifiedChallengesTitle: 'Gamified Challenges',
    gamifiedChallengesDesc: 'Progress through levels and unlock new skills with our engaging challenge system.',
    trackProgressTitle: 'Track Progress',
    trackProgressDesc: 'Monitor your learning journey and compete with others on the leaderboard.',
    communityLearningTitle: 'Community Learning',
    communityLearningDesc: 'Join thousands of learners mastering Excel skills together.',

    // Level names and descriptions
    level1Title: 'Excel Basics',
    level2Title: 'Formulas & Functions',
    level3Title: 'Data Management',
    level4Title: 'Advanced Functions',
    level5Title: 'Data Analysis',

    // Learning path
    interfaceNavigation: 'Interface & Navigation',
    sumAverageIf: 'SUM, AVERAGE, IF',
    sortingFiltering: 'Sorting & Filtering',
    vlookupIndexMatch: 'VLOOKUP, INDEX/MATCH',
    pivotTablesCharts: 'Pivot Tables & Charts',

    // Dashboard
    welcomeBack: 'Welcome back',
    yourProgress: 'Your Progress',
    totalPoints: 'Total Points',
    completedChallenges: 'Completed Challenges',
    currentLevel: 'Current Level',
    availableLevels: 'Available Levels',
    startLevel: 'Start Level',
    continueLevel: 'Continue',

    // Common
    loading: 'Loading...',
    submit: 'Submit',
    cancel: 'Cancel',
    back: 'Back'
  },
  zh: {
    // Navigation
    home: '首页',
    dashboard: '控制台',
    leaderboard: '排行榜',
    login: '登录',
    register: '注册',
    logout: '退出',
    language: '语言',

    // Home page
    homeTitle: 'Excel 学习平台',
    homeSubtitle: '通过互动挑战和游戏化学习掌握 Excel 技能',
    heroTitle: '通过',
    heroTitleHighlight: '互动学习掌握 Excel',
    heroDescription: '通过游戏化挑战和实践练习学习 Excel 技能。从基础公式到高级数据分析，逐步成为 Excel 专家。',
    continueLearning: '继续学习',
    startLearning: '开始学习',
    signIn: '登录',
    whyChoose: '为什么选择 Excel 大师？',
    learningJourney: '您的学习之旅',
    learningJourneyDesc: '通过 5 个综合等级的学习，从 Excel 基础到高级数据分析',

    // Features
    interactiveLearningTitle: '互动学习',
    interactiveLearningDesc: '通过我们的互动电子表格模拟器进行实践学习 Excel。',
    gamifiedChallengesTitle: '游戏化挑战',
    gamifiedChallengesDesc: '通过我们引人入胜的挑战系统逐级进步并解锁新技能。',
    trackProgressTitle: '跟踪进度',
    trackProgressDesc: '监控您的学习进程并在排行榜上与他人竞争。',
    communityLearningTitle: '社区学习',
    communityLearningDesc: '与数千名学习者一起掌握 Excel 技能。',

    // Level names and descriptions
    level1Title: 'Excel 基础',
    level2Title: '公式与函数',
    level3Title: '数据管理',
    level4Title: '高级函数',
    level5Title: '数据分析',

    // Learning path
    interfaceNavigation: '界面与导航',
    sumAverageIf: 'SUM、AVERAGE、IF',
    sortingFiltering: '排序与筛选',
    vlookupIndexMatch: 'VLOOKUP、INDEX/MATCH',
    pivotTablesCharts: '数据透视表与图表',

    // Dashboard
    welcomeBack: '欢迎回来',
    yourProgress: '您的进度',
    totalPoints: '总积分',
    completedChallenges: '已完成挑战',
    currentLevel: '当前等级',
    availableLevels: '可用等级',
    startLevel: '开始等级',
    continueLevel: '继续',

    // Common
    loading: '加载中...',
    submit: '提交',
    cancel: '取消',
    back: '返回'
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    return localStorage.getItem('language') || 'en';
  });

  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  const t = (key) => {
    return translations[language][key] || key;
  };

  const toggleLanguage = () => {
    setLanguage(prev => prev === 'en' ? 'zh' : 'en');
  };

  const value = {
    language,
    setLanguage,
    toggleLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
