import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

const translations = {
  en: {
    // Navigation
    home: 'Home',
    dashboard: 'Dashboard',
    leaderboard: 'Leaderboard',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    language: 'Language',

    // Home page
    homeTitle: 'Excel Learning Platform',
    homeSubtitle: 'Master Excel skills through interactive challenges and gamified learning',
    heroTitle: 'Master Excel Through',
    heroTitleHighlight: 'Interactive Learning',
    heroDescription: 'Learn Excel skills through gamified challenges and hands-on practice. From basic formulas to advanced data analysis, become an Excel expert step by step.',
    continueLearning: 'Continue Learning',
    startLearning: 'Start Learning',
    signIn: 'Sign In',
    whyChoose: 'Why Choose Excel Master?',
    learningJourney: 'Your Learning Journey',
    learningJourneyDesc: 'Progress through 5 comprehensive levels, from Excel basics to advanced data analysis',

    // Features
    interactiveLearningTitle: 'Interactive Learning',
    interactiveLearningDesc: 'Learn Excel through hands-on practice with our interactive spreadsheet simulator.',
    gamifiedChallengesTitle: 'Gamified Challenges',
    gamifiedChallengesDesc: 'Progress through levels and unlock new skills with our engaging challenge system.',
    trackProgressTitle: 'Track Progress',
    trackProgressDesc: 'Monitor your learning journey and compete with others on the leaderboard.',
    communityLearningTitle: 'Community Learning',
    communityLearningDesc: 'Join thousands of learners mastering Excel skills together.',

    // Level names and descriptions
    level1Title: 'Excel Basics',
    level2Title: 'Formulas & Functions',
    level3Title: 'Data Management',
    level4Title: 'Advanced Functions',
    level5Title: 'Data Analysis',

    // Learning path
    interfaceNavigation: 'Interface & Navigation',
    sumAverageIf: 'SUM, AVERAGE, IF',
    sortingFiltering: 'Sorting & Filtering',
    vlookupIndexMatch: 'VLOOKUP, INDEX/MATCH',
    pivotTablesCharts: 'Pivot Tables & Charts',

    // Dashboard
    welcomeBackDashboard: 'Welcome back',
    yourProgress: 'Your Progress',
    totalPoints: 'Total Points',
    completedChallenges: 'Completed Challenges',
    currentLevel: 'Current Level',
    availableLevels: 'Available Levels',
    startLevel: 'Start Level',
    continueLevel: 'Continue',
    progress: 'Progress',
    overallProgress: 'Overall Progress',
    challengesCompleted: 'challenges completed',
    learningLevels: 'Learning Levels',
    locked: 'Locked',
    quickActions: 'Quick Actions',
    viewLeaderboard: 'View Leaderboard',
    practiceMode: 'Practice Mode',
    continueYourExcelLearningJourney: 'Continue your Excel learning journey',

    // Leaderboard
    leaderboardPage: 'Leaderboard',
    seeHowYouRank: 'See how you rank against other Excel learners',
    totalLearners: 'Total Learners',
    topScore: 'Top Score',
    mostChallenges: 'Most Challenges',
    topPerformers: 'Top Performers',
    noLearnersYet: 'No learners yet. Be the first to start learning!',
    joined: 'Joined',
    pts: 'pts',
    challenges: 'challenges',
    wantToClimb: 'Want to climb the leaderboard? Keep learning and completing challenges!',

    // Level page
    backToLevel: 'Back to Level',
    backToDashboard: 'Back to Dashboard',
    levelNotFound: 'Level Not Found',
    pointsEarned: 'points earned',
    challengeOf: 'Challenge',
    of: 'of',
    startChallenge: 'Start Challenge',
    review: 'Review',
    congratulationsLevelComplete: 'Congratulations! Level Complete! 🎉',
    youveCompletedAllChallenges: 'You\'ve completed all challenges in this level and earned',
    pointsExclamation: 'points!',
    continueToNextLevel: 'Continue to Next Level',

    // Login page
    welcomeBackLogin: 'Welcome Back',
    signInToContinue: 'Sign in to continue your Excel learning journey',
    emailAddress: 'Email Address',
    password: 'Password',
    enterYourEmail: 'Enter your email',
    enterYourPassword: 'Enter your password',
    signingIn: 'Signing In...',
    signInButton: 'Sign In',
    dontHaveAccount: 'Don\'t have an account?',
    signUpHere: 'Sign up here',

    // Register page
    joinExcelMaster: 'Join Excel Master',
    createAccountAndStart: 'Create your account and start learning Excel today',
    username: 'Username',
    chooseUsername: 'Choose a username',
    confirmPassword: 'Confirm Password',
    createPassword: 'Create a password',
    confirmYourPassword: 'Confirm your password',
    creatingAccount: 'Creating Account...',
    createAccount: 'Create Account',
    alreadyHaveAccount: 'Already have an account?',
    signInHere: 'Sign in here',
    passwordsDoNotMatch: 'Passwords do not match',
    passwordTooShort: 'Password must be at least 6 characters long',

    // Challenge content translations
    // Level 1 challenges
    'challenge_1-1_title': 'Navigate the Interface',
    'challenge_1-1_description': 'Learn to navigate Excel\'s interface and understand basic components',
    'challenge_1-1_instructions': 'Click on cell A1, then navigate to cell C3 using arrow keys',
    'challenge_1-2_title': 'Enter and Edit Data',
    'challenge_1-2_description': 'Practice entering and editing data in cells',
    'challenge_1-2_instructions': 'Enter "Hello World" in cell A1, then edit it to "Hello Excel"',
    'challenge_1-3_title': 'Basic Formatting',
    'challenge_1-3_description': 'Learn to format cells with bold, italic, and colors',
    'challenge_1-3_instructions': 'Make cell A1 bold, cell B1 italic, and cell C1 with yellow background',
    'challenge_1-4_title': 'Cell References',
    'challenge_1-4_description': 'Understand how to reference cells in formulas',
    'challenge_1-4_instructions': 'In cell B1, create a formula that references cell A1',

    // Level 2 challenges
    'challenge_2-1_title': 'SUM Function',
    'challenge_2-1_description': 'Learn to use the SUM function',
    'challenge_2-1_instructions': 'Enter numbers 1-5 in cells A1:A5, then use SUM function in A6',
    'challenge_2-2_title': 'AVERAGE Function',
    'challenge_2-2_description': 'Calculate averages using the AVERAGE function',
    'challenge_2-2_instructions': 'Use the AVERAGE function to find the average of numbers in A1:A5',
    'challenge_2-3_title': 'IF Function',
    'challenge_2-3_description': 'Learn conditional logic with IF function',
    'challenge_2-3_instructions': 'In B1, create an IF formula: if A1>10, show "High", otherwise "Low"',
    'challenge_2-4_title': 'COUNT Function',
    'challenge_2-4_description': 'Count cells with numbers using COUNT function',
    'challenge_2-4_instructions': 'Use COUNT function to count numeric values in range A1:A10',

    // Level data translations
    'level_1_title': 'Excel Basics',
    'level_1_description': 'Learn the fundamentals of Excel',
    'level_2_title': 'Formulas & Functions',
    'level_2_description': 'Master Excel formulas and basic functions',
    'level_3_title': 'Data Management',
    'level_3_description': 'Learn to organize and manipulate data',
    'level_4_title': 'Advanced Functions',
    'level_4_description': 'Master complex Excel functions',
    'level_5_title': 'Data Analysis',
    'level_5_description': 'Advanced data analysis and visualization',

    // Challenge page
    challengeNotFound: 'Challenge Not Found',
    instructions: 'Instructions',
    tip: 'Tip',
    navigationTip: 'Use arrow keys to navigate between cells',
    formattingTip: 'Use the formatting toolbar to apply styles',
    excelWorkspace: 'Excel Workspace',
    reset: 'Reset',
    youEarned: 'You earned',
    nextChallenge: 'Next Challenge',
    checkingSolution: 'Checking Solution...',
    submitSolution: 'Submit Solution',

    // Level and difficulty translations
    points: 'pts',
    earned: 'Earned',
    beginner: 'Beginner',
    intermediate: 'Intermediate',
    advanced: 'Advanced',
    expert: 'Expert',

    // Level page specific
    level: 'Level',
    pointsEarned: 'points earned',
    progress: 'Progress',
    challenges: 'challenges',
    challengeOf: 'Challenge',
    of: 'of',
    review: 'Review',
    startChallenge: 'Start Challenge',
    locked: 'Locked',
    congratulationsLevelComplete: 'Congratulations! Level Complete!',
    youveCompletedAllChallenges: 'You\'ve completed all challenges and earned',
    pointsExclamation: 'points!',
    continueToNextLevel: 'Continue to Next Level',

    // Navigation and common actions
    backToLevel: 'Back to Level',
    backToDashboard: 'Back to Dashboard',
    signInButton: 'Sign In',
    welcomeBackLogin: 'Welcome Back',
    signInToContinue: 'Sign in to continue your Excel learning journey',
    emailAddress: 'Email Address',
    enterYourEmail: 'Enter your email',
    signingIn: 'Signing In...',

    // Level specific translations (matching backend data structure)
    'level_1_title': 'Excel 基础',
    'level_1_description': '学习 Excel 的基本知识',
    'level_2_title': '公式与函数',
    'level_2_description': '掌握 Excel 公式和基本函数',
    'level_3_title': '数据管理',
    'level_3_description': '学习组织和处理数据',
    'level_4_title': '高级函数',
    'level_4_description': '掌握复杂的 Excel 函数',
    'level_5_title': '数据分析',
    'level_5_description': '高级数据分析和可视化',

    // Challenge specific translations (matching backend data structure)
    'challenge_1-1_title': '界面导航',
    'challenge_1-1_description': '学习导航 Excel 界面并了解基本组件',
    'challenge_1-1_instructions': '点击单元格 A1，然后使用箭头键导航到单元格 C3',
    'challenge_1-2_title': '输入和编辑数据',
    'challenge_1-2_description': '练习在单元格中输入和编辑数据',
    'challenge_1-2_instructions': '在单元格 A1 中输入"Hello World"，然后编辑为"Hello Excel"',
    'challenge_1-3_title': '基本单元格格式化',
    'challenge_1-3_description': '学习使用粗体、斜体和颜色格式化单元格',
    'challenge_1-3_instructions': '将单元格 A1 设为粗体并将背景颜色改为黄色',
    'challenge_1-4_title': '简单计算',
    'challenge_1-4_description': '执行基本算术运算',
    'challenge_1-4_instructions': '在 A1 中输入 10，在 B1 中输入 20，在 C1 中创建公式将它们相加',
    'challenge_2-1_title': 'SUM 函数',
    'challenge_2-1_description': '学习使用 SUM 函数',
    'challenge_2-1_instructions': '在单元格 A1:A5 中输入数字 1-5，然后在 A6 中使用 SUM 函数',
    'challenge_2-2_title': 'AVERAGE 函数',
    'challenge_2-2_description': '使用 AVERAGE 函数计算平均值',
    'challenge_2-2_instructions': '使用 AVERAGE 函数计算 A1:A5 中数字的平均值',
    'challenge_2-3_title': 'IF 函数',
    'challenge_2-3_description': '学习使用 IF 函数的条件逻辑',
    'challenge_2-3_instructions': '在 B1 中创建 IF 公式：如果 A1>10，显示"High"，否则显示"Low"',
    'challenge_2-4_title': 'COUNT 函数',
    'challenge_2-4_description': '使用 COUNT 函数计算包含数字的单元格',
    'challenge_2-4_instructions': '使用 COUNT 函数计算范围 A1:A10 中的数值',

    // Level 3 challenges
    'challenge_3-1_title': '数据排序',
    'challenge_3-1_description': '学习在 Excel 中排序数据',
    'challenge_3-1_instructions': '按列 B 升序排列 A1:C10 中的数据',
    'challenge_3-2_title': '数据筛选',
    'challenge_3-2_description': '对数据应用筛选器',
    'challenge_3-2_instructions': '应用筛选器显示列 C 中大于 100 的值',
    'challenge_3-3_title': '数据验证',
    'challenge_3-3_description': '设置数据验证规则',
    'challenge_3-3_instructions': '在单元格 A1 中创建下拉列表，选项为：红色、绿色、蓝色',
    'challenge_3-4_title': '查找和替换',
    'challenge_3-4_description': '使用查找和替换功能',
    'challenge_3-4_instructions': '将工作表中所有"old"替换为"new"',

    // Level 4 challenges
    'challenge_4-1_title': 'VLOOKUP 函数',
    'challenge_4-1_description': '掌握 VLOOKUP 函数',
    'challenge_4-1_instructions': '使用 VLOOKUP 查找单元格 A1 中产品 ID 的价格',
    'challenge_4-2_title': 'INDEX MATCH',
    'challenge_4-2_description': '学习 INDEX 和 MATCH 函数',
    'challenge_4-2_instructions': '使用 INDEX 和 MATCH 比 VLOOKUP 更灵活地查找值',
    'challenge_4-3_title': 'CONCATENATE 函数',
    'challenge_4-3_description': '合并多个单元格的文本',
    'challenge_4-3_instructions': '将名字 (A1) 和姓氏 (B1) 用空格连接起来',
    'challenge_4-4_title': 'SUMIF 函数',
    'challenge_4-4_description': '根据条件求和',
    'challenge_4-4_instructions': '对列 A 等于"Sales"的所有列 B 值求和',

    // Level 5 challenges
    'challenge_5-1_title': '数据透视表',
    'challenge_5-1_description': '创建和自定义数据透视表',
    'challenge_5-1_instructions': '创建数据透视表按地区和产品汇总销售数据',
    'challenge_5-2_title': '图表和图形',
    'challenge_5-2_description': '创建专业图表',
    'challenge_5-2_instructions': '创建显示月度销售数据的柱状图',
    'challenge_5-3_title': '条件格式',
    'challenge_5-3_description': '应用条件格式规则',
    'challenge_5-3_instructions': '将范围 A1:A10 中大于 50 的单元格突出显示为绿色',
    'challenge_5-4_title': '数据分析工具',
    'challenge_5-4_description': '使用 Excel 的数据分析功能',
    'challenge_5-4_instructions': '创建列 A 中数据的频率分布',

    // Common
    loading: 'Loading...',
    submit: 'Submit',
    cancel: 'Cancel',
    back: 'Back',

    // Excel Simulator
    cell: 'Cell',
    enterValue: 'Enter value',
    undo: 'Undo',
    redo: 'Redo',
    selected: 'Selected',
    ready: 'Ready',
    bold: 'Bold',
    italic: 'Italic',
    underline: 'Underline',
    noColor: 'No Color',
    yellow: 'Yellow',
    lightBlue: 'Light Blue',
    lightGreen: 'Light Green',
    pink: 'Pink',
    orange: 'Orange'
  },
  zh: {
    // Navigation
    home: '首页',
    dashboard: '控制台',
    leaderboard: '排行榜',
    login: '登录',
    register: '注册',
    logout: '退出',
    language: '语言',

    // Home page
    homeTitle: 'Excel 学习平台',
    homeSubtitle: '通过互动挑战和游戏化学习掌握 Excel 技能',
    heroTitle: '通过',
    heroTitleHighlight: '互动学习掌握 Excel',
    heroDescription: '通过游戏化挑战和实践练习学习 Excel 技能。从基础公式到高级数据分析，逐步成为 Excel 专家。',
    continueLearning: '继续学习',
    startLearning: '开始学习',
    signIn: '登录',
    whyChoose: '为什么选择 Excel 大师？',
    learningJourney: '您的学习之旅',
    learningJourneyDesc: '通过 5 个综合级别的学习，从 Excel 基础到高级数据分析',

    // Features
    interactiveLearningTitle: '互动学习',
    interactiveLearningDesc: '通过我们的互动电子表格模拟器进行实践学习 Excel。',
    gamifiedChallengesTitle: '游戏化挑战',
    gamifiedChallengesDesc: '通过我们引人入胜的挑战系统逐级进步并解锁新技能。',
    trackProgressTitle: '跟踪进度',
    trackProgressDesc: '监控您的学习进程并在排行榜上与他人竞争。',
    communityLearningTitle: '社区学习',
    communityLearningDesc: '与数千名学习者一起掌握 Excel 技能。',

    // Level names and descriptions
    level1Title: 'Excel 基础',
    level2Title: '公式与函数',
    level3Title: '数据管理',
    level4Title: '高级函数',
    level5Title: '数据分析',

    // Learning path
    interfaceNavigation: '界面与导航',
    sumAverageIf: 'SUM、AVERAGE、IF',
    sortingFiltering: '排序与筛选',
    vlookupIndexMatch: 'VLOOKUP、INDEX/MATCH',
    pivotTablesCharts: '数据透视表与图表',

    // Dashboard
    welcomeBackDashboard: '欢迎回来',
    yourProgress: '您的进度',
    totalPoints: '总积分',
    completedChallenges: '已完成挑战',
    currentLevel: '当前等级',
    availableLevels: '可用等级',
    startLevel: '开始等级',
    continueLevel: '继续',
    progress: '进度',
    overallProgress: '总体进度',
    challengesCompleted: '个挑战已完成',
    learningLevels: '学习等级',
    locked: '已锁定',
    quickActions: '快速操作',
    viewLeaderboard: '查看排行榜',
    practiceMode: '练习模式',
    continueYourExcelLearningJourney: '继续您的 Excel 学习之旅',

    // Leaderboard
    leaderboardPage: '排行榜',
    seeHowYouRank: '查看您在其他 Excel 学习者中的排名',
    totalLearners: '总学习者',
    topScore: '最高分',
    mostChallenges: '最多挑战',
    topPerformers: '顶级表现者',
    noLearnersYet: '还没有学习者。成为第一个开始学习的人！',
    joined: '加入于',
    pts: '分',
    challenges: '个挑战',
    wantToClimb: '想要攀登排行榜？继续学习并完成挑战！',

    // Level page
    backToLevel: '返回等级',
    backToDashboard: '返回控制台',
    levelNotFound: '未找到等级',
    pointsEarned: '分已获得',
    challengeOf: '挑战',
    of: '共',
    startChallenge: '开始挑战',
    review: '复习',
    congratulationsLevelComplete: '恭喜！等级完成！🎉',
    youveCompletedAllChallenges: '您已完成此等级的所有挑战并获得了',
    pointsExclamation: '分！',
    continueToNextLevel: '继续下一等级',

    // Login page
    welcomeBackLogin: '欢迎回来',
    signInToContinue: '登录以继续您的 Excel 学习之旅',
    emailAddress: '邮箱地址',
    password: '密码',
    enterYourEmail: '输入您的邮箱',
    enterYourPassword: '输入您的密码',
    signingIn: '登录中...',
    signInButton: '登录',
    dontHaveAccount: '还没有账户？',
    signUpHere: '在这里注册',

    // Register page
    joinExcelMaster: '加入 Excel 大师',
    createAccountAndStart: '创建您的账户并开始学习 Excel',
    username: '用户名',
    chooseUsername: '选择一个用户名',
    confirmPassword: '确认密码',
    createPassword: '创建密码',
    confirmYourPassword: '确认您的密码',
    creatingAccount: '创建账户中...',
    createAccount: '创建账户',
    alreadyHaveAccount: '已有账户？',
    signInHere: '在这里登录',
    passwordsDoNotMatch: '密码不匹配',
    passwordTooShort: '密码必须至少6个字符',

    // Challenge content translations
    // Level 1 challenges
    'challenge_1-1_title': '界面导航',
    'challenge_1-1_description': '学习导航 Excel 界面并了解基本组件',
    'challenge_1-1_instructions': '点击单元格 A1，然后使用方向键导航到单元格 C3',
    'challenge_1-2_title': '输入和编辑数据',
    'challenge_1-2_description': '练习在单元格中输入和编辑数据',
    'challenge_1-2_instructions': '在单元格 A1 中输入 "Hello World"，然后编辑为 "Hello Excel"',
    'challenge_1-3_title': '基本格式设置',
    'challenge_1-3_description': '学习使用粗体、斜体和颜色格式化单元格',
    'challenge_1-3_instructions': '将单元格 A1 设为粗体，B1 设为斜体，C1 设为黄色背景',
    'challenge_1-4_title': '单元格引用',
    'challenge_1-4_description': '了解如何在公式中引用单元格',
    'challenge_1-4_instructions': '在单元格 B1 中创建一个引用单元格 A1 的公式',

    // Level 2 challenges
    'challenge_2-1_title': 'SUM 函数',
    'challenge_2-1_description': '学习使用 SUM 函数',
    'challenge_2-1_instructions': '在单元格 A1:A5 中输入数字 1-5，然后在 A6 中使用 SUM 函数',
    'challenge_2-2_title': 'AVERAGE 函数',
    'challenge_2-2_description': '使用 AVERAGE 函数计算平均值',
    'challenge_2-2_instructions': '使用 AVERAGE 函数计算 A1:A5 中数字的平均值',
    'challenge_2-3_title': 'IF 函数',
    'challenge_2-3_description': '学习使用 IF 函数的条件逻辑',
    'challenge_2-3_instructions': '在 B1 中创建 IF 公式：如果 A1>10，显示"高"，否则显示"低"',
    'challenge_2-4_title': 'COUNT 函数',
    'challenge_2-4_description': '使用 COUNT 函数计算包含数字的单元格',
    'challenge_2-4_instructions': '使用 COUNT 函数计算范围 A1:A10 中的数值个数',

    // Level data translations
    'level_1_title': 'Excel 基础',
    'level_1_description': '学习 Excel 的基础知识',
    'level_2_title': '公式与函数',
    'level_2_description': '掌握 Excel 公式和基本函数',
    'level_3_title': '数据管理',
    'level_3_description': '学习组织和操作数据',
    'level_4_title': '高级函数',
    'level_4_description': '掌握复杂的 Excel 函数',
    'level_5_title': '数据分析',
    'level_5_description': '高级数据分析和可视化',

    // Challenge page
    challengeNotFound: '未找到挑战',
    instructions: '操作说明',
    tip: '提示',
    navigationTip: '使用方向键在单元格之间导航',
    formattingTip: '使用格式工具栏应用样式',
    excelWorkspace: 'Excel 工作区',
    reset: '重置',
    youEarned: '您获得了',
    nextChallenge: '下一个挑战',
    checkingSolution: '检查解决方案中...',
    submitSolution: '提交解决方案',

    // Level and difficulty translations
    points: '分',
    earned: '已获得',
    beginner: '初级',
    intermediate: '中级',
    advanced: '高级',
    expert: '专家',

    // Level page specific
    level: '关卡',
    pointsEarned: '已获得积分',
    progress: '进度',
    challenges: '挑战',
    challengeOf: '挑战',
    of: '共',
    review: '复习',
    startChallenge: '开始挑战',
    locked: '已锁定',
    congratulationsLevelComplete: '恭喜！关卡完成！',
    youveCompletedAllChallenges: '您已完成所有挑战并获得了',
    pointsExclamation: '积分！',
    continueToNextLevel: '继续下一关',

    // Navigation and common actions
    backToLevel: '返回关卡',
    backToDashboard: '返回控制台',
    signInButton: '登录',
    welcomeBackLogin: '欢迎回来',
    signInToContinue: '登录以继续您的 Excel 学习之旅',
    emailAddress: '邮箱地址',
    enterYourEmail: '输入您的邮箱',
    signingIn: '登录中...',

    // Level specific translations
    level_1_title: 'Excel 基础',
    level_1_description: '学习 Excel 的基本知识',
    level_2_title: '公式与函数',
    level_2_description: '掌握 Excel 公式和基本函数',
    level_3_title: '数据分析',
    level_3_description: '有效地分析和处理数据',
    level_4_title: '图表与可视化',
    level_4_description: '创建引人注目的图表和可视化',
    level_5_title: '高级功能',
    level_5_description: '掌握 Excel 高级功能和自动化',

    // Challenge specific translations
    challenge_1_1_title: '基本导航',
    challenge_1_1_description: '学习导航 Excel 界面',
    challenge_1_1_instructions: '点击单元格 B2 并输入您的姓名',
    challenge_1_2_title: '数据输入',
    challenge_1_2_description: '练习输入不同类型的数据',
    challenge_1_2_instructions: '在指定单元格中输入数字、文本和日期',
    challenge_1_3_title: '基本格式化',
    challenge_1_3_description: '对单元格应用基本格式',
    challenge_1_3_instructions: '将单元格 A1 设为粗体并将背景颜色改为黄色',
    challenge_1_4_title: '简单公式',
    challenge_1_4_description: '创建您的第一个 Excel 公式',
    challenge_1_4_instructions: '在单元格 C1 中，创建一个公式来计算 A1 和 B1 的和',

    // Common
    loading: '加载中...',
    submit: '提交',
    cancel: '取消',
    back: '返回',

    // Excel Simulator
    cell: '单元格',
    enterValue: '输入值',
    undo: '撤销',
    redo: '重做',
    selected: '已选择',
    ready: '就绪',
    bold: '粗体',
    italic: '斜体',
    underline: '下划线',
    noColor: '无颜色',
    yellow: '黄色',
    lightBlue: '浅蓝色',
    lightGreen: '浅绿色',
    pink: '粉色',
    orange: '橙色'
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    return localStorage.getItem('language') || 'en';
  });

  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  const t = (key) => {
    return translations[language][key] || key;
  };

  const toggleLanguage = () => {
    setLanguage(prev => prev === 'en' ? 'zh' : 'en');
  };

  const value = {
    language,
    setLanguage,
    toggleLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
