import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

const translations = {
  en: {
    homeTitle: 'Excel Learning Platform',
    homeSubtitle: 'Master Excel skills through interactive challenges and gamified learning',
    whyChoose: 'Why Choose Excel Master?',
    interactiveLearningTitle: 'Interactive Learning',
    gamifiedChallengesTitle: 'Gamified Challenges',
    trackProgressTitle: 'Track Progress'
  },
  zh: {
    homeTitle: 'Excel 学习平台',
    homeSubtitle: '通过互动挑战和游戏化学习掌握 Excel 技能',
    whyChoose: '为什么选择 Excel 大师？',
    interactiveLearningTitle: '互动学习',
    gamifiedChallengesTitle: '游戏化挑战',
    trackProgressTitle: '跟踪进度'
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    return localStorage.getItem('language') || 'en';
  });

  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  const t = (key) => {
    return translations[language][key] || key;
  };

  const toggleLanguage = () => {
    setLanguage(prev => prev === 'en' ? 'zh' : 'en');
  };

  const value = {
    language,
    setLanguage,
    toggleLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
