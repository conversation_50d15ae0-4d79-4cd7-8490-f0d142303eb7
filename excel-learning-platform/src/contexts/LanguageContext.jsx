import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

const translations = {
  en: {
    // Navigation
    home: 'Home',
    dashboard: 'Dashboard',
    leaderboard: 'Leaderboard',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    language: 'Language',

    // Home page
    homeTitle: 'Excel Learning Platform',
    homeSubtitle: 'Master Excel skills through interactive challenges and gamified learning',
    heroTitle: 'Master Excel Through',
    heroTitleHighlight: 'Interactive Learning',
    heroDescription: 'Learn Excel skills through gamified challenges and hands-on practice. From basic formulas to advanced data analysis, become an Excel expert step by step.',
    continueLearning: 'Continue Learning',
    startLearning: 'Start Learning',
    signIn: 'Sign In',
    whyChoose: 'Why Choose Excel Master?',
    learningJourney: 'Your Learning Journey',
    learningJourneyDesc: 'Progress through 5 comprehensive levels, from Excel basics to advanced data analysis',

    // Features
    interactiveLearningTitle: 'Interactive Learning',
    interactiveLearningDesc: 'Learn Excel through hands-on practice with our interactive spreadsheet simulator.',
    gamifiedChallengesTitle: 'Gamified Challenges',
    gamifiedChallengesDesc: 'Progress through levels and unlock new skills with our engaging challenge system.',
    trackProgressTitle: 'Track Progress',
    trackProgressDesc: 'Monitor your learning journey and compete with others on the leaderboard.',
    communityLearningTitle: 'Community Learning',
    communityLearningDesc: 'Join thousands of learners mastering Excel skills together.',

    // Level names and descriptions
    level1Title: 'Excel Basics',
    level2Title: 'Formulas & Functions',
    level3Title: 'Data Management',
    level4Title: 'Advanced Functions',
    level5Title: 'Data Analysis',

    // Learning path
    interfaceNavigation: 'Interface & Navigation',
    sumAverageIf: 'SUM, AVERAGE, IF',
    sortingFiltering: 'Sorting & Filtering',
    vlookupIndexMatch: 'VLOOKUP, INDEX/MATCH',
    pivotTablesCharts: 'Pivot Tables & Charts',

    // Dashboard
    welcomeBack: 'Welcome back',
    yourProgress: 'Your Progress',
    totalPoints: 'Total Points',
    completedChallenges: 'Completed Challenges',
    currentLevel: 'Current Level',
    availableLevels: 'Available Levels',
    startLevel: 'Start Level',
    continueLevel: 'Continue',
    progress: 'Progress',
    overallProgress: 'Overall Progress',
    challengesCompleted: 'challenges completed',
    learningLevels: 'Learning Levels',
    locked: 'Locked',
    quickActions: 'Quick Actions',
    viewLeaderboard: 'View Leaderboard',
    practiceMode: 'Practice Mode',
    continueYourExcelLearningJourney: 'Continue your Excel learning journey',

    // Leaderboard
    leaderboard: 'Leaderboard',
    seeHowYouRank: 'See how you rank against other Excel learners',
    totalLearners: 'Total Learners',
    topScore: 'Top Score',
    mostChallenges: 'Most Challenges',
    topPerformers: 'Top Performers',
    noLearnersYet: 'No learners yet. Be the first to start learning!',
    joined: 'Joined',
    pts: 'pts',
    challenges: 'challenges',
    wantToClimb: 'Want to climb the leaderboard? Keep learning and completing challenges!',

    // Level page
    backToLevel: 'Back to Level',
    backToDashboard: 'Back to Dashboard',
    levelNotFound: 'Level Not Found',
    pointsEarned: 'points earned',
    challengeOf: 'Challenge',
    of: 'of',
    startChallenge: 'Start Challenge',
    review: 'Review',
    congratulationsLevelComplete: 'Congratulations! Level Complete! 🎉',
    youveCompletedAllChallenges: 'You\'ve completed all challenges in this level and earned',
    pointsExclamation: 'points!',
    continueToNextLevel: 'Continue to Next Level',

    // Common
    loading: 'Loading...',
    submit: 'Submit',
    cancel: 'Cancel',
    back: 'Back',

    // Excel Simulator
    cell: 'Cell',
    enterValue: 'Enter value',
    bold: 'Bold',
    italic: 'Italic',
    underline: 'Underline',
    noColor: 'No Color',
    yellow: 'Yellow',
    lightBlue: 'Light Blue',
    lightGreen: 'Light Green',
    pink: 'Pink',
    orange: 'Orange'
  },
  zh: {
    // Navigation
    home: '首页',
    dashboard: '控制台',
    leaderboard: '排行榜',
    login: '登录',
    register: '注册',
    logout: '退出',
    language: '语言',

    // Home page
    homeTitle: 'Excel 学习平台',
    homeSubtitle: '通过互动挑战和游戏化学习掌握 Excel 技能',
    heroTitle: '通过',
    heroTitleHighlight: '互动学习掌握 Excel',
    heroDescription: '通过游戏化挑战和实践练习学习 Excel 技能。从基础公式到高级数据分析，逐步成为 Excel 专家。',
    continueLearning: '继续学习',
    startLearning: '开始学习',
    signIn: '登录',
    whyChoose: '为什么选择 Excel 大师？',
    learningJourney: '您的学习之旅',
    learningJourneyDesc: '通过 5 个综合等级的学习，从 Excel 基础到高级数据分析',

    // Features
    interactiveLearningTitle: '互动学习',
    interactiveLearningDesc: '通过我们的互动电子表格模拟器进行实践学习 Excel。',
    gamifiedChallengesTitle: '游戏化挑战',
    gamifiedChallengesDesc: '通过我们引人入胜的挑战系统逐级进步并解锁新技能。',
    trackProgressTitle: '跟踪进度',
    trackProgressDesc: '监控您的学习进程并在排行榜上与他人竞争。',
    communityLearningTitle: '社区学习',
    communityLearningDesc: '与数千名学习者一起掌握 Excel 技能。',

    // Level names and descriptions
    level1Title: 'Excel 基础',
    level2Title: '公式与函数',
    level3Title: '数据管理',
    level4Title: '高级函数',
    level5Title: '数据分析',

    // Learning path
    interfaceNavigation: '界面与导航',
    sumAverageIf: 'SUM、AVERAGE、IF',
    sortingFiltering: '排序与筛选',
    vlookupIndexMatch: 'VLOOKUP、INDEX/MATCH',
    pivotTablesCharts: '数据透视表与图表',

    // Dashboard
    welcomeBack: '欢迎回来',
    yourProgress: '您的进度',
    totalPoints: '总积分',
    completedChallenges: '已完成挑战',
    currentLevel: '当前等级',
    availableLevels: '可用等级',
    startLevel: '开始等级',
    continueLevel: '继续',
    progress: '进度',
    overallProgress: '总体进度',
    challengesCompleted: '个挑战已完成',
    learningLevels: '学习等级',
    locked: '已锁定',
    quickActions: '快速操作',
    viewLeaderboard: '查看排行榜',
    practiceMode: '练习模式',
    continueYourExcelLearningJourney: '继续您的 Excel 学习之旅',

    // Leaderboard
    leaderboard: '排行榜',
    seeHowYouRank: '查看您在其他 Excel 学习者中的排名',
    totalLearners: '总学习者',
    topScore: '最高分',
    mostChallenges: '最多挑战',
    topPerformers: '顶级表现者',
    noLearnersYet: '还没有学习者。成为第一个开始学习的人！',
    joined: '加入于',
    pts: '分',
    challenges: '个挑战',
    wantToClimb: '想要攀登排行榜？继续学习并完成挑战！',

    // Level page
    backToLevel: '返回等级',
    backToDashboard: '返回控制台',
    levelNotFound: '未找到等级',
    pointsEarned: '分已获得',
    challengeOf: '挑战',
    of: '共',
    startChallenge: '开始挑战',
    review: '复习',
    congratulationsLevelComplete: '恭喜！等级完成！🎉',
    youveCompletedAllChallenges: '您已完成此等级的所有挑战并获得了',
    pointsExclamation: '分！',
    continueToNextLevel: '继续下一等级',

    // Common
    loading: '加载中...',
    submit: '提交',
    cancel: '取消',
    back: '返回',

    // Excel Simulator
    cell: '单元格',
    enterValue: '输入值',
    bold: '粗体',
    italic: '斜体',
    underline: '下划线',
    noColor: '无颜色',
    yellow: '黄色',
    lightBlue: '浅蓝色',
    lightGreen: '浅绿色',
    pink: '粉色',
    orange: '橙色'
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    return localStorage.getItem('language') || 'en';
  });

  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  const t = (key) => {
    return translations[language][key] || key;
  };

  const toggleLanguage = () => {
    setLanguage(prev => prev === 'en' ? 'zh' : 'en');
  };

  const value = {
    language,
    setLanguage,
    toggleLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
