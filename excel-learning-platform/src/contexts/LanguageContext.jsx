import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// 翻译数据
const translations = {
  en: {
    // Navigation
    home: 'Home',
    dashboard: 'Dashboard',
    leaderboard: 'Leaderboard',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',

    // Common
    loading: 'Loading...',
    submit: 'Submit',
    cancel: 'Cancel',
    reset: 'Reset',
    continue: 'Continue',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',

    // Home page
    homeTitle: 'Excel Learning Platform',
    homeSubtitle: 'Master Excel skills through interactive challenges and gamified learning',
    getStarted: 'Get Started',
    learnMore: 'Learn More',

    // Auth
    email: 'Email',
    password: 'Password',
    username: 'Userna<PERSON>',
    confirmPassword: 'Confirm Password',
    loginTitle: 'Login to Your Account',
    registerTitle: 'Create New Account',
    alreadyHaveAccount: 'Already have an account?',
    dontHaveAccount: "Don't have an account?",

    // Dashboard
    welcomeBack: 'Welcome back',
    yourProgress: 'Your Progress',
    totalPoints: 'Total Points',
    completedChallenges: 'Completed Challenges',
    currentLevel: 'Current Level',
    availableLevels: 'Available Levels',
    startLevel: 'Start Level',
    continueLevel: 'Continue',
    locked: 'Locked',

    // Levels
    level: 'Level',
    difficulty: 'Difficulty',
    beginner: 'Beginner',
    intermediate: 'Intermediate',
    advanced: 'Advanced',
    expert: 'Expert',

    // Level names and descriptions
    'level1Title': 'Excel Basics',
    'level1Description': 'Learn the fundamentals of Excel',
    'level2Title': 'Formulas & Functions',
    'level2Description': 'Master Excel formulas and basic functions',
    'level3Title': 'Data Management',
    'level3Description': 'Learn to organize and manipulate data',
    'level4Title': 'Advanced Functions',
    'level4Description': 'Master complex Excel functions',
    'level5Title': 'Data Analysis',
    'level5Description': 'Advanced data analysis and visualization',

    // Challenges
    challenge: 'Challenge',
    instructions: 'Instructions',
    points: 'points',
    pointsEarned: 'points earned',
    submitSolution: 'Submit Solution',
    checkingSolution: 'Checking...',
    resetWorkspace: 'Reset',
    continuelearning: 'Continue Learning',

    // Challenge feedback
    correct: 'Correct!',
    incorrect: 'Not quite right. Try again!',
    congratulations: 'Congratulations! Challenge Complete!',
    youEarned: 'You earned',

    // Excel Simulator
    excelWorkspace: 'Excel Workspace',
    formulaBar: 'Formula Bar',
    cell: 'Cell',
    enterValue: 'Enter value or formula...',
    selected: 'Selected',
    ready: 'Ready',

    // Navigation tips
    navigationTip: 'Tip: Use arrow keys (↑↓←→) to navigate between cells, or click on cells directly.',

    // Leaderboard
    leaderboardTitle: 'Leaderboard',
    rank: 'Rank',
    player: 'Player',
    score: 'Score',
    challenges: 'Challenges',

    // Errors
    errorOccurred: 'An error occurred',
    tryAgain: 'Please try again',
    challengeNotFound: 'Challenge Not Found',
    levelNotFound: 'Level Not Found',
    backToDashboard: 'Back to Dashboard',

    // Level completion
    levelComplete: 'Level Complete!',
    levelCompleteMessage: "You've completed all challenges in this level!",
    continueToNext: 'Continue to Next Level',

    // Language
    language: 'Language',
    english: 'English',
    chinese: '中文',

    // Excel Formatting
    bold: 'Bold',
    italic: 'Italic',
    underline: 'Underline',
    backgroundColor: 'Background Color',
    noColor: 'No Color',
    yellow: 'Yellow',
    lightBlue: 'Light Blue',
    lightGreen: 'Light Green',
    pink: 'Pink',
    orange: 'Orange',

    // Challenge specific
    nextChallenge: 'Next Challenge',
    formattingTip: 'Use the formatting toolbar above to make text bold, italic, or change background colors.',

    // Challenge titles and descriptions
    'challenge1-1Title': 'Cell Navigation',
    'challenge1-1Description': 'Learn to navigate between cells using arrow keys',
    'challenge1-1Instructions': 'Use arrow keys to navigate from cell A1 to cell C3',

    'challenge1-2Title': 'Data Entry',
    'challenge1-2Description': 'Practice entering data into Excel cells',
    'challenge1-2Instructions': 'Enter "Hello Excel" in cell A1',

    'challenge1-3Title': 'Cell Formatting',
    'challenge1-3Description': 'Learn to format cells with bold text and colors',
    'challenge1-3Instructions': 'Make cell A1 bold and change the background color to yellow'
  },

  zh: {
    // Navigation
    home: '首页',
    dashboard: '控制台',
    leaderboard: '排行榜',
    login: '登录',
    register: '注册',
    logout: '退出',

    // Common
    loading: '加载中...',
    submit: '提交',
    cancel: '取消',
    reset: '重置',
    continue: '继续',
    back: '返回',
    next: '下一个',
    previous: '上一个',
    save: '保存',
    edit: '编辑',
    delete: '删除',

    // Home page
    homeTitle: 'Excel 学习平台',
    homeSubtitle: '通过互动挑战和游戏化学习掌握 Excel 技能',
    getStarted: '开始学习',
    learnMore: '了解更多',

    // Auth
    email: '邮箱',
    password: '密码',
    username: '用户名',
    confirmPassword: '确认密码',
    loginTitle: '登录您的账户',
    registerTitle: '创建新账户',
    alreadyHaveAccount: '已有账户？',
    dontHaveAccount: '还没有账户？',

    // Dashboard
    welcomeBack: '欢迎回来',
    yourProgress: '您的进度',
    totalPoints: '总积分',
    completedChallenges: '已完成挑战',
    currentLevel: '当前等级',
    availableLevels: '可用等级',
    startLevel: '开始等级',
    continueLevel: '继续',
    locked: '已锁定',

    // Levels
    level: '等级',
    difficulty: '难度',
    beginner: '初级',
    intermediate: '中级',
    advanced: '高级',
    expert: '专家',

    // Level names and descriptions
    'level1Title': 'Excel 基础',
    'level1Description': '学习 Excel 的基本操作',
    'level2Title': '公式与函数',
    'level2Description': '掌握 Excel 公式和基本函数',
    'level3Title': '数据管理',
    'level3Description': '学习组织和处理数据',
    'level4Title': '高级函数',
    'level4Description': '掌握复杂的 Excel 函数',
    'level5Title': '数据分析',
    'level5Description': '高级数据分析和可视化',

    // Challenges
    challenge: '挑战',
    instructions: '操作说明',
    points: '积分',
    pointsEarned: '获得积分',
    submitSolution: '提交答案',
    checkingSolution: '检查中...',
    resetWorkspace: '重置',
    continuelearning: '继续学习',

    // Challenge feedback
    correct: '正确！',
    incorrect: '不太对，请再试一次！',
    congratulations: '恭喜！挑战完成！',
    youEarned: '您获得了',

    // Excel Simulator
    excelWorkspace: 'Excel 工作区',
    formulaBar: '公式栏',
    cell: '单元格',
    enterValue: '输入数值或公式...',
    selected: '已选择',
    ready: '就绪',

    // Navigation tips
    navigationTip: '提示：使用方向键（↑↓←→）在单元格间导航，或直接点击单元格。',

    // Leaderboard
    leaderboardTitle: '排行榜',
    rank: '排名',
    player: '玩家',
    score: '得分',
    challenges: '挑战',

    // Errors
    errorOccurred: '发生错误',
    tryAgain: '请重试',
    challengeNotFound: '未找到挑战',
    levelNotFound: '未找到等级',
    backToDashboard: '返回控制台',

    // Level completion
    levelComplete: '等级完成！',
    levelCompleteMessage: '您已完成此等级的所有挑战！',
    continueToNext: '继续下一等级',

    // Language
    language: '语言',
    english: 'English',
    chinese: '中文',

    // Excel Formatting
    bold: '粗体',
    italic: '斜体',
    underline: '下划线',
    backgroundColor: '背景颜色',
    noColor: '无颜色',
    yellow: '黄色',
    lightBlue: '浅蓝色',
    lightGreen: '浅绿色',
    pink: '粉色',
    orange: '橙色',

    // Challenge specific
    nextChallenge: '下一个挑战',
    formattingTip: '使用上方的格式工具栏来设置文本粗体、斜体或更改背景颜色。',

    // Challenge titles and descriptions
    'challenge1-1Title': '单元格导航',
    'challenge1-1Description': '学习使用方向键在单元格间导航',
    'challenge1-1Instructions': '使用方向键从单元格 A1 导航到单元格 C3',

    'challenge1-2Title': '数据输入',
    'challenge1-2Description': '练习在 Excel 单元格中输入数据',
    'challenge1-2Instructions': '在单元格 A1 中输入 "Hello Excel"',

    'challenge1-3Title': '单元格格式化',
    'challenge1-3Description': '学习设置单元格格式，包括粗体和颜色',
    'challenge1-3Instructions': '将单元格 A1 设置为粗体并将背景颜色改为黄色'
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    // 从 localStorage 获取保存的语言设置，默认为英文
    return localStorage.getItem('language') || 'en';
  });

  // 保存语言设置到 localStorage
  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  // 翻译函数
  const t = (key) => {
    return translations[language][key] || key;
  };

  // 切换语言
  const toggleLanguage = () => {
    setLanguage(prev => prev === 'en' ? 'zh' : 'en');
  };

  const value = {
    language,
    setLanguage,
    toggleLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
