import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

const translations = {
  en: {
    // Navigation
    home: 'Home',
    dashboard: 'Dashboard',
    leaderboard: 'Leaderboard',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    language: 'Language',

    // Home page
    homeTitle: 'Excel Learning Platform',
    homeSubtitle: 'Master Excel skills through interactive challenges and gamified learning',
    heroTitle: 'Master Excel with',
    heroTitleHighlight: 'Interactive Learning',
    heroDescription: 'Learn Excel skills through gamified challenges and hands-on practice. Progress from basic formulas to advanced data analysis and become an Excel expert.',
    continueLearning: 'Continue Learning',
    startLearning: 'Start Learning',
    signIn: 'Sign In',
    whyChoose: 'Why Choose Excel Master?',
    learningJourney: 'Your Learning Journey',
    learningJourneyDesc: 'Progress through 5 comprehensive levels from Excel basics to advanced data analysis',

    // Features
    interactiveLearningTitle: 'Interactive Learning',
    interactiveLearningDesc: 'Learn Excel hands-on with our interactive spreadsheet simulator.',
    gamifiedChallengesTitle: 'Gamified Challenges',
    gamifiedChallengesDesc: 'Progress through levels and unlock new skills with our engaging challenge system.',
    trackProgressTitle: 'Track Progress',
    trackProgressDesc: 'Monitor your learning journey and compete with others on the leaderboard.',
    communityLearningTitle: 'Community Learning',
    communityLearningDesc: 'Join thousands of learners mastering Excel skills together.',

    // Level names and descriptions
    level1Title: 'Excel Basics',
    level2Title: 'Formulas & Functions',
    level3Title: 'Data Management',
    level4Title: 'Advanced Functions',
    level5Title: 'Data Analysis',

    // Learning path
    interfaceNavigation: 'Interface & Navigation',
    sumAverageIf: 'SUM, AVERAGE, IF',
    sortingFiltering: 'Sorting & Filtering',
    vlookupIndexMatch: 'VLOOKUP, INDEX/MATCH',
    pivotTablesCharts: 'Pivot Tables & Charts',

    // Dashboard
    welcomeBackDashboard: 'Welcome Back',
    yourProgress: 'Your Progress',
    totalPoints: 'Total Points',
    completedChallenges: 'Completed Challenges',
    currentLevel: 'Current Level',
    availableLevels: 'Available Levels',
    startLevel: 'Start Level',
    continueLevel: 'Continue',
    progress: 'Progress',
    overallProgress: 'Overall Progress',
    challengesCompleted: 'challenges completed',
    challengesCompletedProgress: '%1 of %2 challenges completed',
    learningLevels: 'Learning Levels',
    locked: 'Locked',
    quickActions: 'Quick Actions',
    viewLeaderboard: 'View Leaderboard',
    practiceMode: 'Practice Mode',
    continueYourExcelLearningJourney: 'Continue your Excel learning journey',

    // Leaderboard
    leaderboardPage: 'Leaderboard',
    seeHowYouRank: 'See how you rank among other Excel learners',
    totalLearners: 'Total Learners',
    topScore: 'Top Score',
    mostChallenges: 'Most Challenges',
    topPerformers: 'Top Performers',
    noLearnersYet: 'No learners yet. Be the first to start learning!',
    joined: 'Joined',
    pts: 'pts',
    challenges: 'challenges',
    wantToClimb: 'Want to climb the leaderboard? Keep learning and complete challenges!',

    // Level page
    backToLevel: 'Back to Level',
    backToDashboard: 'Back to Dashboard',
    levelNotFound: 'Level Not Found',
    pointsEarned: 'points earned',
    challengeOf: 'Challenge %1 of %2',
    levelProgress: '%1 / %2 challenges',
    levelTitle: 'Level %1',
    pointsEarnedTotal: '%1 points earned',
    youEarnedPoints: 'You earned %1 points!',
    review: 'Review',
    startChallenge: 'Start Challenge',
    congratulationsLevelComplete: 'Congratulations! Level Complete!',
    youveCompletedAllChallenges: 'You\'ve completed all challenges and earned',
    pointsExclamation: 'points!',
    continueToNextLevel: 'Continue to Next Level',

    // Login page
    welcomeBackLogin: 'Welcome Back',
    signInToContinue: 'Sign in to continue your Excel learning journey',
    emailAddress: 'Email Address',
    password: 'Password',
    enterYourEmail: 'Enter your email',
    enterYourPassword: 'Enter your password',
    signingIn: 'Signing In...',
    signInButton: 'Sign In',
    dontHaveAccount: 'Don\'t have an account?',
    signUpHere: 'Sign up here',

    // Register page
    joinExcelMaster: 'Join Excel Master',
    createAccountAndStart: 'Create your account and start learning Excel',
    username: 'Username',
    chooseUsername: 'Choose a username',
    confirmPassword: 'Confirm Password',
    createPassword: 'Create a password',
    confirmYourPassword: 'Confirm your password',
    creatingAccount: 'Creating Account...',
    createAccount: 'Create Account',
    alreadyHaveAccount: 'Already have an account?',
    signInHere: 'Sign in here',
    passwordsDoNotMatch: 'Passwords do not match',
    passwordTooShort: 'Password must be at least 6 characters',

    // Challenge page
    challengeNotFound: 'Challenge Not Found',
    instructions: 'Instructions',
    tip: 'Tip',
    navigationTip: 'Use arrow keys to navigate between cells',
    formattingTip: 'Use the formatting toolbar to apply styles',
    excelWorkspace: 'Excel Workspace',
    reset: 'Reset',
    youEarned: 'You earned',
    nextChallenge: 'Next Challenge',
    checkingSolution: 'Checking Solution...',
    submitSolution: 'Submit Solution',

    // Level and difficulty translations
    points: 'pts',
    earned: 'Earned',
    beginner: 'Beginner',
    intermediate: 'Intermediate',
    advanced: 'Advanced',
    expert: 'Expert',

    // Common
    loading: 'Loading...',
    submit: 'Submit',
    cancel: 'Cancel',
    back: 'Back',

    // Excel Simulator
    cell: 'Cell',
    enterValue: 'Enter value',
    undo: 'Undo',
    redo: 'Redo',
    selected: 'Selected',
    ready: 'Ready',
    bold: 'Bold',
    italic: 'Italic',
    underline: 'Underline',
    backgroundColor: 'Background Color',
    noColor: 'No Color',
    yellow: 'Yellow',
    lightBlue: 'Light Blue',
    lightGreen: 'Light Green',
    pink: 'Pink',
    orange: 'Orange',

    // Level specific translations
    'level_1_title': 'Excel Basics',
    'level_1_description': 'Learn Excel fundamentals',
    'level_2_title': 'Formulas & Functions',
    'level_2_description': 'Master Excel formulas and basic functions',
    'level_3_title': 'Data Management',
    'level_3_description': 'Learn to organize and manipulate data',
    'level_4_title': 'Advanced Functions',
    'level_4_description': 'Master complex Excel functions',
    'level_5_title': 'Data Analysis',
    'level_5_description': 'Advanced data analysis and visualization',

    // Challenge specific translations
    'challenge_1-1_title': 'Interface Navigation',
    'challenge_1-1_description': 'Learn to navigate the Excel interface and understand basic components',
    'challenge_1-1_instructions': 'Click on cell A1, then use arrow keys to navigate to cell C3',
    'challenge_1-2_title': 'Data Entry and Editing',
    'challenge_1-2_description': 'Practice entering and editing data in cells',
    'challenge_1-2_instructions': 'Enter "Hello World" in cell A1, then edit it to "Hello Excel"',
    'challenge_1-3_title': 'Basic Cell Formatting',
    'challenge_1-3_description': 'Learn to format cells using bold, italic, and colors',
    'challenge_1-3_instructions': 'Make cell A1 bold and change the background color to yellow',
    'challenge_1-4_title': 'Simple Calculations',
    'challenge_1-4_description': 'Perform basic arithmetic operations',
    'challenge_1-4_instructions': 'Enter 10 in A1, 20 in B1, and create a formula in C1 to add them',
    'challenge_2-1_title': 'SUM Function',
    'challenge_2-1_description': 'Learn to use the SUM function',
    'challenge_2-1_instructions': 'Enter numbers 1-5 in cells A1:A5, then use SUM function in A6',
    'challenge_2-2_title': 'AVERAGE Function',
    'challenge_2-2_description': 'Use AVERAGE function to calculate averages',
    'challenge_2-2_instructions': 'Use AVERAGE function to calculate the average of numbers in A1:A5',
    'challenge_2-3_title': 'IF Function',
    'challenge_2-3_description': 'Learn conditional logic with IF function',
    'challenge_2-3_instructions': 'Create IF formula in B1: if A1>10, show "High", otherwise show "Low"',
    'challenge_2-4_title': 'COUNT Function',
    'challenge_2-4_description': 'Use COUNT function to count cells containing numbers',
    'challenge_2-4_instructions': 'Use COUNT function to count numeric values in range A1:A10'
  },
  zh: {
    // Navigation
    home: '首页',
    dashboard: '控制台',
    leaderboard: '排行榜',
    login: '登录',
    register: '注册',
    logout: '退出',
    language: '语言',

    // Home page
    homeTitle: 'Excel 学习平台',
    homeSubtitle: '通过互动闯关和游戏化学习掌握 Excel 技能',
    heroTitle: '通过',
    heroTitleHighlight: '互动学习掌握 Excel',
    heroDescription: '通过游戏化闯关和实践练习学习 Excel 技能。从基础公式到高级数据分析，逐步成为 Excel 专家。',
    continueLearning: '继续学习',
    startLearning: '开始学习',
    signIn: '登录',
    whyChoose: '为什么选择 Excel 大师？',
    learningJourney: '您的学习之旅',
    learningJourneyDesc: '通过 5 个综合级别的学习，从 Excel 基础到高级数据分析',

    // Features
    interactiveLearningTitle: '互动学习',
    interactiveLearningDesc: '通过我们的互动电子表格模拟器进行实践学习 Excel。',
    gamifiedChallengesTitle: '游戏化闯关',
    gamifiedChallengesDesc: '通过我们引人入胜的闯关系统逐级进步并解锁新技能。',
    trackProgressTitle: '跟踪进度',
    trackProgressDesc: '监控您的学习进程并在排行榜上与他人竞争。',
    communityLearningTitle: '社区学习',
    communityLearningDesc: '与数千名学习者一起掌握 Excel 技能。',

    // Level names and descriptions
    level1Title: 'Excel 基础',
    level2Title: '公式与函数',
    level3Title: '数据管理',
    level4Title: '高级函数',
    level5Title: '数据分析',

    // Learning path
    interfaceNavigation: '界面与导航',
    sumAverageIf: 'SUM、AVERAGE、IF',
    sortingFiltering: '排序与筛选',
    vlookupIndexMatch: 'VLOOKUP、INDEX/MATCH',
    pivotTablesCharts: '数据透视表与图表',

    // Dashboard
    welcomeBackDashboard: '欢迎回来',
    yourProgress: '您的进度',
    totalPoints: '总积分',
    completedChallenges: '已完成闯关',
    currentLevel: '当前级别',
    availableLevels: '可用级别',
    startLevel: '开始级别',
    continueLevel: '继续',
    progress: '进度',
    overallProgress: '总体进度',
    challengesCompleted: '个闯关已完成',
    challengesCompletedProgress: '已闯过 %1 关，共 %2 关',
    learningLevels: '学习级别',
    locked: '等你解锁',
    quickActions: '快速操作',
    viewLeaderboard: '查看排行榜',
    practiceMode: '练习模式',
    continueYourExcelLearningJourney: '继续您的 Excel 学习之旅',

    // Leaderboard
    leaderboardPage: '排行榜',
    seeHowYouRank: '查看您在其他 Excel 学习者中的排名',
    totalLearners: '总学习者',
    topScore: '最高分',
    mostChallenges: '最多闯关',
    topPerformers: '顶级表现者',
    noLearnersYet: '还没有学习者。成为第一个开始学习的人！',
    joined: '加入于',
    pts: '分',
    challenges: '关',
    wantToClimb: '想要攀登排行榜？继续学习并完成闯关！',

    // Level page
    backToLevel: '返回级别',
    backToDashboard: '返回控制台',
    levelNotFound: '未找到级别',
    pointsEarned: '分已获得',
    challengeOf: '第 %1 关，共 %2 关',
    levelProgress: '已闯过 %1 关，共 %2 关',
    levelTitle: '级别 %1',
    pointsEarnedTotal: '已获得 %1 积分',
    youEarnedPoints: '您获得了 %1 积分！',
    review: '复习',
    startChallenge: '开始闯关',
    congratulationsLevelComplete: '恭喜！完成此级别！🎉',
    youveCompletedAllChallenges: '您已完成此级别的所有闯关并获得了',
    pointsExclamation: '分！',
    continueToNextLevel: '继续下一级别',

    // Login page
    welcomeBackLogin: '欢迎回来',
    signInToContinue: '登录以继续您的 Excel 学习之旅',
    emailAddress: '邮箱地址',
    password: '密码',
    enterYourEmail: '输入您的邮箱',
    enterYourPassword: '输入您的密码',
    signingIn: '登录中...',
    signInButton: '登录',
    dontHaveAccount: '还没有账户？',
    signUpHere: '在这里注册',

    // Register page
    joinExcelMaster: '加入 Excel 大师',
    createAccountAndStart: '创建您的账户并开始学习 Excel',
    username: '用户名',
    chooseUsername: '选择一个用户名',
    confirmPassword: '确认密码',
    createPassword: '创建密码',
    confirmYourPassword: '确认您的密码',
    creatingAccount: '创建账户中...',
    createAccount: '创建账户',
    alreadyHaveAccount: '已有账户？',
    signInHere: '在这里登录',
    passwordsDoNotMatch: '密码不匹配',
    passwordTooShort: '密码必须至少6个字符',

    // Challenge page
    challengeNotFound: '未找到闯关',
    instructions: '操作说明',
    tip: '提示',
    navigationTip: '使用方向键在单元格之间导航',
    formattingTip: '使用格式工具栏应用样式',
    excelWorkspace: 'Excel 工作区',
    reset: '重置',
    youEarned: '您获得了',
    nextChallenge: '下一个关',
    checkingSolution: '检查解决方案中...',
    submitSolution: '提交解决方案',

    // Level and difficulty translations
    points: '分',
    earned: '已获积分',
    beginner: '初级',
    intermediate: '中级',
    advanced: '高级',
    expert: '专家',

    // Common
    loading: '加载中...',
    submit: '提交',
    cancel: '取消',
    back: '返回',

    // Excel Simulator
    cell: '单元格',
    enterValue: '输入值',
    undo: '撤销',
    redo: '重做',
    selected: '已选择',
    ready: '就绪',
    bold: '粗体',
    italic: '斜体',
    underline: '下划线',
    backgroundColor: '背景颜色',
    noColor: '无颜色',
    yellow: '黄色',
    lightBlue: '浅蓝色',
    lightGreen: '浅绿色',
    pink: '粉色',
    orange: '橙色',

    // Level specific translations
    'level_1_title': 'Excel 基础',
    'level_1_description': '学习 Excel 的基础知识',
    'level_2_title': '公式与函数',
    'level_2_description': '掌握 Excel 公式和基本函数',
    'level_3_title': '数据管理',
    'level_3_description': '学习组织和操作数据',
    'level_4_title': '高级函数',
    'level_4_description': '掌握复杂的 Excel 函数',
    'level_5_title': '数据分析',
    'level_5_description': '高级数据分析和可视化',

    // Challenge specific translations
    'challenge_1-1_title': '界面导航',
    'challenge_1-1_description': '学习导航 Excel 界面并了解基本组件',
    'challenge_1-1_instructions': '点击单元格 A1，然后使用方向键导航到单元格 C3',
    'challenge_1-2_title': '输入和编辑数据',
    'challenge_1-2_description': '练习在单元格中输入和编辑数据',
    'challenge_1-2_instructions': '在单元格 A1 中输入 "Hello World"，然后编辑为 "Hello Excel"',
    'challenge_1-3_title': '基本格式设置',
    'challenge_1-3_description': '学习使用粗体、斜体和颜色格式化单元格',
    'challenge_1-3_instructions': '将单元格 A1 设为粗体并将背景颜色改为黄色',
    'challenge_1-4_title': '简单计算',
    'challenge_1-4_description': '执行基本算术运算',
    'challenge_1-4_instructions': '在 A1 中输入 10，在 B1 中输入 20，在 C1 中创建公式将它们相加',
    'challenge_2-1_title': 'SUM 函数',
    'challenge_2-1_description': '学习使用 SUM 函数',
    'challenge_2-1_instructions': '在单元格 A1:A5 中输入数字 1-5，然后在 A6 中使用 SUM 函数',
    'challenge_2-2_title': 'AVERAGE 函数',
    'challenge_2-2_description': '使用 AVERAGE 函数计算平均值',
    'challenge_2-2_instructions': '使用 AVERAGE 函数计算 A1:A5 中数字的平均值',
    'challenge_2-3_title': 'IF 函数',
    'challenge_2-3_description': '学习使用 IF 函数的条件逻辑',
    'challenge_2-3_instructions': '在 B1 中创建 IF 公式：如果 A1>10，显示"高"，否则显示"低"',
    'challenge_2-4_title': 'COUNT 函数',
    'challenge_2-4_description': '使用 COUNT 函数计算包含数字的单元格',
    'challenge_2-4_instructions': '使用 COUNT 函数计算范围 A1:A10 中的数值个数'
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    return localStorage.getItem('language') || 'en';
  });

  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  const t = (key, params = []) => {
    let translation = translations[language][key] || key;

    // Replace parameters in the format %1, %2, etc.
    if (typeof translation === 'string' && params && params.length > 0) {
      params.forEach((value, index) => {
        // Support %1, %2 format
        translation = translation.replace(new RegExp(`%${index + 1}`, 'g'), value);
      });
    }

    return translation;
  };

  const toggleLanguage = () => {
    setLanguage(prev => prev === 'en' ? 'zh' : 'en');
  };

  const value = {
    language,
    setLanguage,
    toggleLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
