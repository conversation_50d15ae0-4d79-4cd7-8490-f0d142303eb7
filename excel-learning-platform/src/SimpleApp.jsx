import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { LanguageProvider, useLanguage } from './contexts/LanguageContext';

function HomePage() {
  const { t, language, toggleLanguage } = useLanguage();

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={toggleLanguage}
          style={{ padding: '10px', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '5px' }}
        >
          {language === 'en' ? '中文' : 'English'}
        </button>
      </div>
      <h1 style={{ color: 'green' }}>{t('homeTitle')}</h1>
      <p>{t('homeSubtitle')}</p>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0', borderRadius: '5px' }}>
        <h2>{t('whyChoose')}</h2>
        <ul>
          <li>{t('interactiveLearningTitle')}</li>
          <li>{t('gamifiedChallengesTitle')}</li>
          <li>{t('trackProgressTitle')}</li>
        </ul>
      </div>
    </div>
  );
}

function SimpleApp() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <main className="container mx-auto px-4 py-8">
          <Routes>
            <Route path="/" element={<HomePage />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default SimpleApp;
