import React, { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'

// Global error handler for Fortune Sheet issues
window.addEventListener('error', (event) => {
  if (event.error && event.error.message) {
    const message = event.error.message;
    // Suppress known Fortune Sheet errors
    if (message.includes('objectInnerText') ||
        message.includes('NaN') && message.includes('width') ||
        message.includes('fortune-sheet') ||
        message.includes('luckysheet')) {
      console.warn('Fortune Sheet error suppressed:', message);
      event.preventDefault();
      return false;
    }
  }
});

// Global unhandled rejection handler
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message) {
    const message = event.reason.message;
    if (message.includes('fortune-sheet') ||
        message.includes('objectInnerText') ||
        message.includes('luckysheet')) {
      console.warn('Fortune Sheet promise rejection suppressed:', message);
      event.preventDefault();
      return false;
    }
  }
});

// Suppress React development warnings about NaN values
const originalConsoleWarn = console.warn;
console.warn = (...args) => {
  const message = args.join(' ');
  if (message.includes('NaN') && message.includes('css style property')) {
    // Suppress NaN CSS warnings from Fortune Sheet
    return;
  }
  originalConsoleWarn.apply(console, args);
};

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
