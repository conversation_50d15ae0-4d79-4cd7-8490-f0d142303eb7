import React, { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'

// Global error handler for Fortune Sheet issues
window.addEventListener('error', (event) => {
  if (event.error && event.error.message) {
    const message = event.error.message;
    // Suppress known Fortune Sheet errors
    if (message.includes('objectInnerText') ||
        message.includes('NaN') && message.includes('width') ||
        message.includes('fortune-sheet')) {
      console.warn('Fortune Sheet error suppressed:', message);
      event.preventDefault();
      return false;
    }
  }
});

// Global unhandled rejection handler
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message) {
    const message = event.reason.message;
    if (message.includes('fortune-sheet') || message.includes('objectInnerText')) {
      console.warn('Fortune Sheet promise rejection suppressed:', message);
      event.preventDefault();
      return false;
    }
  }
});

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
