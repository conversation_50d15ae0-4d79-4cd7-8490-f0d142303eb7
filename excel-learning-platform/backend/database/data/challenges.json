[{"id": "1-1", "levelId": "1", "title": "Navigate the Interface", "description": "Learn to navigate Excel's interface and understand basic components", "instructions": "Click on cell A1, then navigate to cell C3 using arrow keys", "points": 10, "orderIndex": 1, "solutionData": {"targetCell": "C3"}}, {"id": "1-2", "levelId": "1", "title": "Enter and Edit Data", "description": "Practice entering and editing data in cells", "instructions": "Enter \"Hello World\" in cell A1, then edit it to \"Hello Excel\"", "points": 15, "orderIndex": 2, "solutionData": {"cellA1": "Hello Excel"}}, {"id": "1-3", "levelId": "1", "title": "Basic Cell Formatting", "description": "Learn to format cells with bold, italic, and colors", "instructions": "Make cell A1 bold and change the background color to yellow", "points": 20, "orderIndex": 3, "solutionData": {"cellA1": {"bold": true, "backgroundColor": "yellow"}}}, {"id": "1-4", "levelId": "1", "title": "Simple Calculations", "description": "Perform basic arithmetic operations", "instructions": "In cell A1 enter 10, in B1 enter 20, in C1 create a formula to add them", "points": 25, "orderIndex": 4, "solutionData": {"cellA1": 10, "cellB1": 20, "cellC1": "=A1+B1"}}]