[{"id": "1-1", "levelId": "1", "title": "Navigate the Interface", "description": "Learn to navigate Excel's interface and understand basic components", "instructions": "Click on cell A1, then navigate to cell C3 using arrow keys", "points": 10, "orderIndex": 1, "solutionData": {"targetCell": "C3"}}, {"id": "1-2", "levelId": "1", "title": "Enter and Edit Data", "description": "Practice entering and editing data in cells", "instructions": "Enter \"Hello World\" in cell A1, then edit it to \"Hello Excel\"", "points": 15, "orderIndex": 2, "solutionData": {"cellA1": "Hello Excel"}}, {"id": "1-3", "levelId": "1", "title": "Basic Cell Formatting", "description": "Learn to format cells with bold, italic, and colors", "instructions": "Make cell A1 bold and change the background color to yellow", "points": 20, "orderIndex": 3, "solutionData": {"cellA1": {"bold": true, "backgroundColor": "yellow"}}}, {"id": "1-4", "levelId": "1", "title": "Simple Calculations", "description": "Perform basic arithmetic operations", "instructions": "In cell A1 enter 10, in B1 enter 20, in C1 create a formula to add them", "points": 25, "orderIndex": 4, "solutionData": {"cellA1": 10, "cellB1": 20, "cellC1": "=A1+B1"}}, {"id": "2-1", "levelId": "2", "title": "SUM Function", "description": "Learn to use the SUM function", "instructions": "Enter numbers 1-5 in cells A1:A5, then use SUM function in A6", "points": 20, "orderIndex": 1, "solutionData": {"cellA1": 1, "cellA2": 2, "cellA3": 3, "cellA4": 4, "cellA5": 5, "cellA6": "=SUM(A1:A5)"}}, {"id": "2-2", "levelId": "2", "title": "AVERAGE Function", "description": "Calculate averages using the AVERAGE function", "instructions": "Use the AVERAGE function to find the average of numbers in A1:A5", "points": 20, "orderIndex": 2, "solutionData": {"cellB6": "=AVERAGE(A1:A5)"}}, {"id": "2-3", "levelId": "2", "title": "IF Function", "description": "Learn conditional logic with IF function", "instructions": "In B1, create an IF formula: if A1>10, show \"High\", otherwise \"Low\"", "points": 30, "orderIndex": 3, "solutionData": {"cellB1": "=IF(A1>10,\"High\",\"Low\")"}}, {"id": "2-4", "levelId": "2", "title": "COUNT Function", "description": "Count cells with numbers using COUNT function", "instructions": "Use COUNT function to count numeric values in range A1:A10", "points": 25, "orderIndex": 4, "solutionData": {"cellB10": "=COUNT(A1:A10)"}}]