[{"id": "1-1", "levelId": "1", "title": "Navigate the Interface", "description": "Learn to navigate Excel's interface and understand basic components", "instructions": "步骤1: 点击单元格A1（第一行第一列）\n步骤2: 使用键盘方向键向右移动2次到达C列\n步骤3: 使用向下方向键移动2次到达第3行\n最终目标: 选中单元格C3", "instructionsEn": "Step 1: Click on cell A1 (first row, first column)\nStep 2: Use keyboard arrow keys to move right 2 times to reach column C\nStep 3: Use down arrow key to move down 2 times to reach row 3\nFinal goal: Select cell C3", "points": 10, "orderIndex": 1, "solutionData": {"targetCell": "C3"}}, {"id": "1-2", "levelId": "1", "title": "Enter and Edit Data", "description": "Practice entering and editing data in cells", "instructions": "步骤1: 点击单元格A1\n步骤2: 输入文本 \"Hello World\"（不包括引号）\n步骤3: 按回车键确认输入\n步骤4: 双击A1单元格进入编辑模式\n步骤5: 将文本修改为 \"Hello Excel\"\n步骤6: 按回车键完成编辑", "instructionsEn": "Step 1: Click on cell A1\nStep 2: Type \"Hello World\" (without quotes)\nStep 3: Press Enter to confirm input\nStep 4: Double-click cell A1 to enter edit mode\nStep 5: Change the text to \"Hello Excel\"\nStep 6: Press Enter to complete editing", "points": 15, "orderIndex": 2, "solutionData": {"cellA1": "Hello Excel"}}, {"id": "1-3", "levelId": "1", "title": "Basic Cell Formatting", "description": "Learn to format cells with bold, italic, and colors", "instructions": "步骤1: 点击选中单元格A1\n步骤2: 在格式工具栏中点击粗体按钮（B图标）\n步骤3: 在背景颜色下拉菜单中选择黄色\n提示: 格式工具栏位于Excel工作区上方", "instructionsEn": "Step 1: Click to select cell A1\nStep 2: Click the Bold button (B icon) in the formatting toolbar\nStep 3: Select yellow from the background color dropdown menu\nTip: The formatting toolbar is located above the Excel workspace", "points": 20, "orderIndex": 3, "solutionData": {"cellA1": {"bold": true, "backgroundColor": "yellow"}}}, {"id": "1-4", "levelId": "1", "title": "Simple Calculations", "description": "Perform basic arithmetic operations", "instructions": "步骤1: 点击单元格A1，输入数字 10，按回车确认\n步骤2: 点击单元格B1，输入数字 20，按回车确认\n步骤3: 点击单元格C1\n步骤4: 输入公式 =A1+B1（必须以等号开始）\n步骤5: 按回车键，C1应该显示计算结果 30\n提示: Excel公式总是以等号(=)开始", "instructionsEn": "Step 1: Click cell A1, enter number 10, press Enter to confirm\nStep 2: Click cell B1, enter number 20, press Enter to confirm\nStep 3: Click cell C1\nStep 4: Enter formula =A1+B1 (must start with equals sign)\nStep 5: Press Enter, C1 should display the result 30\nTip: Excel formulas always start with an equals sign (=)", "points": 25, "orderIndex": 4, "solutionData": {"cellA1": 10, "cellB1": 20, "cellC1": "=A1+B1"}}, {"id": "2-1", "levelId": "2", "title": "SUM Function", "description": "Learn to use the SUM function", "instructions": "Enter numbers 1-5 in cells A1:A5, then use SUM function in A6", "points": 20, "orderIndex": 1, "solutionData": {"cellA1": 1, "cellA2": 2, "cellA3": 3, "cellA4": 4, "cellA5": 5, "cellA6": "=SUM(A1:A5)"}}, {"id": "2-2", "levelId": "2", "title": "AVERAGE Function", "description": "Calculate averages using the AVERAGE function", "instructions": "Use the AVERAGE function to find the average of numbers in A1:A5", "points": 20, "orderIndex": 2, "solutionData": {"cellB6": "=AVERAGE(A1:A5)"}}, {"id": "2-3", "levelId": "2", "title": "IF Function", "description": "Learn conditional logic with IF function", "instructions": "In B1, create an IF formula: if A1>10, show \"High\", otherwise \"Low\"", "points": 30, "orderIndex": 3, "solutionData": {"cellB1": "=IF(A1>10,\"High\",\"Low\")"}}, {"id": "2-4", "levelId": "2", "title": "COUNT Function", "description": "Count cells with numbers using COUNT function", "instructions": "Use COUNT function to count numeric values in range A1:A10", "points": 25, "orderIndex": 4, "solutionData": {"cellB10": "=COUNT(A1:A10)"}}, {"id": "3-1", "levelId": "3", "title": "Data Sorting", "description": "Learn to sort data in Excel", "instructions": "Sort the data in A1:C10 by column B in ascending order", "points": 20, "orderIndex": 1, "solutionData": {"sortedBy": "columnB", "sortOrder": "ascending"}}, {"id": "3-2", "levelId": "3", "title": "Data Filtering", "description": "Apply filters to data", "instructions": "Apply a filter to show only values greater than 100 in column C", "points": 25, "orderIndex": 2, "solutionData": {"filterColumn": "C", "filterCriteria": ">100"}}, {"id": "3-3", "levelId": "3", "title": "Data Validation", "description": "Set up data validation rules", "instructions": "Create a dropdown list in cell A1 with options: Red, Green, Blue", "points": 30, "orderIndex": 3, "solutionData": {"validationType": "list", "validationList": ["Red", "Green", "Blue"]}}, {"id": "3-4", "levelId": "3", "title": "Find and Replace", "description": "Use Find and Replace functionality", "instructions": "Replace all instances of 'old' with 'new' in the worksheet", "points": 20, "orderIndex": 4, "solutionData": {"findText": "old", "replaceText": "new"}}, {"id": "4-1", "levelId": "4", "title": "VLOOKUP Function", "description": "Master the VLOOKUP function", "instructions": "Use VLOOKUP to find the price for product ID in cell A1", "points": 35, "orderIndex": 1, "solutionData": {"cellB1": "=VLOOKUP(A1,D:F,3,FALSE)"}}, {"id": "4-2", "levelId": "4", "title": "INDEX MATCH", "description": "Learn INDEX and MATCH functions", "instructions": "Use INDEX and MATCH to lookup values more flexibly than VLOOKUP", "points": 40, "orderIndex": 2, "solutionData": {"cellB1": "=INDEX(F:F,MATCH(A1,D:D,0))"}}, {"id": "4-3", "levelId": "4", "title": "CONCATENATE Function", "description": "Combine text from multiple cells", "instructions": "Combine first name (A1) and last name (B1) with a space in between", "points": 25, "orderIndex": 3, "solutionData": {"cellC1": "=CONCATENATE(A1,\" \",B1)"}}, {"id": "4-4", "levelId": "4", "title": "SUMIF Function", "description": "Sum values based on criteria", "instructions": "Sum all values in column B where column A equals 'Sales'", "points": 30, "orderIndex": 4, "solutionData": {"cellC1": "=SUMIF(A:A,\"Sales\",B:B)"}}, {"id": "5-1", "levelId": "5", "title": "Pivot Tables", "description": "Create and customize pivot tables", "instructions": "Create a pivot table to summarize sales data by region and product", "points": 45, "orderIndex": 1, "solutionData": {"pivotTable": {"rows": ["Region"], "columns": ["Product"], "values": ["Sales"]}}}, {"id": "5-2", "levelId": "5", "title": "Charts and Graphs", "description": "Create professional charts", "instructions": "Create a column chart showing monthly sales data", "points": 35, "orderIndex": 2, "solutionData": {"chartType": "column", "dataRange": "A1:B12"}}, {"id": "5-3", "levelId": "5", "title": "Conditional Formatting", "description": "Apply conditional formatting rules", "instructions": "Highlight cells in range A1:A10 that are greater than 50 in green", "points": 30, "orderIndex": 3, "solutionData": {"range": "A1:A10", "condition": ">50", "format": "green"}}, {"id": "5-4", "levelId": "5", "title": "Data Analysis Tools", "description": "Use Excel's data analysis features", "instructions": "Create a frequency distribution of the data in column A", "points": 40, "orderIndex": 4, "solutionData": {"analysisType": "frequency", "dataRange": "A:A"}}]